package influxdb

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	"github.com/influxdata/influxdb-client-go/v2/api/write"
	"github.com/influxdata/influxdb-client-go/v2/domain"
)

// Client represents an InfluxDB client
type Client struct {
	client   influxdb2.Client
	writeAPI api.WriteAPI
	org      string
}

// GPSRecord represents a GPS record from the JSON data
type GPSRecord struct {
	Lat            string  `json:"lat"`
	Lon            string  `json:"lon"`
	LocalTime      string  `json:"local_time"`
	BatteryPercent float64 `json:"battery_percent"`
	SpeedKmh       float64 `json:"speed_kmh"`
}

// DocData represents the structure of data from Minio files
type DocData struct {
	Imei string      `json:"imei"`
	Data []GPSRecord `json:"data"`
}

// Payload represents the payload structure for InfluxDB operations
type Payload struct {
	Ident    string `json:"ident"`
	Psm      string `json:"psm"`
	Estate   string `json:"estate"`
	Division string `json:"division"`
	Nik      string `json:"nik"`
	Type     string `json:"type"`
}

// NewInfluxDBClient creates a new InfluxDB client
func NewInfluxDBClient(url, token, org string) (*Client, error) {
	if url == "" {
		return nil, fmt.Errorf("InfluxDB URL is required")
	}
	if token == "" {
		return nil, fmt.Errorf("InfluxDB token is required")
	}
	if org == "" {
		return nil, fmt.Errorf("InfluxDB organization is required")
	}

	// Create InfluxDB client
	client := influxdb2.NewClient(url, token)

	// Create write API
	writeAPI := client.WriteAPI(org, "")

	return &Client{
		client:   client,
		writeAPI: writeAPI,
		org:      org,
	}, nil
}

// Close closes the InfluxDB client
func (c *Client) Close() {
	if c.client != nil {
		c.client.Close()
	}
}

// WritePoints writes data points to InfluxDB
func (c *Client) WritePoints(bucket string, points []*write.Point) error {
	if len(points) == 0 {
		return fmt.Errorf("no points to write")
	}

	// Set the bucket for write API
	c.writeAPI = c.client.WriteAPI(c.org, bucket)

	// Write points
	for _, point := range points {
		c.writeAPI.WritePoint(point)
	}

	// Force flush
	c.writeAPI.Flush()

	// Check for errors
	errorsCh := c.writeAPI.Errors()
	select {
	case err := <-errorsCh:
		return fmt.Errorf("failed to write to InfluxDB: %w", err)
	default:
		// No errors
	}

	return nil
}

// ConvertToInfluxPoints converts GPS records to InfluxDB points
func (c *Client) ConvertToInfluxPoints(docData DocData, payload Payload) ([]*write.Point, error) {
	var points []*write.Point

	for _, rec := range docData.Data {
		// Skip records with missing required fields
		if rec.Lat == "" || rec.Lon == "" || rec.LocalTime == "" {
			continue
		}

		// Parse latitude and longitude
		lat, err := parseFloat(rec.Lat)
		if err != nil {
			continue // Skip invalid lat/lon
		}

		lon, err := parseFloat(rec.Lon)
		if err != nil {
			continue // Skip invalid lat/lon
		}

		// Parse timestamp
		timestamp, err := time.Parse("2006-01-02 15:04:05", rec.LocalTime)
		if err != nil {
			continue // Skip invalid timestamp
		}

		// Create tags
		tags := map[string]string{
			"date":     strings.Split(rec.LocalTime, " ")[0],
			"psm":      payload.Psm,
			"estate":   payload.Estate,
			"division": payload.Division,
			"nik":      payload.Nik,
		}

		// Create fields
		fields := map[string]interface{}{
			"latitude":        lat,
			"longitude":       lon,
			"speed":           rec.SpeedKmh,
			"battery_percent": rec.BatteryPercent,
		}

		// Create point
		point := write.NewPoint("sensor_measurement", tags, fields, timestamp)
		points = append(points, point)
	}

	return points, nil
}

// WriteGPSData writes GPS data to InfluxDB
func (c *Client) WriteGPSData(docData DocData, payload Payload) error {
	// Build bucket name
	bucket := fmt.Sprintf("caveo-%s", payload.Ident)

	// Convert to InfluxDB points
	points, err := c.ConvertToInfluxPoints(docData, payload)
	if err != nil {
		return fmt.Errorf("failed to convert to InfluxDB points: %w", err)
	}

	if len(points) == 0 {
		return fmt.Errorf("no valid data points to write")
	}

	// Write points to InfluxDB
	err = c.WritePoints(bucket, points)
	if err != nil {
		return fmt.Errorf("failed to write points to InfluxDB: %w", err)
	}

	return nil
}

// parseFloat parses a string to float64
func parseFloat(s string) (float64, error) {
	return strconv.ParseFloat(strings.TrimSpace(s), 64)
}

// HealthCheck checks if InfluxDB is accessible
func (c *Client) HealthCheck(ctx context.Context) error {
	health, err := c.client.Health(ctx)
	if err != nil {
		return fmt.Errorf("InfluxDB health check failed: %w", err)
	}

	if health.Status != "pass" {
		return fmt.Errorf("InfluxDB health check failed: status is %s", health.Status)
	}

	return nil
}

// GetBuckets returns a list of available buckets
func (c *Client) GetBuckets(ctx context.Context) ([]string, error) {
	bucketsAPI := c.client.BucketsAPI()
	buckets, err := bucketsAPI.GetBuckets(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get buckets: %w", err)
	}

	var bucketNames []string
	for _, bucket := range *buckets {
		bucketNames = append(bucketNames, bucket.Name)
	}

	return bucketNames, nil
}

// CreateBucket creates a new bucket if it doesn't exist
func (c *Client) CreateBucket(ctx context.Context, bucketName string) error {
	bucketsAPI := c.client.BucketsAPI()

	// Check if bucket already exists
	bucket, err := bucketsAPI.FindBucketByName(ctx, bucketName)
	if err == nil && bucket != nil {
		// Bucket already exists
		return nil
	}

	// Get organization by name
	orgAPI := c.client.OrganizationsAPI()
	org, err := orgAPI.FindOrganizationByName(ctx, c.org)
	if err != nil {
		return fmt.Errorf("failed to find organization %s: %w", c.org, err)
	}

	if org == nil {
		return fmt.Errorf("organization %s not found", c.org)
	}

	// Create bucket model
	bucketModel := &domain.Bucket{
		Name:  bucketName,
		OrgID: org.Id,
	}

	// Create new bucket
	_, err = bucketsAPI.CreateBucket(ctx, bucketModel)
	if err != nil {
		return fmt.Errorf("failed to create bucket %s: %w", bucketName, err)
	}

	return nil
}

// CountRecordsByIMEIAndDateRange counts records in InfluxDB by IMEI and date range
func (c *Client) CountRecordsByIMEIAndDateRange(ctx context.Context, imei string, startDate, endDate time.Time) (int, error) {
	// Build bucket name using the IMEI
	bucket := fmt.Sprintf("caveo-%s", imei)

	// Create query API
	queryAPI := c.client.QueryAPI(c.org)

	// Build Flux query to count records
	// Since the bucket is already specific to the IMEI, we don't need to filter by IMEI
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> count()
	`, bucket, startDate.Format(time.RFC3339), endDate.Format(time.RFC3339))

	// Execute query
	result, err := queryAPI.Query(ctx, query)
	if err != nil {
		// Check if the error is due to bucket not found
		if strings.Contains(err.Error(), "could not find bucket") {
			// Return 0 count if bucket doesn't exist
			return 0, nil
		}
		return 0, fmt.Errorf("failed to execute query: %w", err)
	}
	defer result.Close()

	// Parse result
	count := 0
	for result.Next() {
		record := result.Record()
		if record.Value() != nil {
			if val, ok := record.Value().(int64); ok {
				count = int(val)
			}
		}
	}

	// Check for query errors
	if result.Err() != nil {
		return 0, fmt.Errorf("query execution error: %w", result.Err())
	}

	return count, nil
}

// TrackingPoint represents a tracking point with longitude and latitude
type TrackingPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Latitude  float64   `json:"latitude"`
	Longitude float64   `json:"longitude"`
}

// GetTrackingDataByIMEIAndDate retrieves tracking data (longitude, latitude) from InfluxDB by IMEI and date
func (c *Client) GetTrackingDataByIMEIAndDate(ctx context.Context, imei string, date time.Time) ([]TrackingPoint, error) {
	// Build bucket name using the IMEI
	bucket := fmt.Sprintf("caveo-%s", imei)

	// Create query API
	queryAPI := c.client.QueryAPI(c.org)

	// Set start and end times for the entire day
	startTime := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endTime := startTime.Add(24 * time.Hour)

	// Build Flux query to get tracking data
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: %s, stop: %s)
		|> filter(fn: (r) => r["_measurement"] == "sensor_measurement")
		|> filter(fn: (r) => r["_field"] == "latitude" or r["_field"] == "longitude")
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
		|> sort(columns: ["_time"])
	`, bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339))

	// Execute query
	result, err := queryAPI.Query(ctx, query)
	if err != nil {
		// Check if the error is due to bucket not found
		if strings.Contains(err.Error(), "could not find bucket") {
			// Return empty slice if bucket doesn't exist
			return []TrackingPoint{}, nil
		}
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer result.Close()

	// Parse result
	var trackingPoints []TrackingPoint
	for result.Next() {
		record := result.Record()

		// Extract timestamp
		timestamp := record.Time()

		// Extract latitude and longitude
		var latitude, longitude float64
		var hasLatitude, hasLongitude bool

		// Get latitude value
		if latValue := record.ValueByKey("latitude"); latValue != nil {
			if lat, ok := latValue.(float64); ok {
				latitude = lat
				hasLatitude = true
			}
		}

		// Get longitude value
		if lonValue := record.ValueByKey("longitude"); lonValue != nil {
			if lon, ok := lonValue.(float64); ok {
				longitude = lon
				hasLongitude = true
			}
		}

		// Only add point if both latitude and longitude are available
		if hasLatitude && hasLongitude {
			trackingPoint := TrackingPoint{
				Timestamp: timestamp,
				Latitude:  latitude,
				Longitude: longitude,
			}
			trackingPoints = append(trackingPoints, trackingPoint)
		}
	}

	// Check for query errors
	if result.Err() != nil {
		return nil, fmt.Errorf("query execution error: %w", result.Err())
	}

	return trackingPoints, nil
}
