package redislogformat

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"
)

// WorkerLogFormat represents the log format for a worker type
type WorkerLogFormat map[string]string

// Config represents the Redis log format configuration
type Config struct {
	formats map[string]WorkerLogFormat
}

// New creates a new Redis log format configuration
func New(configPath string) (*Config, error) {
	// Read the configuration file
	file, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read Redis log format config file: %w", err)
	}

	// Parse the configuration
	var formats map[string]WorkerLogFormat
	if err := json.Unmarshal(file, &formats); err != nil {
		return nil, fmt.Errorf("failed to parse Redis log format config file: %w", err)
	}

	return &Config{
		formats: formats,
	}, nil
}

// GetKeyFormat returns the key format for a worker type and status
func (c *Config) GetKeyFormat(workerType, status string) string {
	// Check if the worker type exists in the configuration
	if workerFormat, ok := c.formats[workerType]; ok {
		// Check if the status exists for the worker type
		if format, ok := workerFormat[status]; ok {
			return format
		}
	}

	// If not found, use the default format
	if defaultFormat, ok := c.formats["default"]; ok {
		if format, ok := defaultFormat[status]; ok {
			return format
		}
	}

	// If no default format is found, return a fallback format
	return "logs:%s:YYYY-MM-DD:%s:%s"
}

// FormatKey formats a key for a worker type, status, and ID
func (c *Config) FormatKey(workerType, status, id string) string {
	// Get the key format
	format := c.GetKeyFormat(workerType, status)

	// Replace date placeholder with current date
	dateStr := time.Now().Format("2006-01-02")
	format = strings.Replace(format, "YYYY-MM-DD", dateStr, -1)

	// If the format contains two %s placeholders, it's for the default format
	if strings.Count(format, "%s") == 2 {
		return fmt.Sprintf(format, workerType, id)
	}

	// Otherwise, it's for a specific worker type
	return fmt.Sprintf(format, id)
}

// FormatKeyWithDate formats a key for a worker type, status, ID, and custom date
func (c *Config) FormatKeyWithDate(workerType, status, id, dateStr string) string {
	// Get the key format
	format := c.GetKeyFormat(workerType, status)

	// Replace date placeholder with provided date
	format = strings.Replace(format, "YYYY-MM-DD", dateStr, -1)

	// If the format contains two %s placeholders, it's for the default format
	if strings.Count(format, "%s") == 2 {
		return fmt.Sprintf(format, workerType, id)
	}

	// Otherwise, it's for a specific worker type
	return fmt.Sprintf(format, id)
}

// GetStatusCodeCategory returns the status code category for HTTP status codes
func GetStatusCodeCategory(statusCode int) string {
	if statusCode >= 200 && statusCode < 300 {
		return "success"
	} else if statusCode >= 300 && statusCode < 400 {
		return "redirect"
	} else if statusCode >= 400 && statusCode < 500 {
		return "client-error"
	} else if statusCode >= 500 && statusCode < 600 {
		return "server-error"
	}
	return "unknown"
}
