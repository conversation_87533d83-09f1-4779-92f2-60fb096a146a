package logwriter

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/user/workers/pkg/redislogformat"
)

// LogEntry represents a log entry in the JSON format
type LogEntry struct {
	Timestamp  string                 `json:"timestamp"`
	WorkerType string                 `json:"worker_type"`
	Status     string                 `json:"status"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// LogWriter is a thread-safe utility for writing logs to Redis with fallback to file-based storage
type LogWriter struct {
	logsDir      string
	mu           sync.Mutex
	redisClient  *redis.Client
	useRedis     bool
	ctx          context.Context
	logFormatCfg *redislogformat.Config
	useLogFormat bool
}

// LogWriterConfig holds configuration for the LogWriter
type LogWriterConfig struct {
	LogsDir           string
	RedisHost         string
	RedisPort         string
	RedisPassword     string
	RedisDB           int
	LogFormatFilePath string
}

// New creates a new LogWriter instance
func New(config LogWriterConfig) (*LogWriter, error) {
	// Create logs directory if it doesn't exist (for fallback)
	if err := os.MkdirAll(config.LogsDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create logs directory: %w", err)
	}

	// Create LogWriter instance
	lw := &LogWriter{
		logsDir:      config.LogsDir,
		ctx:          context.Background(),
		useRedis:     false,
		useLogFormat: false,
	}

	// Initialize Redis log format configuration if file path is provided
	if config.LogFormatFilePath != "" {
		logFormatCfg, err := redislogformat.New(config.LogFormatFilePath)
		if err != nil {
			fmt.Printf("Warning: Failed to load Redis log format configuration: %v. Using default format.\n", err)
		} else {
			lw.logFormatCfg = logFormatCfg
			lw.useLogFormat = true
			fmt.Println("Redis log format configuration loaded successfully.")
		}
	}

	// Initialize Redis client if host is provided
	if config.RedisHost != "" {
		redisAddr := fmt.Sprintf("%s:%s", config.RedisHost, config.RedisPort)
		fmt.Printf("Connecting to Redis at %s with password: %s\n", redisAddr, maskPassword(config.RedisPassword))

		// Create Redis client with options
		lw.redisClient = redis.NewClient(&redis.Options{
			Addr:         redisAddr,
			Password:     config.RedisPassword,
			DB:           config.RedisDB,
			DialTimeout:  5 * time.Second,
			ReadTimeout:  3 * time.Second,
			WriteTimeout: 3 * time.Second,
			PoolSize:     10,
			MinIdleConns: 5,
		})

		// Test Redis connection
		if err := lw.redisClient.Ping(lw.ctx).Err(); err != nil {
			// Log the error but continue with file-based logging as fallback
			fmt.Printf("Warning: Failed to connect to Redis at %s: %v. Falling back to file-based logging.\n", redisAddr, err)
		} else {
			fmt.Printf("Successfully connected to Redis at %s\n", redisAddr)

			// Test Redis JSON module
			testKey := "test_json_module"
			testValue := map[string]interface{}{"test": "value"}
			testJSON, _ := json.Marshal(testValue)

			if err := lw.redisClient.Do(lw.ctx, "JSON.SET", testKey, ".", string(testJSON)).Err(); err != nil {
				fmt.Printf("Warning: Redis JSON module test failed: %v. Falling back to file-based logging.\n", err)
			} else {
				// Clean up test key
				lw.redisClient.Del(lw.ctx, testKey)
				fmt.Println("Redis JSON module is working correctly.")
				lw.useRedis = true
			}
		}
	} else {
		fmt.Println("Warning: Redis host not provided. Using file-based logging only.")
	}

	return lw, nil
}

// maskPassword returns a masked version of the password for logging
func maskPassword(password string) string {
	if len(password) <= 4 {
		return "****"
	}
	return password[:2] + "****" + password[len(password)-2:]
}

// WriteLog writes a log entry to Redis with fallback to file-based storage
func (lw *LogWriter) WriteLog(key, workerType, status string, metadata map[string]interface{}) error {
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	timestamp := time.Now().UTC()
	timestampStr := timestamp.Format(time.RFC3339)

	entry := LogEntry{
		Timestamp:  timestampStr,
		WorkerType: workerType,
		Status:     status,
		Metadata:   metadata,
	}

	entryJSON, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal log entry: %w", err)
	}

	lw.mu.Lock()
	defer lw.mu.Unlock()

	if !lw.useRedis && lw.redisClient != nil {
		fmt.Println("Attempting to reconnect to Redis...")
		if err := lw.redisClient.Ping(lw.ctx).Err(); err == nil {
			fmt.Println("Successfully reconnected to Redis!")
			lw.useRedis = true
		}
	}

	if lw.useRedis && lw.redisClient != nil {
		err := lw.redisClient.Do(lw.ctx, "JSON.SET", key, ".", string(entryJSON)).Err()
		if err != nil {
			fmt.Printf("Warning: Failed to store log in Redis for key %s: %v. Falling back to file-based logging.\n", key, err)
			if err.Error() == "redis: client is closed" || err.Error() == "redis: connection pool timeout" {
				fmt.Println("Redis connection lost. Attempting to reconnect...")
				if pingErr := lw.redisClient.Ping(lw.ctx).Err(); pingErr != nil {
					fmt.Printf("Failed to reconnect to Redis: %v\n", pingErr)
					lw.useRedis = false
				}
			}
		} else {
			return nil
		}
	} else {
		fmt.Printf("Redis is not available. Using file-based logging for key: %s\n", key)
	}

	// Fallback ke file jika Redis tidak tersedia
	entryJSON = append(entryJSON, '\n')
	jakartaLocation, _ := time.LoadLocation("Asia/Jakarta")
	date := time.Now().In(jakartaLocation).Format("2006-01-02")
	filename := fmt.Sprintf("%s.json", date)
	filePath := filepath.Join(lw.logsDir, filename)

	file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open log file: %w", err)
	}
	defer file.Close()

	if _, err := file.Write(entryJSON); err != nil {
		return fmt.Errorf("failed to write log entry: %w", err)
	}

	return nil
}

// LogSuccess logs a successful operation
func (lw *LogWriter) LogSuccess(key, workerType string, metadata map[string]interface{}) error {
	return lw.WriteLog(key, workerType, "success", metadata)
}

// LogFailure logs a failed operation
func (lw *LogWriter) LogFailure(key, workerType string, metadata map[string]interface{}) error {
	return lw.WriteLog(key, workerType, "failed", metadata)
}

// LogRetry logs a retry operation
func (lw *LogWriter) LogRetry(key, workerType string, metadata map[string]interface{}) error {
	return lw.WriteLog(key, workerType, "retry", metadata)
}

// LogReschedule logs a re-schedule operation
func (lw *LogWriter) LogReschedule(key, workerType string, metadata map[string]interface{}) error {
	return lw.WriteLog(key, workerType, "re-schedule", metadata)
}

// WriteLogWithDate writes a log entry to Redis with a custom date for the key
func (lw *LogWriter) WriteLogWithDate(workerType, status, dateStr string, metadata map[string]interface{}) error {
	// If metadata is nil, initialize it
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	// Get current timestamp in UTC
	timestamp := time.Now().UTC()
	timestampStr := timestamp.Format(time.RFC3339)

	// Add worker_id to key if available in metadata
	taskID := "unknown"
	if id, ok := metadata["worker_id"]; ok {
		if idStr, ok := id.(string); ok {
			taskID = idStr
		}
	} else if id, ok := metadata["task_id"]; ok { // Backward compatibility
		if idStr, ok := id.(string); ok {
			taskID = idStr
		}
	}

	// For caveo_to_influx worker, use filename instead of task_id
	if workerType == "caveo_to_influx" {
		if filename, ok := metadata["filename"]; ok {
			if filenameStr, ok := filename.(string); ok {
				taskID = filenameStr
			}
		}
	}

	// Create log entry
	entry := LogEntry{
		Timestamp:  timestampStr,
		WorkerType: workerType,
		Status:     status,
		Metadata:   metadata,
	}

	// Marshal log entry to JSON
	entryJSON, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal log entry: %w", err)
	}

	// Lock to ensure thread safety
	lw.mu.Lock()
	defer lw.mu.Unlock()

	// Create Redis key based on worker type and status with custom date
	var key string

	if lw.useLogFormat && lw.logFormatCfg != nil {
		// Use the Redis log format configuration with custom date
		if workerType == "api_pull" && metadata["status_code"] != nil {
			// Extract status code for api_pull worker
			var statusCode int
			switch sc := metadata["status_code"].(type) {
			case int:
				statusCode = sc
			case float64:
				statusCode = int(sc)
			case string:
				if parsedCode, err := fmt.Sscanf(sc, "%d", &statusCode); err != nil || parsedCode != 1 {
					statusCode = 0
				}
			}

			// Get status code category
			category := redislogformat.GetStatusCodeCategory(statusCode)

			// Format key using the configuration with custom date
			key = lw.logFormatCfg.FormatKeyWithDate(workerType, category, taskID, dateStr)
		} else {
			// Use the configuration for other worker types with custom date
			key = lw.logFormatCfg.FormatKeyWithDate(workerType, status, taskID, dateStr)
		}
	} else {
		// Use standard format for other worker types with custom date
		key = fmt.Sprintf("logs:%s:%s:%s", workerType, dateStr, taskID)
	}

	// Try to reconnect to Redis if it's not available
	if !lw.useRedis && lw.redisClient != nil {
		fmt.Println("Attempting to reconnect to Redis...")
		if err := lw.redisClient.Ping(lw.ctx).Err(); err == nil {
			fmt.Println("Successfully reconnected to Redis!")
			lw.useRedis = true
		}
	}

	if lw.useRedis && lw.redisClient != nil {
		// Store log entry in Redis using JSON.SET
		err := lw.redisClient.Do(lw.ctx, "JSON.SET", key, ".", string(entryJSON)).Err()
		if err != nil {
			// If Redis operation fails, log the error and fall back to file-based logging
			fmt.Printf("Warning: Failed to store log in Redis for key %s: %v. Falling back to file-based logging.\n", key, err)

			// Check if it's a connection error and try to reconnect
			if err.Error() == "redis: client is closed" || err.Error() == "redis: connection pool timeout" {
				fmt.Println("Redis connection lost. Attempting to reconnect...")
				if pingErr := lw.redisClient.Ping(lw.ctx).Err(); pingErr != nil {
					fmt.Printf("Failed to reconnect to Redis: %v\n", pingErr)
					lw.useRedis = false
				}
			}
		} else {
			return nil
		}
	} else {
		fmt.Printf("Redis is not available. Using file-based logging for key: %s\n", key)
	}

	// Fallback to file-based logging if Redis is not available or operation failed
	// Add newline to JSON for file storage
	entryJSON = append(entryJSON, '\n')

	// Get current date for filename using Jakarta time (UTC+7)
	jakartaLocation, _ := time.LoadLocation("Asia/Jakarta")
	date := time.Now().In(jakartaLocation).Format("2006-01-02")
	filename := fmt.Sprintf("%s.json", date)
	filePath := filepath.Join(lw.logsDir, filename)

	// Open file in append mode, create if it doesn't exist
	file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open log file: %w", err)
	}
	defer file.Close()

	// Write log entry to file
	if _, err := file.Write(entryJSON); err != nil {
		return fmt.Errorf("failed to write log entry: %w", err)
	}

	return nil
}

// StoreRescheduleJob stores a job in Redis for re-scheduling
func (lw *LogWriter) StoreRescheduleJob(workerType string, payload []byte, jobID string) error {
	if !lw.useRedis || lw.redisClient == nil {
		return fmt.Errorf("redis is not available")
	}

	// Lock to ensure thread safety
	lw.mu.Lock()
	defer lw.mu.Unlock()

	var data struct {
		Config struct {
			TimeStart string `json:"timeStart"`
			TimeEnd   string `json:"timeEnd"`
		} `json:"config"`
	}

	if err := json.Unmarshal(payload, &data); err != nil {
		fmt.Println("Failed to parse JSON:", err)
		return fmt.Errorf("failed to parse payload JSON: %w", err)
	}

	// For api_pull_caveo worker, use timeStart for date and time formatting
	var dateStr, timeStr string
	var key string

	if workerType == "api_pull_caveo" {
		// Parse timeStart to get date and time
		timestamp, err := time.Parse("2006-01-02 15:04:05", data.Config.TimeStart)
		if err != nil {
			fmt.Println("Invalid timeStart format:", err)
			return fmt.Errorf("invalid timeStart format: %w", err)
		}
		dateStr = timestamp.Format("2006-01-02")
		timeStr = timestamp.Format("1504") // HHMM format
		key = fmt.Sprintf("re-schedule-jobs:%s:%s:%s-%s", workerType, dateStr, jobID, timeStr)
	} else {
		// For other worker types, use timeEnd (backward compatibility)
		timestamp, err := time.Parse("2006-01-02 15:04:05", data.Config.TimeEnd)
		if err != nil {
			fmt.Println("Invalid timeEnd format:", err)
			return fmt.Errorf("invalid timeEnd format: %w", err)
		}
		dateStr = timestamp.Format("2006-01-02")
		key = fmt.Sprintf("re-schedule-jobs:%s:%s:%s", workerType, dateStr, jobID)
	}

	// Parse payload to ensure it's valid JSON before storing
	var payloadData interface{}
	if err := json.Unmarshal(payload, &payloadData); err != nil {
		return fmt.Errorf("invalid JSON payload: %w", err)
	}

	// Store job payload in Redis as JSON object using JSON.SET
	err := lw.redisClient.Do(lw.ctx, "JSON.SET", key, ".", string(payload)).Err()
	if err != nil {
		return fmt.Errorf("failed to store re-schedule jobs in Redis: %w", err)
	}

	return nil
}

// GetLogsByWorkerType retrieves logs from Redis for a specific worker type
func (lw *LogWriter) GetLogsByWorkerType(workerType string) ([]LogEntry, error) {
	if !lw.useRedis || lw.redisClient == nil {
		return nil, fmt.Errorf("redis is not available")
	}

	// Create patterns for keys - check all formats for backward compatibility
	patterns := []string{
		fmt.Sprintf("logs:%s:*", workerType), // Standard format
	}

	if workerType == "api_pull" {
		patterns = append(patterns,
			"logs:api_pull:*:success:*",      // New format for success
			"logs:api_pull:*:redirect:*",     // New format for redirect
			"logs:api_pull:*:client-error:*", // New format for client error
			"logs:api_pull:*:server-error:*", // New format for server error
			"logs:api_pull:*:unknown:*",      // New format for unknown
		)
	}

	// Add legacy format for api_pull_caveo if applicable
	if workerType == "api_pull_caveo" {
		patterns = append(patterns, fmt.Sprintf("logs/%s/*", workerType)) // Legacy format
	}

	// Get all keys matching the patterns
	var allKeys []string
	for _, pattern := range patterns {
		keys, err := lw.redisClient.Keys(lw.ctx, pattern).Result()
		if err != nil {
			return nil, fmt.Errorf("failed to get keys from Redis with pattern %s: %w", pattern, err)
		}
		allKeys = append(allKeys, keys...)
	}

	if len(allKeys) == 0 {
		return []LogEntry{}, nil
	}

	// Get log entries for each key
	logs := make([]LogEntry, 0, len(allKeys))
	for _, key := range allKeys {
		// First try to get log entry using RedisJSON
		result, err := lw.redisClient.Do(lw.ctx, "JSON.GET", key, ".").Result()
		if err == nil {
			// Successfully retrieved using RedisJSON
			var entry LogEntry
			if err := json.Unmarshal([]byte(result.(string)), &entry); err != nil {
				continue
			}
			logs = append(logs, entry)
			continue
		}

		// If RedisJSON failed, try regular GET (for backward compatibility)
		jsonData, err := lw.redisClient.Get(lw.ctx, key).Result()
		if err != nil {
			if err.Error() == "redis: nil" {
				// Skip if key not found (might have been deleted)
				continue
			}

			// Check if it's a WRONGTYPE error
			if err.Error() == "WRONGTYPE Operation against a key holding the wrong kind of value" {
				continue
			}
			continue
		}

		// Unmarshal JSON data
		var entry LogEntry
		if err := json.Unmarshal([]byte(jsonData), &entry); err != nil {
			continue
		}

		logs = append(logs, entry)
	}

	return logs, nil
}

// GetLogsByTaskID retrieves logs from Redis for a specific task ID
func (lw *LogWriter) GetLogsByTaskID(taskID string) ([]LogEntry, error) {
	if !lw.useRedis || lw.redisClient == nil {
		return nil, fmt.Errorf("redis is not available")
	}

	// Create patterns for keys - check all formats for backward compatibility
	patterns := []string{
		fmt.Sprintf("logs:*:*:%s", taskID),                // New standard format for all workers
		fmt.Sprintf("logs:*:*:%s:*", taskID),              // Old format with timestamp
		"logs:api_pull:*:*:" + taskID,                     // Format for api_pull with status code category
		"logs:api_pull:*:*:" + taskID + ":*",              // Old format for api_pull with timestamp
		fmt.Sprintf("logs/api_pull_caveo/*/*/%s", taskID), // Legacy format for api_pull_caveo
	}

	// Get all keys matching the patterns
	var allKeys []string
	for _, pattern := range patterns {
		keys, err := lw.redisClient.Keys(lw.ctx, pattern).Result()
		if err != nil {
			return nil, fmt.Errorf("failed to get keys from Redis with pattern %s: %w", pattern, err)
		}
		allKeys = append(allKeys, keys...)
	}

	if len(allKeys) == 0 {
		return []LogEntry{}, nil
	}

	// Get log entries for each key
	logs := make([]LogEntry, 0, len(allKeys))
	for _, key := range allKeys {
		// First try to get log entry using RedisJSON
		result, err := lw.redisClient.Do(lw.ctx, "JSON.GET", key, ".").Result()
		if err == nil {
			// Successfully retrieved using RedisJSON
			var entry LogEntry
			if err := json.Unmarshal([]byte(result.(string)), &entry); err != nil {
				continue
			}
			logs = append(logs, entry)
			continue
		}

		// If RedisJSON failed, try regular GET (for backward compatibility)
		jsonData, err := lw.redisClient.Get(lw.ctx, key).Result()
		if err != nil {
			if err.Error() == "redis: nil" {
				// Skip if key not found (might have been deleted)
				continue
			}

			// Check if it's a WRONGTYPE error
			if err.Error() == "WRONGTYPE Operation against a key holding the wrong kind of value" {
				continue
			}
			continue
		}

		// Unmarshal JSON data
		var entry LogEntry
		if err := json.Unmarshal([]byte(jsonData), &entry); err != nil {
			continue
		}

		logs = append(logs, entry)
	}

	return logs, nil
}

// IsRedisAvailable returns true if Redis is available and configured
func (lw *LogWriter) IsRedisAvailable() bool {
	if !lw.useRedis || lw.redisClient == nil {
		return false
	}

	// Test Redis connection
	if err := lw.redisClient.Ping(lw.ctx).Err(); err != nil {
		return false
	}

	return true
}

// GetRescheduleJobs retrieves jobs that need to be re-scheduled from Redis
func (lw *LogWriter) GetRescheduleJobs(workerType string) ([][]byte, error) {
	if !lw.useRedis || lw.redisClient == nil {
		return nil, fmt.Errorf("redis is not available")
	}

	// Create pattern for keys
	pattern := fmt.Sprintf("re-schedule-jobs:%s:*", workerType)

	// Get all keys matching the pattern
	keys, err := lw.redisClient.Keys(lw.ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get re-schedule jobs keys from Redis: %w", err)
	}

	if len(keys) == 0 {
		return [][]byte{}, nil
	}

	// Get job payloads for each key
	jobs := make([][]byte, 0, len(keys))
	for _, key := range keys {
		// Get job payload from Redis
		result, err := lw.redisClient.Get(lw.ctx, key).Result()
		if err != nil {
			continue
		}

		jobs = append(jobs, []byte(result))

		// Delete the key after retrieving it to avoid processing it multiple times
		lw.redisClient.Del(lw.ctx, key)
	}

	return jobs, nil
}

// StoreInvalidPayload stores an invalid payload in Redis
func (lw *LogWriter) StoreInvalidPayload(key string, payload json.RawMessage) error {
	if !lw.useRedis || lw.redisClient == nil {
		return fmt.Errorf("redis is not available")
	}

	// Lock to ensure thread safety
	lw.mu.Lock()
	defer lw.mu.Unlock()

	// Store payload in Redis
	err := lw.redisClient.Set(lw.ctx, key, payload, 0).Err()
	if err != nil {
		return fmt.Errorf("failed to store invalid payload in Redis: %w", err)
	}

	return nil
}

// StoreInRedis stores data in Redis with the given key
func (lw *LogWriter) StoreInRedis(key string, data map[string]interface{}) error {
	if !lw.useRedis || lw.redisClient == nil {
		return fmt.Errorf("redis is not available")
	}

	// Lock to ensure thread safety
	lw.mu.Lock()
	defer lw.mu.Unlock()

	// Store data in Redis
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data to JSON: %w", err)
	}
	err = lw.redisClient.Do(lw.ctx, "JSON.SET", key, ".", string(jsonBytes)).Err()
	if err != nil {
		return fmt.Errorf("failed to store data in Redis: %w", err)
	}

	return nil
}

// DeleteRescheduleJob removes a reschedule job from Redis
func (lw *LogWriter) DeleteRescheduleJob(workerType string, jobID string, payload []byte) error {
	if !lw.useRedis || lw.redisClient == nil {
		return fmt.Errorf("redis is not available")
	}

	// Lock to ensure thread safety
	lw.mu.Lock()
	defer lw.mu.Unlock()

	// Extract date from payload to match the key format used when storing
	var data struct {
		Config struct {
			TimeStart string `json:"timeStart"`
			TimeEnd   string `json:"timeEnd"`
		} `json:"config"`
	}

	var key string
	if err := json.Unmarshal(payload, &data); err != nil {
		// If we can't parse the payload, try to find the key using pattern matching
		var pattern string
		if workerType == "api_pull_caveo" {
			// Try both new format (with time) and old format patterns
			pattern = fmt.Sprintf("re-schedule-jobs:%s:*:%s*", workerType, jobID)
		} else {
			pattern = fmt.Sprintf("re-schedule-jobs:%s:*:%s", workerType, jobID)
		}

		keys, err := lw.redisClient.Keys(lw.ctx, pattern).Result()
		if err != nil {
			return fmt.Errorf("failed to find reschedule jobs key: %w", err)
		}
		if len(keys) == 0 {
			// Key doesn't exist, consider it already deleted
			return nil
		}
		// Use the first matching key
		err = lw.redisClient.Del(lw.ctx, keys[0]).Err()
		if err != nil {
			return fmt.Errorf("failed to delete re-schedule jobs from Redis: %w", err)
		}
		return nil
	}

	// For api_pull_caveo worker, use timeStart for key construction
	if workerType == "api_pull_caveo" {
		// Parse timeStart to get the date and time that was used when storing the job
		timestamp, err := time.Parse("2006-01-02 15:04:05", data.Config.TimeStart)
		if err != nil {
			// If we can't parse the time, try to find the key using pattern matching
			pattern := fmt.Sprintf("re-schedule-jobs:%s:*:%s*", workerType, jobID)
			keys, err := lw.redisClient.Keys(lw.ctx, pattern).Result()
			if err != nil {
				return fmt.Errorf("failed to find reschedule jobs key: %w", err)
			}
			if len(keys) == 0 {
				// Key doesn't exist, consider it already deleted
				return nil
			}
			// Use the first matching key
			err = lw.redisClient.Del(lw.ctx, keys[0]).Err()
			if err != nil {
				return fmt.Errorf("failed to delete re-schedule jobs from Redis: %w", err)
			}
			return nil
		}

		dateStr := timestamp.Format("2006-01-02")
		timeStr := timestamp.Format("1504") // HHMM format
		key = fmt.Sprintf("re-schedule-jobs:%s:%s:%s-%s", workerType, dateStr, jobID, timeStr)
	} else {
		// For other worker types, use timeEnd (backward compatibility)
		timestamp, err := time.Parse("2006-01-02 15:04:05", data.Config.TimeEnd)
		if err != nil {
			// If we can't parse the time, try to find the key using pattern matching
			pattern := fmt.Sprintf("re-schedule-jobs:%s:*:%s", workerType, jobID)
			keys, err := lw.redisClient.Keys(lw.ctx, pattern).Result()
			if err != nil {
				return fmt.Errorf("failed to find reschedule jobs key: %w", err)
			}
			if len(keys) == 0 {
				// Key doesn't exist, consider it already deleted
				return nil
			}
			// Use the first matching key
			err = lw.redisClient.Del(lw.ctx, keys[0]).Err()
			if err != nil {
				return fmt.Errorf("failed to delete re-schedule jobs from Redis: %w", err)
			}
			return nil
		}

		dateStr := timestamp.Format("2006-01-02")
		key = fmt.Sprintf("re-schedule-jobs:%s:%s:%s", workerType, dateStr, jobID)
	}

	// Delete the key from Redis
	err := lw.redisClient.Del(lw.ctx, key).Err()
	if err != nil {
		return fmt.Errorf("failed to delete re-schedule jobs from Redis: %w", err)
	}

	return nil
}

// StoreFailedJob stores a failed job in Redis with the format failed-job:YYYY-MM-DD:imei
func (lw *LogWriter) StoreFailedJob(dateStr string, imei string, payload []byte, metadata map[string]interface{}) error {
	if !lw.useRedis || lw.redisClient == nil {
		return fmt.Errorf("redis is not available")
	}

	// Lock to ensure thread safety
	lw.mu.Lock()
	defer lw.mu.Unlock()

	// Create failed job key: failed-job:YYYY-MM-DD:imei
	key := fmt.Sprintf("failed-jobs:%s:%s", dateStr, imei)

	// Create failed job data
	failedJobData := map[string]interface{}{
		"original_payload": string(payload),
		"timestamp":        time.Now().UTC().Format(time.RFC3339),
		"reason":           "Job failed",
	}

	// Add metadata if provided
	if metadata != nil {
		for k, v := range metadata {
			failedJobData[k] = v
		}
	}

	// Convert to JSON
	jsonData, err := json.Marshal(failedJobData)
	if err != nil {
		return fmt.Errorf("failed to marshal failed job data: %w", err)
	}

	// Store failed job in Redis using JSON.SET
	err = lw.redisClient.Do(lw.ctx, "JSON.SET", key, ".", string(jsonData)).Err()
	if err != nil {
		return fmt.Errorf("failed to store failed job in Redis: %w", err)
	}

	fmt.Printf("Successfully stored failed job in Redis with key: %s\n", key)
	return nil
}
