package couchdb

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"
)

// Server represents a CouchDB server configuration
type Server struct {
	Host       string `json:"host"`
	Port       string `json:"port"`
	Username   string `json:"username"`
	Password   string `json:"password"`
	UseSSL     bool   `json:"use_ssl"`
	SkipVerify bool   `json:"skip_verify"`
}

// Client represents a CouchDB client
type Client struct {
	httpClients map[string]*http.Client
	servers     map[string]Server
	baseURLs    map[string]string
}

// Document represents a CouchDB document
type Document map[string]any

// ViewRow represents a row in a CouchDB view response
type ViewRow struct {
	ID    string          `json:"id"`
	Key   string          `json:"key"`
	Value json.RawMessage `json:"value"`
	Doc   Document        `json:"doc,omitempty"`
}

// ViewResponse represents a response from a CouchDB view
type ViewResponse struct {
	TotalRows int       `json:"total_rows"`
	Offset    int       `json:"offset"`
	Rows      []ViewRow `json:"rows"`
}

// ErrorResponse represents a CouchDB error response
type ErrorResponse struct {
	Error  string `json:"error"`
	Reason string `json:"reason"`
}

// NewClient creates a new CouchDB client
func NewClient(configPath string) (*Client, error) {
	// Load server configurations from the config file
	servers, err := loadConfig(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load CouchDB configuration: %w", err)
	}

	// Initialize HTTP clients for each server
	httpClients := make(map[string]*http.Client)
	baseURLs := make(map[string]string)

	for name, server := range servers {
		// Create a custom transport with TLS configuration based on server settings
		transport := &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: server.SkipVerify,
			},
		}

		// Create HTTP client with the custom transport
		httpClient := &http.Client{
			Transport: transport,
			Timeout:   time.Second * 30, // Default timeout of 30 seconds
		}

		// Build base URL
		protocol := "http"
		if server.UseSSL {
			protocol = "https"
		}

		// Store the base URL for this server
		baseURL := fmt.Sprintf("%s://%s:%s", protocol, server.Host, server.Port)
		baseURLs[name] = baseURL

		httpClients[name] = httpClient
	}

	return &Client{
		httpClients: httpClients,
		servers:     servers,
		baseURLs:    baseURLs,
	}, nil
}

// loadConfig loads the CouchDB server configurations from a JSON file
func loadConfig(configPath string) (map[string]Server, error) {
	// Read the configuration file
	file, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse the configuration
	var config struct {
		Servers map[string]Server `json:"servers"`
	}
	if err := json.Unmarshal(file, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return config.Servers, nil
}

// createRequest creates an HTTP request with the appropriate headers and authentication
func (c *Client) createRequest(ctx context.Context, serverName, method, path string, body io.Reader) (*http.Request, error) {
	server, exists := c.servers[serverName]
	if !exists {
		return nil, fmt.Errorf("server %s not found in configuration", serverName)
	}

	// Build URL
	baseURL, exists := c.baseURLs[serverName]
	if !exists {
		return nil, fmt.Errorf("base URL for server %s not found", serverName)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, method, baseURL+path, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Add headers
	req.Header.Set("Accept", "application/json")
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Add basic auth
	req.SetBasicAuth(server.Username, server.Password)

	return req, nil
}

// executeRequest executes an HTTP request and returns the response body
func (c *Client) executeRequest(serverName string, req *http.Request) ([]byte, int, error) {
	httpClient, exists := c.httpClients[serverName]
	if !exists {
		return nil, 0, fmt.Errorf("HTTP client for server %s not found", serverName)
	}

	// Execute request
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to execute HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, resp.StatusCode, fmt.Errorf("failed to read response body: %w", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errResp ErrorResponse
		if err := json.Unmarshal(body, &errResp); err == nil {
			return nil, resp.StatusCode, fmt.Errorf("CouchDB error: %s - %s", errResp.Error, errResp.Reason)
		}
		return nil, resp.StatusCode, fmt.Errorf("HTTP error: %d - %s", resp.StatusCode, resp.Status)
	}

	return body, resp.StatusCode, nil
}

// Connect checks if the CouchDB server is reachable
func (c *Client) Connect(ctx context.Context, serverName string) error {
	server, exists := c.servers[serverName]
	if !exists {
		return fmt.Errorf("server %s not found in configuration", serverName)
	}

	// Build URL for the server info endpoint
	protocol := "http"
	if server.UseSSL {
		protocol = "https"
	}

	// Create HTTP client with the appropriate TLS configuration
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: server.SkipVerify,
		},
	}
	httpClient := &http.Client{Transport: transport}

	// Create request to the server info endpoint
	url := fmt.Sprintf("%s://%s:%s/", protocol, server.Host, server.Port)
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Add basic auth
	req.SetBasicAuth(server.Username, server.Password)

	// Execute request
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to connect to CouchDB server: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode >= 400 {
		return fmt.Errorf("failed to connect to CouchDB server: unexpected status code %d", resp.StatusCode)
	}

	fmt.Printf("Debug: Successfully connected to CouchDB server at %s\n", url)
	return nil
}

// CreateDatabase creates a new database
func (c *Client) CreateDatabase(ctx context.Context, serverName, dbName string) error {
	// Create request
	path := fmt.Sprintf("/%s", dbName)
	req, err := c.createRequest(ctx, serverName, "PUT", path, nil)
	if err != nil {
		return err
	}

	// Execute request
	_, _, err = c.executeRequest(serverName, req)
	if err != nil {
		return fmt.Errorf("failed to create database: %w", err)
	}

	return nil
}

// DeleteDatabase deletes a database
func (c *Client) DeleteDatabase(ctx context.Context, serverName, dbName string) error {
	// Create request
	path := fmt.Sprintf("/%s", dbName)
	req, err := c.createRequest(ctx, serverName, "DELETE", path, nil)
	if err != nil {
		return err
	}

	// Execute request
	_, _, err = c.executeRequest(serverName, req)
	if err != nil {
		return fmt.Errorf("failed to delete database: %w", err)
	}

	return nil
}

// CreateDocument creates a new document
func (c *Client) CreateDocument(ctx context.Context, serverName, dbName string, doc Document) (string, error) {
	// Convert document to JSON
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return "", fmt.Errorf("failed to marshal document: %w", err)
	}

	// Create request
	path := fmt.Sprintf("/%s", dbName)
	req, err := c.createRequest(ctx, serverName, "POST", path, bytes.NewReader(docJSON))
	if err != nil {
		return "", err
	}

	// Execute request
	respBody, _, err := c.executeRequest(serverName, req)
	if err != nil {
		return "", fmt.Errorf("failed to create document: %w", err)
	}

	// Parse response to get document ID
	var resp struct {
		ID  string `json:"id"`
		Rev string `json:"rev"`
		OK  bool   `json:"ok"`
	}
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if !resp.OK || resp.ID == "" {
		return "", fmt.Errorf("failed to create document: unexpected response")
	}

	return resp.ID, nil
}

// GetDocument gets a document by ID
func (c *Client) GetDocument(ctx context.Context, serverName, dbName, docID string) (Document, error) {
	// Create request
	path := fmt.Sprintf("/%s/%s", dbName, docID)
	req, err := c.createRequest(ctx, serverName, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	// Execute request
	respBody, _, err := c.executeRequest(serverName, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// Parse response
	var doc Document
	if err := json.Unmarshal(respBody, &doc); err != nil {
		return nil, fmt.Errorf("failed to parse document: %w", err)
	}

	return doc, nil
}

// UpdateDocument updates an existing document
func (c *Client) UpdateDocument(ctx context.Context, serverName, dbName, docID string, doc Document) error {
	// Get the current document to get the revision
	currentDoc, err := c.GetDocument(ctx, serverName, dbName, docID)
	if err != nil {
		return fmt.Errorf("failed to get current document: %w", err)
	}

	// Add the revision to the document
	doc["_rev"] = currentDoc["_rev"]

	// Convert document to JSON
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	// Create request
	path := fmt.Sprintf("/%s/%s", dbName, docID)
	req, err := c.createRequest(ctx, serverName, "PUT", path, bytes.NewReader(docJSON))
	if err != nil {
		return err
	}

	// Execute request
	_, _, err = c.executeRequest(serverName, req)
	if err != nil {
		return fmt.Errorf("failed to update document: %w", err)
	}

	return nil
}

// DeleteDocument deletes a document
func (c *Client) DeleteDocument(ctx context.Context, serverName, dbName, docID string) error {
	// Get the current document to get the revision
	currentDoc, err := c.GetDocument(ctx, serverName, dbName, docID)
	if err != nil {
		return fmt.Errorf("failed to get current document: %w", err)
	}

	rev, ok := currentDoc["_rev"].(string)
	if !ok {
		return fmt.Errorf("failed to get document revision")
	}

	// Create request with rev query parameter
	path := fmt.Sprintf("/%s/%s?rev=%s", dbName, docID, rev)
	req, err := c.createRequest(ctx, serverName, "DELETE", path, nil)
	if err != nil {
		return err
	}

	// Execute request
	_, _, err = c.executeRequest(serverName, req)
	if err != nil {
		return fmt.Errorf("failed to delete document: %w", err)
	}

	return nil
}

// GetAllDocuments gets all documents in a database
func (c *Client) GetAllDocuments(ctx context.Context, serverName, dbName string) ([]Document, error) {
	// Create request
	path := fmt.Sprintf("/%s/_all_docs?include_docs=true", dbName)
	req, err := c.createRequest(ctx, serverName, "GET", path, nil)
	if err != nil {
		return nil, err
	}

	// Execute request
	respBody, _, err := c.executeRequest(serverName, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get all documents: %w", err)
	}

	// Parse response
	var resp struct {
		TotalRows int `json:"total_rows"`
		Offset    int `json:"offset"`
		Rows      []struct {
			ID  string   `json:"id"`
			Key string   `json:"key"`
			Doc Document `json:"doc"`
		} `json:"rows"`
	}
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Extract documents
	docs := make([]Document, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		docs = append(docs, row.Doc)
	}

	return docs, nil
}

// GetDocumentByView gets documents using a view
func (c *Client) GetDocumentByView(ctx context.Context, serverName, dbName, designDoc, viewName string, options map[string]string) (*ViewResponse, error) {
	server, exists := c.servers[serverName]
	if !exists {
		return nil, fmt.Errorf("server %s not found in configuration", serverName)
	}

	// Build URL for the view endpoint
	protocol := "http"
	if server.UseSSL {
		protocol = "https"
	}

	// Build query parameters
	queryParams := url.Values{}
	for k, v := range options {
		queryParams.Add(k, v)
	}

	// Build URL - include the server name (database) in the path
	urlStr := fmt.Sprintf("%s://%s:%s/%s/%s/_design/%s/_view/%s",
		protocol, server.Host, server.Port, serverName, dbName, designDoc, viewName)
	if len(queryParams) > 0 {
		urlStr += "?" + queryParams.Encode()
	}

	// Create HTTP client with the appropriate TLS configuration
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: server.SkipVerify,
		},
	}
	httpClient := &http.Client{Transport: transport}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", urlStr, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Add basic auth
	req.SetBasicAuth(server.Username, server.Password)

	// Execute request
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Check for error response
	if resp.StatusCode >= 400 {
		var errResp ErrorResponse
		if err := json.Unmarshal(body, &errResp); err == nil {
			return nil, fmt.Errorf("CouchDB error: %s - %s", errResp.Error, errResp.Reason)
		}
		return nil, fmt.Errorf("HTTP error: %d - %s", resp.StatusCode, resp.Status)
	}

	// Parse response
	var viewResp ViewResponse
	if err := json.Unmarshal(body, &viewResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &viewResp, nil
}

// View is a convenience method for GetDocumentByView with common options
func (c *Client) View(ctx context.Context, serverName, dbName, designDoc, viewName, key string, includeDocs bool) (*ViewResponse, error) {
	options := make(map[string]string)

	if key != "" {
		// If key is not already quoted, quote it
		// But don't double quote it
		if strings.HasPrefix(key, "\"") && strings.HasSuffix(key, "\"") {
			options["key"] = key
		} else {
			options["key"] = fmt.Sprintf("\"%s\"", key)
		}
	}

	if includeDocs {
		options["include_docs"] = "true"
	}

	// Build URL for debugging
	server, exists := c.servers[serverName]
	if !exists {
		return nil, fmt.Errorf("server %s not found in configuration", serverName)
	}

	// Build query parameters
	queryParams := url.Values{}
	for k, v := range options {
		queryParams.Add(k, v)
	}

	// Build URL for debugging
	protocol := "http"
	if server.UseSSL {
		protocol = "https"
	}

	// Include the server name in the path before the database name
	debugURL := fmt.Sprintf("%s://%s:%s/%s/%s/_design/%s/_view/%s",
		protocol, server.Host, server.Port, serverName, dbName, designDoc, viewName)
	if len(queryParams) > 0 {
		debugURL += "?" + queryParams.Encode()
	}

	return c.GetDocumentByView(ctx, serverName, dbName, designDoc, viewName, options)
}
