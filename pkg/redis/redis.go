package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

// LogEntry represents a log entry in Redis
type LogEntry struct {
	Timestamp  string                 `json:"timestamp"`
	WorkerType string                 `json:"worker_type"`
	Status     string                 `json:"status"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// Client represents a Redis client
type Client struct {
	client *redis.Client
}

// Config holds Redis configuration
type Config struct {
	Host     string
	Port     string
	Password string
	DB       int
}

// New creates a new Redis client
func New(cfg Config) (*Client, error) {
	// Create Redis client
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &Client{
		client: client,
	}, nil
}

// Close closes the Redis client
func (c *Client) Close() error {
	return c.client.Close()
}

// Ping checks if the Redis server is reachable
func (c *Client) Ping(ctx context.Context) error {
	return c.client.Ping(ctx).Err()
}

// GetKeysByPattern returns all keys matching a pattern
func (c *Client) GetKeysByPattern(ctx context.Context, pattern string) ([]string, error) {
	var keys []string
	iter := c.client.Scan(ctx, 0, pattern, 0).Iterator()
	for iter.Next(ctx) {
		keys = append(keys, iter.Val())
	}
	if err := iter.Err(); err != nil {
		return nil, fmt.Errorf("failed to scan keys: %w", err)
	}
	return keys, nil
}

// DeleteKey deletes a key from Redis
func (c *Client) DeleteKey(ctx context.Context, key string) error {
	result, err := c.client.Del(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("failed to delete key %s: %w", key, err)
	}

	if result == 0 {
		return fmt.Errorf("key %s not found", key)
	}

	return nil
}

// Info returns information about the Redis server
func (c *Client) Info(ctx context.Context) (map[string]string, error) {
	info, err := c.client.Info(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis info: %w", err)
	}

	// Parse info string into map
	infoMap := make(map[string]string)
	lines := strings.Split(info, "\r\n")

	for _, line := range lines {
		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Split line by colon
		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			continue
		}

		// Add to map
		infoMap[parts[0]] = parts[1]
	}

	return infoMap, nil
}

// DeleteByKeyPattern deletes all keys matching the given pattern
func (c *Client) DeleteByKeyPattern(ctx context.Context, pattern string) (int64, error) {
	// Use SCAN to find all keys matching the pattern
	iter := c.client.Scan(ctx, 0, pattern, 0).Iterator()

	var keys []string
	for iter.Next(ctx) {
		keys = append(keys, iter.Val())
	}

	if err := iter.Err(); err != nil {
		return 0, fmt.Errorf("failed to scan keys: %w", err)
	}

	// If no keys found, return 0
	if len(keys) == 0 {
		return 0, nil
	}

	// Delete all found keys
	deleted, err := c.client.Del(ctx, keys...).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to delete keys: %w", err)
	}

	return deleted, nil
}

// Get gets a value from Redis, handling both JSON and string data types
func (c *Client) Get(ctx context.Context, key string) (string, error) {
	// First try to get using JSON.GET (for JSON data)
	result, err := c.client.Do(ctx, "JSON.GET", key, ".").Result()
	if err == nil {
		// Successfully retrieved using JSON.GET
		if jsonStr, ok := result.(string); ok {
			return jsonStr, nil
		}
		return "", fmt.Errorf("JSON.GET result for key %s is not a string", key)
	}

	// If JSON.GET failed, try regular GET (for string data or backward compatibility)
	val, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", fmt.Errorf("key %s not found", key)
		}
		return "", fmt.Errorf("failed to get key %s: %w", key, err)
	}

	return val, nil
}

// Set sets a value in Redis
func (c *Client) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if err := c.client.Set(ctx, key, value, expiration).Err(); err != nil {
		return fmt.Errorf("failed to set key %s: %w", key, err)
	}

	return nil
}

// SaveLog saves a log entry to Redis
// Key format: logs:{worker_type}:{timestamp}:{task_id}
func (c *Client) SaveLog(ctx context.Context, entry LogEntry) error {
	// Generate a unique key for the log entry
	taskID := "unknown"
	if entry.Metadata != nil {
		if id, ok := entry.Metadata["task_id"].(string); ok {
			taskID = id
		}
	}

	// Format timestamp for key (remove special characters)
	keyTimestamp := strings.ReplaceAll(entry.Timestamp, ":", "-")
	keyTimestamp = strings.ReplaceAll(keyTimestamp, ".", "-")

	// Create key
	key := fmt.Sprintf("logs:%s:%s:%s", entry.WorkerType, keyTimestamp, taskID)

	// Marshal log entry to JSON
	jsonData, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal log entry: %w", err)
	}

	// Save to Redis (no expiration for now, can be configured later)
	return c.Set(ctx, key, jsonData, 0)
}

// GetLogsByPattern retrieves logs matching a pattern
func (c *Client) GetLogsByPattern(ctx context.Context, pattern string) ([]LogEntry, error) {
	// Use SCAN to find all keys matching the pattern
	iter := c.client.Scan(ctx, 0, pattern, 0).Iterator()

	var logs []LogEntry
	for iter.Next(ctx) {
		key := iter.Val()

		// First try to get log entry using RedisJSON
		result, err := c.client.Do(ctx, "JSON.GET", key, ".").Result()
		if err == nil {
			// Successfully retrieved using RedisJSON
			var entry LogEntry
			if err := json.Unmarshal([]byte(result.(string)), &entry); err != nil {
				// Log the error but continue with next key
				fmt.Printf("Warning: Failed to unmarshal log entry for key %s: %v\n", key, err)
				continue
			}
			logs = append(logs, entry)
			continue
		}

		// If RedisJSON failed, try regular GET (for backward compatibility)
		jsonData, err := c.client.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				// Skip if key not found (might have been deleted)
				continue
			}

			// Check if it's a WRONGTYPE error
			if err.Error() == "WRONGTYPE Operation against a key holding the wrong kind of value" {
				fmt.Printf("Warning: Key %s has wrong type, skipping\n", key)
				continue
			}

			return nil, fmt.Errorf("failed to get log entry for key %s: %w", key, err)
		}

		// Unmarshal JSON data
		var entry LogEntry
		if err := json.Unmarshal([]byte(jsonData), &entry); err != nil {
			// Log the error but continue with next key
			fmt.Printf("Warning: Failed to unmarshal log entry for key %s: %v\n", key, err)
			continue
		}

		logs = append(logs, entry)
	}

	if err := iter.Err(); err != nil {
		return nil, fmt.Errorf("failed to scan keys: %w", err)
	}

	return logs, nil
}

// GetLogsByWorkerType retrieves logs for a specific worker type
func (c *Client) GetLogsByWorkerType(ctx context.Context, workerType string) ([]LogEntry, error) {
	// Handle special case for api_pull_caveo worker type
	var pattern string
	if workerType == "api_pull_caveo" {
		// Check both formats for api_pull_caveo
		patternStandard := fmt.Sprintf("logs:%s:*", workerType)
		patternLegacy := fmt.Sprintf("logs/%s/*", workerType)

		// Get logs from both patterns and merge them
		logsStandard, err1 := c.GetLogsByPattern(ctx, patternStandard)
		logsLegacy, err2 := c.GetLogsByPattern(ctx, patternLegacy)

		// If both patterns failed, return the first error
		if err1 != nil && err2 != nil {
			return nil, err1
		}

		// Merge logs from both patterns
		return append(logsStandard, logsLegacy...), nil
	} else {
		// Original format for other workers
		pattern = fmt.Sprintf("logs:%s:*", workerType)
		return c.GetLogsByPattern(ctx, pattern)
	}
}

// GetLogsByStatus retrieves logs with a specific status
func (c *Client) GetLogsByStatus(ctx context.Context, status string) ([]LogEntry, error) {
	// First get all logs
	allLogs, err := c.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return nil, err
	}

	// Filter by status
	var filteredLogs []LogEntry
	for _, log := range allLogs {
		if log.Status == status {
			filteredLogs = append(filteredLogs, log)
		}
	}

	return filteredLogs, nil
}

// GetLogsByTaskID retrieves logs for a specific task ID
func (c *Client) GetLogsByTaskID(ctx context.Context, taskID string) ([]LogEntry, error) {
	// First get all logs
	allLogs, err := c.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return nil, err
	}

	// Filter by task ID in metadata
	var filteredLogs []LogEntry
	for _, log := range allLogs {
		if log.Metadata != nil {
			if id, ok := log.Metadata["task_id"].(string); ok && id == taskID {
				filteredLogs = append(filteredLogs, log)
			}
		}
	}

	return filteredLogs, nil
}

// GetLogsByIMEI retrieves logs for a specific IMEI
func (c *Client) GetLogsByIMEI(ctx context.Context, imei string) ([]LogEntry, error) {
	// First get all logs
	allLogs, err := c.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return nil, err
	}

	// Filter by IMEI in metadata
	var filteredLogs []LogEntry
	for _, log := range allLogs {
		if log.Metadata != nil {
			if id, ok := log.Metadata["imei"].(string); ok && id == imei {
				filteredLogs = append(filteredLogs, log)
			}
		}
	}

	return filteredLogs, nil
}

// GetLogsByDate retrieves logs for a specific date (YYYY-MM-DD)
func (c *Client) GetLogsByDate(ctx context.Context, date string) ([]LogEntry, error) {
	// Create pattern for key that includes the date
	pattern := fmt.Sprintf("logs:*:%s:*", date)

	// Get logs matching the pattern
	logs, err := c.GetLogsByPattern(ctx, pattern)
	if err != nil {
		return nil, err
	}

	// If no logs found with the pattern, try filtering by timestamp
	if len(logs) == 0 {
		// Get all logs
		allLogs, err := c.GetLogsByPattern(ctx, "logs:*")
		if err != nil {
			return nil, err
		}

		// Filter logs by date in timestamp
		var filteredLogs []LogEntry
		for _, log := range allLogs {
			// Parse timestamp
			t, err := time.Parse(time.RFC3339, log.Timestamp)
			if err != nil {
				// Skip logs with invalid timestamps
				continue
			}

			// Format date as YYYY-MM-DD
			logDate := t.Format("2006-01-02")

			// Compare with requested date
			if logDate == date {
				filteredLogs = append(filteredLogs, log)
			}
		}

		return filteredLogs, nil
	}

	return logs, nil
}
