package envcheck

import (
	"fmt"
	"os"
	"strings"
)

// List of all environment variables used in the application
var EnvVars = []string{
	// Application configuration
	"LOG_LEVEL",
	"PORT",
	"WORKER_POOL_SIZE",
	"ENVIRONMENT",

	// RabbitMQ configuration
	"RABBITMQ_HOST",
	"RABBITMQ_PORT",
	"RABBITMQ_PORT_MANAGEMENT",
	"RABBITMQ_USER",
	"RABBITMQ_PASSWORD",
	"RABBITMQ_API_PULL_QUEUE",
	"RABBITMQ_API_PULL_CAVEO_QUEUE",
	"RABBITMQ_DB_CLONE_QUEUE",

	// API configuration
	"API_BASE_URL",
	"API_TIMEOUT",

	// Caveo API configuration
	"CAVEO_API_BASE_URL",
	"CAVEO_API_TOKEN",
	"CAVEO_API_TIMEOUT",
	"CAVEO_API_TOKEN_USERNAME",
	"CAVEO_API_TOKEN_PASSWORD",

	// Redis configuration
	"REDIS_HOST",
	"REDIS_PORT",
	"REDIS_PASSWORD",
	"REDIS_DB",
	"REDIS_MAX_MEMORY",
	"REDIS_INSIGHT_PORT",

	// Minio configuration
	"MINIO_PORT",
	"MINIO_CONSOLE_PORT",
	"MINIO_ENDPOINT",
	"MINIO_ACCESS_KEY",
	"MINIO_SECRET_KEY",
	"MINIO_USE_SSL",
	"MINIO_BUCKET",
	"DB_CLONE_BUCKET",

	// Source database configuration
	"SOURCE_DB_HOST",
	"SOURCE_DB_PORT",
	"SOURCE_DB_USER",
	"SOURCE_DB_PASSWORD",
	"SOURCE_DB_NAME",

	// Target database configuration
	"TARGET_DB_HOST",
	"TARGET_DB_PORT",
	"TARGET_DB_USER",
	"TARGET_DB_PASSWORD",
	"TARGET_DB_NAME",

	// Reschedule worker configuration
	"RESCHEDULE_TIME_THRESHOLD_HOURS",
}

// ValidateEnvVars checks if all required environment variables are set
// It returns a list of missing variables and a boolean indicating if all variables are set
func ValidateEnvVars() ([]string, bool) {
	var missingVars []string

	for _, envVar := range EnvVars {
		if os.Getenv(envVar) == "" {
			missingVars = append(missingVars, envVar)
		}
	}

	return missingVars, len(missingVars) == 0
}

// PrintMissingVars prints a message about missing environment variables
func PrintMissingVars(missingVars []string) {
	fmt.Println("The following environment variables are not set:")
	for _, envVar := range missingVars {
		fmt.Printf("  - %s\n", envVar)
	}
	fmt.Println("\nPlease set these variables in your .env file or environment.")
}

// CheckAndValidateEnvVars checks if all required environment variables are set
// and prints a warning message if any are missing
func CheckAndValidateEnvVars() {
	missingVars, allSet := ValidateEnvVars()
	if !allSet {
		fmt.Println("WARNING: Some environment variables are not set (empty values).")
		PrintMissingVars(missingVars)
		fmt.Println("The application will continue, but some features may not work correctly.")
		fmt.Println("These variables are defined but have empty values.")
	} else {
		fmt.Println("All required environment variables are set with non-empty values.")
	}

}

// EnsureEnvVarsExist checks if all required environment variables exist (can be empty)
// It returns an error if any variables don't exist in the environment
func EnsureEnvVarsExist() error {
	var missingVars []string

	// Create a map to store environment variables
	envMap := make(map[string]bool)

	// Check if .env file exists and load it
	if _, err := os.Stat(".env"); err == nil {
		// Read .env file
		data, err := os.ReadFile(".env")
		if err == nil {
			// Parse .env file
			lines := strings.Split(string(data), "\n")
			for _, line := range lines {
				line = strings.TrimSpace(line)
				if line == "" || strings.HasPrefix(line, "#") {
					continue
				}
				parts := strings.SplitN(line, "=", 2)
				if len(parts) == 2 {
					key := strings.TrimSpace(parts[0])
					envMap[key] = true

					// Set environment variable if not already set
					if _, exists := os.LookupEnv(key); !exists {
						os.Setenv(key, strings.TrimSpace(parts[1]))
					}
				}
			}
		}
	}

	// Check if all required environment variables exist
	for _, envVar := range EnvVars {
		_, existsInEnv := os.LookupEnv(envVar)
		_, existsInFile := envMap[envVar]

		if !existsInEnv && !existsInFile {
			missingVars = append(missingVars, envVar)
		}
	}

	if len(missingVars) > 0 {
		return fmt.Errorf("missing required environment variables: %s", strings.Join(missingVars, ", "))
	}

	return nil
}
