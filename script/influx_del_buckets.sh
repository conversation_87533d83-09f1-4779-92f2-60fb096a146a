#!/bin/bash

# Load variables from .env file
if [ -f ".env" ]; then
  export $(grep -v '^#' .env | xargs)
else
  echo ".env file not found!"
  exit 1
fi

# Construct host URL
INFLUX_HOST="http://localhost:${INFLUX_PORT:-8086}"

# Fetch ORG_ID dynamically from INFLUX_ORG
ORG_ID=$(curl -s --request GET \
  "$INFLUX_HOST/api/v2/orgs" \
  --header "Authorization: Token $INFLUX_TOKEN" \
  | jq -r --arg ORG_NAME "$INFLUX_ORG" '.orgs[] | select(.name == $ORG_NAME) | .id')

if [ -z "$ORG_ID" ]; then
  echo "Failed to fetch ORG_ID for organization: $INFLUX_ORG"
  exit 1
fi

# Get all bucket IDs where name matches "caveo-<IMEI>" format
bucket_ids=$(curl -s --request GET \
  "$INFLUX_HOST/api/v2/buckets?orgID=$ORG_ID&limit=100" \
  --header "Authorization: Token $INFLUX_TOKEN" \
  | jq -r '.buckets[] | select(.name | test("^caveo-[0-9]{15}$")) | .id')

# Safety check
if [ -z "$bucket_ids" ]; then
  echo "No matching buckets found."
  exit 0
fi

echo "Found $(echo "$bucket_ids" | wc -l) matching buckets. Deleting..."

# Delete each matching bucket
for id in $bucket_ids; do
  echo "Deleting bucket with ID: $id"
  curl -s --request DELETE \
    "$INFLUX_HOST/api/v2/buckets/$id" \
    --header "Authorization: Token $INFLUX_TOKEN"
done

echo "Done."
