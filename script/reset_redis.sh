#!/bin/sh

# Pindah ke direktori kerja
cd ~/projects/us-providers/Workers/smrtp-workers || {
  echo "Gagal pindah ke direktori project"
  exit 1
}

echo "🔽 Menurunkan container Docker..."
docker-compose -f docker-compose.yml down

# Tambahkan waktu tunggu agar proses selesai sepenuhnya
echo "⏳ Menunggu proses shutdown Docker selesai..."
sleep 7

echo "🧹 Menghapus direktori minio..."
rm -rf ./data/minio

echo "📦 Menyalin data dari minio28 ke minio..."
cp -r ./data/minio28 ./data/minio

echo "🧹 Menghapus direktori redis..."
rm -rf ./data/redis

echo "📦 Menyalin data dari redis28 ke redis..."
cp -r ./data/redis28 ./data/redis

echo "🚀 Menjalankan ulang container redis, rabbitmq, minio, dan influxdb..."
docker-compose -f docker-compose.yml up -d redis rabbitmq minio influxdb

# Tunggu agar container memiliki cukup waktu untuk inisialisasi
echo "⏳ Menunggu container startup..."
sleep 10

echo "✅ Proses selesai!"
