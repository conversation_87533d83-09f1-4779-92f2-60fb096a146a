#!/bin/bash

# Move to the root directory
cd "$(dirname "$0")/.."

# Check if .env file exists, if not, create it from example
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example"
    cp .env.example .env
fi

# Run all services except the worker service
echo "Starting services (rabbitmq, minio, and redis)..."
docker compose up -d rabbitmq minio redis influxdb

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
sleep 5

# Check if services are running
echo "Checking service status..."
docker compose ps

echo "Services started successfully!"
echo "RabbitMQ Management UI: http://localhost:15672"
echo "MinIO Console: http://localhost:9001"
echo "Redis Insight: http://localhost:8001"
