#!/bin/bash

# Muat variabel dari file .env di root folder
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
else
  echo "❌ File .env tidak ditemukan di direktori saat ini."
  exit 1
fi

# Validasi variabel
if [[ -z "$REDIS_HOST" || -z "$REDIS_PORT" || -z "$REDIS_PASSWORD" ]]; then
  echo "❌ REDIS_HOST, REDIS_PORT, atau REDIS_PASSWORD belum diatur di .env"
  exit 1
fi

# Pola kunci
PATTERNS=(
  # "cronjob*"
  # "logs:*"
  # "jobs:*"
  # "re-schedule-jobs:*"
  # "not-comply-jobs:*"
  # "failed-jobs:*"
  "*"
)

# Fungsi: Hapus kunci berdasarkan pattern menggunakan SCAN + pipelining
delete_keys_by_pattern() {
  local pattern="$1"
  echo "🧹 Menghapus kunci dengan pola: $pattern"

  # Scan dan hapus secara bulk
  redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning --scan --pattern "$pattern" \
  | xargs -r -L1000 redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" --no-auth-warning del > /dev/null

  echo "✅ Selesai hapus untuk pola: $pattern"
}

# Loop semua pola
for pattern in "${PATTERNS[@]}"; do
  delete_keys_by_pattern "$pattern"
done

echo "✅ Semua pola selesai diproses."
