package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
)

// WorkerConfig represents the configuration for a worker
type WorkerConfig struct {
	Enabled            bool    `json:"enabled"`
	Count              int     `json:"count"`
	Queue              string  `json:"queue"`
	Bucket             string  `json:"bucket"`
	Timeout            int     `json:"timeout,omitempty"`
	RetryAttempts      int     `json:"retry_attempts,omitempty"`
	RetryDelay         int     `json:"retry_delay,omitempty"`
	APIEndpoint        string  `json:"api_endpoint,omitempty"`
	BatchSize          int     `json:"batch_size,omitempty"`
	AccuracyThreshold  float64 `json:"accuracy_threshold,omitempty"`
	MaxRescheduleCount int     `json:"max_reschedule_count,omitempty"`
}

// GlobalConfig represents global configuration settings
type GlobalConfig struct {
	LogLevel            string `json:"log_level"`
	MaxConcurrentTasks  int    `json:"max_concurrent_tasks"`
	HealthCheckInterval int    `json:"health_check_interval"`
}

// WorkersConfig represents the configuration for all workers
type WorkersConfig struct {
	Workers map[string]WorkerConfig `json:"workers"`
	Global  GlobalConfig            `json:"global"`
	mu      sync.RWMutex
}

// GetWorkerConfig returns the configuration for a specific worker
func (wc *WorkersConfig) GetWorkerConfig(workerType string) (WorkerConfig, bool) {
	wc.mu.RLock()
	defer wc.mu.RUnlock()
	config, exists := wc.Workers[workerType]
	return config, exists
}

// GetEnabledWorkers returns a map of enabled workers
func (wc *WorkersConfig) GetEnabledWorkers() map[string]WorkerConfig {
	wc.mu.RLock()
	defer wc.mu.RUnlock()

	enabledWorkers := make(map[string]WorkerConfig)
	for workerType, config := range wc.Workers {
		if config.Enabled {
			enabledWorkers[workerType] = config
		}
	}

	return enabledWorkers
}

// GetAllQueues returns all queue names from the configuration
func (wc *WorkersConfig) GetAllQueues() []string {
	wc.mu.RLock()
	defer wc.mu.RUnlock()

	var queues []string
	for _, config := range wc.Workers {
		if config.Enabled {
			queues = append(queues, config.Queue)
		}
	}

	return queues
}

// GetGlobalConfig returns the global configuration
func (wc *WorkersConfig) GetGlobalConfig() GlobalConfig {
	wc.mu.RLock()
	defer wc.mu.RUnlock()
	return wc.Global
}

// LoadWorkersConfig loads the workers configuration from a JSON file
func LoadWorkersConfig(configPath string) (*WorkersConfig, error) {
	// If configPath is empty, use the default path
	if configPath == "" {
		configPath = "config/workers.json"
	}

	// Make sure the path is absolute
	if !filepath.IsAbs(configPath) {
		// Get the current working directory
		cwd, err := os.Getwd()
		if err != nil {
			return nil, fmt.Errorf("failed to get current working directory: %w", err)
		}
		configPath = filepath.Join(cwd, configPath)
	}

	// Read the configuration file
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read workers configuration file: %w", err)
	}

	// Parse the configuration
	var config WorkersConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse workers configuration: %w", err)
	}

	return &config, nil
}

// SaveWorkersConfig saves the workers configuration to a JSON file
func SaveWorkersConfig(config *WorkersConfig, configPath string) error {
	// If configPath is empty, use the default path
	if configPath == "" {
		configPath = "config/workers.json"
	}

	// Make sure the path is absolute
	if !filepath.IsAbs(configPath) {
		// Get the current working directory
		cwd, err := os.Getwd()
		if err != nil {
			return fmt.Errorf("failed to get current working directory: %w", err)
		}
		configPath = filepath.Join(cwd, configPath)
	}

	// Create the directory if it doesn't exist
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory for workers configuration: %w", err)
	}

	// Marshal the configuration to JSON
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal workers configuration: %w", err)
	}

	// Write the configuration to the file
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write workers configuration: %w", err)
	}

	return nil
}
