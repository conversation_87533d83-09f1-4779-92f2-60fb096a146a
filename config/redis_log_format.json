{"api_pull": {"success": "logs:api_pull:YYYY-MM-DD:success:%s", "redirect": "logs:api_pull:YYYY-MM-DD:redirect:%s", "client-error": "logs:api_pull:YYYY-MM-DD:client-error:%s", "server-error": "logs:api_pull:YYYY-MM-DD:server-error:%s", "unknown": "logs:api_pull:YYYY-MM-DD:unknown:%s"}, "api_pull_caveo": {"success": "logs:api_pull_caveo:YYYY-MM-DD:success:%s", "failed": "logs:api_pull_caveo:YYYY-MM-DD:failed:%s", "error": "logs:api_pull_caveo:YYYY-MM-DD:error:%s", "re-schedule": "logs:api_pull_caveo:YYYY-MM-DD:re-schedule:%s", "retry": "logs:api_pull_caveo:YYYY-MM-DD:retry:%s"}, "caveo_to_influx": {"success": "logs:caveo_to_influx:YYYY-MM-DD:success:%s", "failed": "logs:caveo_to_influx:YYYY-MM-DD:failed:%s", "error": "logs:caveo_to_influx:YYYY-MM-DD:error:%s"}, "db_clone": {"success": "logs:db_clone:YYYY-MM-DD:success:%s", "error": "logs:db_clone:YYYY-MM-DD:error:%s"}, "default": {"success": "logs:%s:YYYY-MM-DD:success:%s", "failed": "logs:%s:YYYY-MM-DD:failed:%s", "error": "logs:%s:YYYY-MM-DD:error:%s", "re-schedule": "logs:%s:YYYY-MM-DD:re-schedule:%s", "retry": "logs:%s:YYYY-MM-DD:retry:%s"}}