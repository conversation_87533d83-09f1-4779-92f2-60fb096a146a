package config

// WorkerMetadata defines the metadata structure for different worker types
type WorkerMetadata struct {
	// Common fields for all worker types can be defined here
	WorkerType string `json:"worker_type"`
}

// APIPullMetadata defines the metadata structure for API pulling worker
type APIPullMetadata struct {
	WorkerMetadata
	URL string `json:"url"`
}

// CaveoAPIPullMetadata defines the metadata structure for Caveo API pulling worker
type CaveoAPIPullMetadata struct {
	WorkerMetadata
	URL        string `json:"url"`
	PSM        string `json:"psm"`
	Estate     string `json:"estate"`
	Division   string `json:"division"`
	NIK        string `json:"nik"`
	DataLength int    `json:"data_length"`
}

// DBCloneMetadata defines the metadata structure for DB cloning worker
type DBCloneMetadata struct {
	WorkerMetadata
	CloneDatetime string `json:"clone_datetime"`
	Source        string `json:"source"`
	Destination   string `json:"destination"`
}

// NewAPIPullMetadata creates a new metadata instance for API pulling worker
func NewAPIPullMetadata(url string) *APIPullMetadata {
	return &APIPullMetadata{
		WorkerMetadata: WorkerMetadata{
			WorkerType: "api_pull",
		},
		URL: url,
	}
}

// NewCaveoAPIPullMetadata creates a new metadata instance for Caveo API pulling worker
func NewCaveoAPIPullMetadata(url, psm, estate, division, nik string, dataLength int) *CaveoAPIPullMetadata {
	return &CaveoAPIPullMetadata{
		WorkerMetadata: WorkerMetadata{
			WorkerType: "api_pull_caveo",
		},
		URL:        url,
		PSM:        psm,
		Estate:     estate,
		Division:   division,
		NIK:        nik,
		DataLength: dataLength,
	}
}

// NewDBCloneMetadata creates a new metadata instance for DB cloning worker
func NewDBCloneMetadata(cloneDatetime, source, destination string) *DBCloneMetadata {
	return &DBCloneMetadata{
		WorkerMetadata: WorkerMetadata{
			WorkerType: "db_cloning_worker",
		},
		CloneDatetime: cloneDatetime,
		Source:        source,
		Destination:   destination,
	}
}

// DefaultCaveoAPIPullMetadata creates a default metadata instance for Caveo API pulling worker
func DefaultCaveoAPIPullMetadata() *CaveoAPIPullMetadata {
	return &CaveoAPIPullMetadata{
		WorkerMetadata: WorkerMetadata{
			WorkerType: "api_pull_caveo",
		},
		URL:        "",
		PSM:        "PSM 2",
		Estate:     "SMSE",
		Division:   "DIVISI 4",
		NIK:        "BACKUP",
		DataLength: 1000,
	}
}

// DefaultAPIPullMetadata creates a default metadata instance for API pulling worker
func DefaultAPIPullMetadata() *APIPullMetadata {
	return &APIPullMetadata{
		WorkerMetadata: WorkerMetadata{
			WorkerType: "api_pull",
		},
		URL: "",
	}
}

// DefaultDBCloneMetadata creates a default metadata instance for DB cloning worker
func DefaultDBCloneMetadata() *DBCloneMetadata {
	return &DBCloneMetadata{
		WorkerMetadata: WorkerMetadata{
			WorkerType: "db_cloning_worker",
		},
		CloneDatetime: "",
		Source:        "",
		Destination:   "",
	}
}
