package dispatcher

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/user/workers/config"
	"github.com/user/workers/internal/queue"
	"github.com/user/workers/internal/worker"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/redis"
)

// Dispatcher is responsible for dispatching tasks to the worker pool
type Dispatcher struct {
	rabbitMQ      *queue.RabbitMQ
	log           *logger.Logger
	config        *config.Config
	workersConfig *config.WorkersConfig
	workerPool    interface{} // Worker pool interface
	redisClient   *redis.Client
}

// NewDispatcher creates a new dispatcher
func NewDispatcher(rabbitMQ *queue.RabbitMQ, log *logger.Logger, config *config.Config, workersConfig *config.WorkersConfig) *Dispatcher {
	// Create Redis client
	redisClient, err := redis.New(redis.Config{
		Host:     config.Redis.Host,
		Port:     config.Redis.Port,
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})
	if err != nil {
		log.WithError(err).Warn("Failed to connect to Redis, some features may not work")
	}

	return &Dispatcher{
		rabbitMQ:      rabbitMQ,
		log:           log,
		config:        config,
		workersConfig: workersConfig,
		workerPool:    nil, // Will be set later
		redisClient:   redisClient,
	}
}

// SetWorkerPool sets the worker pool for the dispatcher
func (d *Dispatcher) SetWorkerPool(pool interface{}) {
	d.workerPool = pool
}

// GetWorkerPool returns the worker pool
func (d *Dispatcher) GetWorkerPool() interface{} {
	return d.workerPool
}

// Dispatch dispatches a task to the worker pool via RabbitMQ
func (d *Dispatcher) Dispatch(ctx context.Context, task *worker.Task) error {
	// Update payload with RescheduleCount if it's a JSON payload (but don't increment here)
	if len(task.Payload) > 0 {
		var payloadMap map[string]interface{}
		if err := json.Unmarshal(task.Payload, &payloadMap); err == nil {
			// Only set reschedule_count if it's not already in the payload
			if _, exists := payloadMap["reschedule_count"]; !exists {
				payloadMap["reschedule_count"] = task.RescheduleCount
			} else {
				// Use the reschedule_count from the payload
				if count, ok := payloadMap["reschedule_count"].(float64); ok {
					task.RescheduleCount = int(count)
				} else if count, ok := payloadMap["reschedule_count"].(int); ok {
					task.RescheduleCount = count
				}
			}
			if updatedPayload, err := json.Marshal(payloadMap); err == nil {
				task.Payload = updatedPayload
			}
		}
	}

	// Marshal task to JSON
	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal task: %w", err)
	}

	// Determine which queue to use based on task type
	var queueName string

	// If workers config is available, use it to determine the queue
	if d.workersConfig != nil {
		// Map worker type to config key
		var configKey string
		switch task.Type {
		case worker.APIWorker:
			configKey = "api_pull"
		case worker.APICaveoWorker:
			configKey = "api_pull_caveo"
		case worker.DBWorker:
			configKey = "db_clone"
		case "caveo_payload_maker":
			configKey = "caveo_payload_maker"
		case worker.RescheduleCaveoWorker:
			configKey = "reschedule_caveo"
		case "caveo_to_influx":
			configKey = "caveo_to_influx"
		case worker.GeofDivisionWorkerType:
			configKey = "geof_division"
		case worker.GeofEstateWorkerType:
			configKey = "geof_estate"
		case worker.GeofBlockWorkerType:
			configKey = "geof_block"
		default:
			return fmt.Errorf("unknown worker type: %s", task.Type)
		}

		// Get worker config
		workerConfig, exists := d.workersConfig.GetWorkerConfig(configKey)
		if !exists || !workerConfig.Enabled {
			return fmt.Errorf("worker type %s is not enabled in configuration", task.Type)
		}

		queueName = workerConfig.Queue
	} else {
		// Fallback to legacy queue configuration
		switch task.Type {
		case worker.APIWorker:
			queueName = d.config.RabbitMQ.APIPullQueue
		case worker.APICaveoWorker:
			queueName = d.config.RabbitMQ.APICaveoPullQueue
		case worker.DBWorker:
			queueName = d.config.RabbitMQ.DBCloneQueue
		case "caveo_payload_maker":
			queueName = "caveo_payload_maker_tasks" // Default queue name
		case "caveo_to_influx":
			queueName = "caveo_to_influx_tasks" // Default queue name
		case worker.GeofDivisionWorkerType:
			queueName = "geof_division_tasks" // Default queue name
		case worker.GeofEstateWorkerType:
			queueName = "geof_estate_tasks" // Default queue name
		case worker.GeofBlockWorkerType:
			queueName = "geof_block_tasks" // Default queue name
		default:
			return fmt.Errorf("unknown worker type: %s", task.Type)
		}
	}

	// Store task in Redis before publishing to RabbitMQ
	if d.redisClient != nil {
		// Format date for key
		date := time.Now().Format("2006-01-02")

		// Create key in format: jobs:{worker_type}:{date}:{task_id}
		key := fmt.Sprintf("jobs:%s:%s:%s", task.Type, date, task.ID)

		// Store task in Redis with no expiration
		if err := d.redisClient.Set(ctx, key, body, 0); err != nil {
			d.log.WithError(err).
				WithField("task_id", task.ID).
				Warn("Failed to store task in Redis, continuing with RabbitMQ dispatch")
		} else {
			d.log.WithField("task_id", task.ID).
				WithField("key", key).
				Info("Task stored in Redis")
		}
	}

	// Publish message to RabbitMQ
	if err := d.rabbitMQ.Publish(ctx, queueName, body); err != nil {
		return fmt.Errorf("failed to publish task: %w", err)
	}

	d.log.WithField("task_id", task.ID).
		WithField("queue", queueName).
		Info("Task dispatched successfully")
	return nil
}
