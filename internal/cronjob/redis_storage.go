package cronjob

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/user/workers/pkg/logger"
)

// RedisStorage implements the Storage interface using Redis
type RedisStorage struct {
	client *redis.Client
	ctx    context.Context
	log    *logger.Logger
}

// NewRedisStorage creates a new Redis storage
func NewRedisStorage(client *redis.Client, log *logger.Logger) *RedisStorage {
	return &RedisStorage{
		client: client,
		ctx:    context.Background(),
		log:    log,
	}
}

// SaveCronjob saves a cronjob to Redis
func (s *RedisStorage) SaveCronjob(cronjob *Cronjob) error {
	// Update timestamps
	now := time.Now()
	if cronjob.CreatedAt.IsZero() {
		cronjob.CreatedAt = now
	}
	cronjob.UpdatedAt = now

	// Convert cronjob to map for Redis hash
	cronjobMap, err := s.cronjobToMap(cronjob)
	if err != nil {
		return fmt.Errorf("failed to convert cronjob to map: %w", err)
	}

	// Create a Redis transaction
	pipe := s.client.TxPipeline()

	// Save cronjob hash
	key := fmt.Sprintf("cronjob:%s", cronjob.Name)
	pipe.HSet(s.ctx, key, cronjobMap)

	// Add to all cronjobs set
	pipe.SAdd(s.ctx, "cronjobs:all", cronjob.Name)

	// Add/remove from enabled cronjobs set
	if cronjob.Enabled {
		pipe.SAdd(s.ctx, "cronjobs:enabled", cronjob.Name)
	} else {
		pipe.SRem(s.ctx, "cronjobs:enabled", cronjob.Name)
	}

	// Execute transaction
	_, err = pipe.Exec(s.ctx)
	if err != nil {
		return fmt.Errorf("failed to save cronjob to Redis: %w", err)
	}

	s.log.WithField("cronjob", cronjob.Name).Info("Cronjob saved to Redis")
	return nil
}

// GetCronjob retrieves a cronjob from Redis
func (s *RedisStorage) GetCronjob(name string) (*Cronjob, error) {
	key := fmt.Sprintf("cronjob:%s", name)

	// Check if cronjob exists
	exists, err := s.client.Exists(s.ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to check if cronjob exists: %w", err)
	}
	if exists == 0 {
		return nil, fmt.Errorf("cronjob %s not found", name)
	}

	// Get cronjob hash
	cronjobMap, err := s.client.HGetAll(s.ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get cronjob from Redis: %w", err)
	}

	// Convert map to cronjob
	cronjob, err := s.mapToCronjob(cronjobMap)
	if err != nil {
		return nil, fmt.Errorf("failed to convert map to cronjob: %w", err)
	}

	return cronjob, nil
}

// ListCronjobs lists all cronjobs from Redis
func (s *RedisStorage) ListCronjobs() ([]*Cronjob, error) {
	// Get all cronjob names
	names, err := s.client.SMembers(s.ctx, "cronjobs:all").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get cronjob names from Redis: %w", err)
	}

	// Get each cronjob
	cronjobs := make([]*Cronjob, 0, len(names))
	for _, name := range names {
		cronjob, err := s.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("cronjob", name).Warn("Failed to get cronjob")
			continue
		}
		cronjobs = append(cronjobs, cronjob)
	}

	return cronjobs, nil
}

// ListEnabledCronjobs lists all enabled cronjobs from Redis
func (s *RedisStorage) ListEnabledCronjobs() ([]*Cronjob, error) {
	// Get enabled cronjob names
	names, err := s.client.SMembers(s.ctx, "cronjobs:enabled").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get enabled cronjob names from Redis: %w", err)
	}

	// Get each cronjob
	cronjobs := make([]*Cronjob, 0, len(names))
	for _, name := range names {
		cronjob, err := s.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("cronjob", name).Warn("Failed to get cronjob")
			continue
		}
		cronjobs = append(cronjobs, cronjob)
	}

	return cronjobs, nil
}

// DeleteCronjob deletes a cronjob from Redis
func (s *RedisStorage) DeleteCronjob(name string) error {
	key := fmt.Sprintf("cronjob:%s", name)

	// Create a Redis transaction
	pipe := s.client.TxPipeline()

	// Delete cronjob hash
	pipe.Del(s.ctx, key)

	// Remove from all cronjobs set
	pipe.SRem(s.ctx, "cronjobs:all", name)

	// Remove from enabled cronjobs set
	pipe.SRem(s.ctx, "cronjobs:enabled", name)

	// Execute transaction
	_, err := pipe.Exec(s.ctx)
	if err != nil {
		return fmt.Errorf("failed to delete cronjob from Redis: %w", err)
	}

	s.log.WithField("cronjob", name).Info("Cronjob deleted from Redis")
	return nil
}

// EnableCronjob enables a cronjob
func (s *RedisStorage) EnableCronjob(name string) error {
	// Get cronjob
	cronjob, err := s.GetCronjob(name)
	if err != nil {
		return err
	}

	// Enable cronjob
	cronjob.Enabled = true

	// Save cronjob
	return s.SaveCronjob(cronjob)
}

// DisableCronjob disables a cronjob
func (s *RedisStorage) DisableCronjob(name string) error {
	// Get cronjob
	cronjob, err := s.GetCronjob(name)
	if err != nil {
		return err
	}

	// Disable cronjob
	cronjob.Enabled = false

	// Save cronjob
	return s.SaveCronjob(cronjob)
}

// UpdateLastRun updates the last run time of a cronjob
func (s *RedisStorage) UpdateLastRun(name string, lastRun time.Time) error {
	key := fmt.Sprintf("cronjob:%s", name)

	// Update last run time
	err := s.client.HSet(s.ctx, key, "last_run", lastRun.Format(time.RFC3339)).Err()
	if err != nil {
		return fmt.Errorf("failed to update last run time: %w", err)
	}

	return nil
}

// UpdateNextRun updates the next run time of a cronjob
func (s *RedisStorage) UpdateNextRun(name string, nextRun time.Time) error {
	key := fmt.Sprintf("cronjob:%s", name)

	// Update next run time
	err := s.client.HSet(s.ctx, key, "next_run", nextRun.Format(time.RFC3339)).Err()
	if err != nil {
		return fmt.Errorf("failed to update next run time: %w", err)
	}

	return nil
}

// Helper methods for converting between Cronjob and map
func (s *RedisStorage) cronjobToMap(cronjob *Cronjob) (map[string]interface{}, error) {
	// Convert cronjob to JSON
	jsonBytes, err := json.Marshal(cronjob)
	if err != nil {
		return nil, err
	}

	// Convert JSON to map
	var cronjobMap map[string]interface{}
	if err := json.Unmarshal(jsonBytes, &cronjobMap); err != nil {
		return nil, err
	}

	// Flatten nested maps to JSON strings for Redis hash
	for key, value := range cronjobMap {
		if value == nil {
			// Skip nil values
			continue
		}

		valueType := reflect.TypeOf(value)
		if valueType != nil && (valueType.Kind() == reflect.Map || valueType.Kind() == reflect.Slice) {
			jsonValue, err := json.Marshal(value)
			if err != nil {
				return nil, err
			}
			cronjobMap[key] = string(jsonValue)
		}
	}

	return cronjobMap, nil
}

func (s *RedisStorage) mapToCronjob(cronjobMap map[string]string) (*Cronjob, error) {
	// Create a new cronjob
	cronjob := &Cronjob{}

	// Set simple fields
	cronjob.Name = cronjobMap["name"]
	cronjob.Description = cronjobMap["description"]
	cronjob.Enabled = cronjobMap["enabled"] == "true" || cronjobMap["enabled"] == "1"
	cronjob.Schedule = cronjobMap["schedule"]
	cronjob.WorkerType = cronjobMap["worker_type"]
	cronjob.Timeout, _ = strconv.Atoi(cronjobMap["timeout"])

	// Parse timestamps
	if createdAt, exists := cronjobMap["created_at"]; exists && createdAt != "" {
		t, err := time.Parse(time.RFC3339, createdAt)
		if err == nil {
			cronjob.CreatedAt = t
		}
	}

	if updatedAt, exists := cronjobMap["updated_at"]; exists && updatedAt != "" {
		t, err := time.Parse(time.RFC3339, updatedAt)
		if err == nil {
			cronjob.UpdatedAt = t
		}
	}

	if lastRun, exists := cronjobMap["last_run"]; exists && lastRun != "" {
		t, err := time.Parse(time.RFC3339, lastRun)
		if err == nil {
			cronjob.LastRun = t
		}
	}

	if nextRun, exists := cronjobMap["next_run"]; exists && nextRun != "" {
		t, err := time.Parse(time.RFC3339, nextRun)
		if err == nil {
			cronjob.NextRun = t
		}
	}

	// Parse complex fields
	if payload, exists := cronjobMap["payload"]; exists && payload != "" {
		var payloadMap map[string]interface{}
		if err := json.Unmarshal([]byte(payload), &payloadMap); err != nil {
			return nil, err
		}
		cronjob.Payload = payloadMap
	}

	if variables, exists := cronjobMap["variables"]; exists && variables != "" {
		var variablesMap map[string]string
		if err := json.Unmarshal([]byte(variables), &variablesMap); err != nil {
			return nil, err
		}
		cronjob.Variables = variablesMap
	}

	if retryPolicy, exists := cronjobMap["retry_policy"]; exists && retryPolicy != "" {
		var retryPolicyObj RetryPolicy
		if err := json.Unmarshal([]byte(retryPolicy), &retryPolicyObj); err != nil {
			return nil, err
		}
		cronjob.RetryPolicy = retryPolicyObj
	}

	return cronjob, nil
}
