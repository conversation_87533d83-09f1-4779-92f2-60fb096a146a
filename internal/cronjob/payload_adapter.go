package cronjob

import (
	"fmt"
	"strings"
)

// PayloadAdapter defines the interface for adapting payloads to worker-specific formats
type PayloadAdapter interface {
	// Validate validates that the payload is valid for the worker type
	Validate(payload map[string]interface{}) error

	// Adapt transforms the payload to match the expected format for the worker type
	Adapt(payload map[string]interface{}) (map[string]interface{}, error)
}

// GetPayloadAdapter returns the appropriate adapter for the given worker type
func GetPayloadAdapter(workerType string) (PayloadAdapter, error) {
	switch workerType {
	case "api_pull":
		return &APIPullPayloadAdapter{}, nil
	case "api_pull_caveo":
		return &CaveoAPIPayloadAdapter{}, nil
	case "db_clone":
		return &DBClonePayloadAdapter{}, nil
	case "caveo_payload_maker":
		return &CaveoPayloadMakerAdapter{}, nil
	default:
		return nil, fmt.Errorf("unknown worker type: %s", workerType)
	}
}

// APIPullPayloadAdapter adapts payloads for the api_pull worker
type APIPullPayloadAdapter struct{}

// Validate validates that the payload is valid for the api_pull worker
func (a *APIPullPayloadAdapter) Validate(payload map[string]interface{}) error {
	// Check if config exists
	config, ok := payload["config"]
	if !ok {
		return fmt.Errorf("missing config field")
	}

	// Check if config is a map
	configMap, ok := config.(map[string]interface{})
	if !ok {
		return fmt.Errorf("config field must be an object")
	}

	// Check required fields
	requiredFields := []string{"url", "method"}
	for _, field := range requiredFields {
		if _, ok := configMap[field]; !ok {
			return fmt.Errorf("missing required field in config: %s", field)
		}
	}

	// Check method is valid
	method, ok := configMap["method"].(string)
	if !ok {
		return fmt.Errorf("method field must be a string")
	}

	method = strings.ToUpper(method)
	if method != "GET" && method != "POST" {
		return fmt.Errorf("unsupported method: %s", method)
	}

	// Check headers
	if headers, ok := configMap["headers"]; ok {
		if _, ok := headers.(map[string]interface{}); !ok {
			return fmt.Errorf("headers field must be an object")
		}
	}

	return nil
}

// Adapt transforms the payload to match the expected format for the api_pull worker
func (a *APIPullPayloadAdapter) Adapt(payload map[string]interface{}) (map[string]interface{}, error) {
	// Create a deep copy of the payload to avoid modifying the original
	adaptedPayload := make(map[string]interface{})

	// Set worker_type
	adaptedPayload["worker_type"] = "api_pull"

	// Copy config
	if config, ok := payload["config"]; ok {
		adaptedPayload["config"] = config
	} else {
		// Create config if it doesn't exist
		adaptedPayload["config"] = make(map[string]interface{})
	}

	// Copy RescheduleCount if it exists
	if rescheduleCount, ok := payload["reschedule_count"]; ok {
		adaptedPayload["reschedule_count"] = rescheduleCount
	} else {
		adaptedPayload["reschedule_count"] = 0
	}

	// Ensure headers exist
	configMap := adaptedPayload["config"].(map[string]interface{})
	if _, ok := configMap["headers"]; !ok {
		configMap["headers"] = make(map[string]interface{})
	}

	// Ensure Content-Type header is set
	headers := configMap["headers"].(map[string]interface{})
	if _, ok := headers["Content-Type"]; !ok {
		headers["Content-Type"] = "application/json"
	}

	return adaptedPayload, nil
}

// CaveoAPIPayloadAdapter adapts payloads for the api_pull_caveo worker
type CaveoAPIPayloadAdapter struct{}

// Validate validates that the payload is valid for the api_pull_caveo worker
func (a *CaveoAPIPayloadAdapter) Validate(payload map[string]interface{}) error {
	// Check if config exists
	config, ok := payload["config"]
	if !ok {
		return fmt.Errorf("missing config field")
	}

	// Check if config is a map
	configMap, ok := config.(map[string]interface{})
	if !ok {
		return fmt.Errorf("config field must be an object")
	}

	// Check device field
	device, ok := configMap["device"]
	if !ok {
		return fmt.Errorf("missing device field in config")
	}

	// Check if device is a map
	deviceMap, ok := device.(map[string]interface{})
	if !ok {
		return fmt.Errorf("device field must be an object")
	}

	// Check required device fields
	requiredDeviceFields := []string{"ident"}
	for _, field := range requiredDeviceFields {
		if _, ok := deviceMap[field]; !ok {
			return fmt.Errorf("missing required field in device: %s", field)
		}
	}

	// Check timeStart and timeEnd
	requiredTimeFields := []string{"timeStart", "timeEnd"}
	for _, field := range requiredTimeFields {
		if _, ok := configMap[field]; !ok {
			return fmt.Errorf("missing required field in config: %s", field)
		}
	}

	return nil
}

// Adapt transforms the payload to match the expected format for the api_pull_caveo worker
func (a *CaveoAPIPayloadAdapter) Adapt(payload map[string]interface{}) (map[string]interface{}, error) {
	// Create a deep copy of the payload to avoid modifying the original
	adaptedPayload := make(map[string]interface{})

	// Set worker_type
	adaptedPayload["worker_type"] = "api_pull_caveo"

	// Copy config
	if config, ok := payload["config"]; ok {
		adaptedPayload["config"] = config
	} else {
		// Create config if it doesn't exist
		adaptedPayload["config"] = make(map[string]interface{})
	}

	// Copy RescheduleCount if it exists
	if rescheduleCount, ok := payload["reschedule_count"]; ok {
		adaptedPayload["reschedule_count"] = rescheduleCount
	} else {
		adaptedPayload["reschedule_count"] = 0
	}

	return adaptedPayload, nil
}

// DBClonePayloadAdapter adapts payloads for the db_clone worker
type DBClonePayloadAdapter struct{}

// Validate validates that the payload is valid for the db_clone worker
func (a *DBClonePayloadAdapter) Validate(payload map[string]interface{}) error {
	// Check table field
	if _, ok := payload["table"]; !ok {
		return fmt.Errorf("missing table field")
	}

	return nil
}

// Adapt transforms the payload to match the expected format for the db_clone worker
func (a *DBClonePayloadAdapter) Adapt(payload map[string]interface{}) (map[string]interface{}, error) {
	// Create a deep copy of the payload to avoid modifying the original
	adaptedPayload := make(map[string]interface{})

	// Copy all fields
	for key, value := range payload {
		adaptedPayload[key] = value
	}

	// Set default batch_size if not present
	if _, ok := adaptedPayload["batch_size"]; !ok {
		adaptedPayload["batch_size"] = 1000
	}

	// Ensure RescheduleCount is set
	if _, ok := adaptedPayload["reschedule_count"]; !ok {
		adaptedPayload["reschedule_count"] = 0
	}

	return adaptedPayload, nil
}

// CaveoPayloadMakerAdapter adapts payloads for the caveo_payload_maker worker
type CaveoPayloadMakerAdapter struct{}

// Validate validates that the payload is valid for the caveo_payload_maker worker
func (a *CaveoPayloadMakerAdapter) Validate(payload map[string]interface{}) error {
	// Check if config exists
	config, ok := payload["config"]
	if !ok {
		return fmt.Errorf("missing config field")
	}

	// Check if config is a map
	configMap, ok := config.(map[string]interface{})
	if !ok {
		return fmt.Errorf("config field must be an object")
	}

	// Check required fields
	if _, ok := configMap["message"]; !ok {
		return fmt.Errorf("missing required field in config: message")
	}

	// Check message is a string
	if _, ok := configMap["message"].(string); !ok {
		return fmt.Errorf("message field must be a string")
	}

	return nil
}

// Adapt transforms the payload to match the expected format for the caveo_payload_maker worker
func (a *CaveoPayloadMakerAdapter) Adapt(payload map[string]interface{}) (map[string]interface{}, error) {
	// Create a deep copy of the payload to avoid modifying the original
	adaptedPayload := make(map[string]interface{})

	// Set worker_type
	adaptedPayload["worker_type"] = "caveo_payload_maker"

	// Copy config
	if config, ok := payload["config"]; ok {
		adaptedPayload["config"] = config
	} else {
		// Create config if it doesn't exist
		adaptedPayload["config"] = make(map[string]interface{})
	}

	// Copy RescheduleCount if it exists
	if rescheduleCount, ok := payload["reschedule_count"]; ok {
		adaptedPayload["reschedule_count"] = rescheduleCount
	} else {
		adaptedPayload["reschedule_count"] = 0
	}

	return adaptedPayload, nil
}
