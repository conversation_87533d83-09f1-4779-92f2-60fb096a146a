package cronjob

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/user/workers/internal/dispatcher"
	"github.com/user/workers/internal/worker"
	"github.com/user/workers/pkg/logger"
)

// Manager manages cronjobs
type Manager struct {
	storage  Storage
	cron     *cron.Cron
	jobs     map[string]cron.EntryID
	dispatch *dispatcher.Dispatcher
	log      *logger.Logger
	mu       sync.RWMutex
}

// NewManager creates a new cronjob manager
func NewManager(storage Storage, dispatch *dispatcher.Dispatcher, log *logger.Logger) *Manager {
	// Create a slice of cron options
	cronOptions := []cron.Option{
		// Enable seconds field in cron expressions
		cron.WithSeconds(),
		// Add logger
		cron.WithLogger(
			cron.VerbosePrintfLogger(
				log.WithField("component", "cron").Logger,
			),
		),
	}

	return &Manager{
		storage:  storage,
		cron:     cron.New(cronOptions...),
		jobs:     make(map[string]cron.EntryID),
		dispatch: dispatch,
		log:      log,
	}
}

// Start starts the cronjob manager
func (m *Manager) Start() error {
	m.log.Info("Starting cronjob manager")

	// Load enabled cronjobs from Redis
	cronjobs, err := m.storage.ListEnabledCronjobs()
	if err != nil {
		return fmt.Errorf("failed to load cronjobs: %w", err)
	}

	// Schedule each cronjob
	for _, cronjob := range cronjobs {
		if err := m.scheduleCronjob(cronjob); err != nil {
			m.log.WithError(err).WithField("cronjob", cronjob.Name).Error("Failed to schedule cronjob")
			continue
		}
	}

	// Start cron scheduler
	m.cron.Start()

	m.log.WithField("cronjob_count", len(cronjobs)).Info("Cronjob manager started")
	return nil
}

// Stop stops the cronjob manager
func (m *Manager) Stop() {
	m.log.Info("Stopping cronjob manager")

	// Stop cron scheduler
	ctx := m.cron.Stop()

	// Wait for jobs to finish
	<-ctx.Done()

	m.log.Info("Cronjob manager stopped")
}

// scheduleCronjob schedules a cronjob
func (m *Manager) scheduleCronjob(cronjob *Cronjob) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Remove existing job if it exists
	if entryID, exists := m.jobs[cronjob.Name]; exists {
		m.cron.Remove(entryID)
		delete(m.jobs, cronjob.Name)
	}

	// Create job function
	jobFunc := func() {
		m.executeCronjob(cronjob.Name)
	}

	// Add job to cron scheduler
	entryID, err := m.cron.AddFunc(cronjob.Schedule, jobFunc)
	if err != nil {
		return fmt.Errorf("failed to add cronjob to scheduler: %w", err)
	}

	// Store entry ID
	m.jobs[cronjob.Name] = entryID

	// Update next run time
	entry := m.cron.Entry(entryID)
	if err := m.storage.UpdateNextRun(cronjob.Name, entry.Next); err != nil {
		m.log.WithError(err).WithField("cronjob", cronjob.Name).Warn("Failed to update next run time")
	}

	m.log.WithFields(map[string]interface{}{
		"cronjob":  cronjob.Name,
		"schedule": cronjob.Schedule,
		"next_run": entry.Next.Format(time.RFC3339),
	}).Info("Cronjob scheduled")

	return nil
}

// executeCronjob executes a cronjob
func (m *Manager) executeCronjob(name string) {
	logger := m.log.WithField("cronjob", name)
	logger.Info("Executing cronjob")

	// Get cronjob from Redis
	cronjob, err := m.storage.GetCronjob(name)
	if err != nil {
		logger.WithError(err).Error("Failed to get cronjob")
		return
	}

	// Update last run time
	now := time.Now()
	if err := m.storage.UpdateLastRun(name, now); err != nil {
		logger.WithError(err).Warn("Failed to update last run time")
	}

	// Process variables in payload
	processedPayload, err := m.processVariables(cronjob)
	if err != nil {
		logger.WithError(err).Error("Failed to process variables")
		return
	}

	// Get payload adapter for worker type
	adapter, err := GetPayloadAdapter(cronjob.WorkerType)
	if err != nil {
		logger.WithError(err).Error("Failed to get payload adapter")
		return
	}

	// Validate payload
	if err := adapter.Validate(processedPayload); err != nil {
		logger.WithError(err).Error("Invalid payload for worker type")
		return
	}

	// Adapt payload to match worker expectations
	adaptedPayload, err := adapter.Adapt(processedPayload)
	if err != nil {
		logger.WithError(err).Error("Failed to adapt payload")
		return
	}

	// Create task
	task := &worker.Task{
		ID:              fmt.Sprintf("cronjob-%s-%d", name, now.Unix()),
		Type:            worker.WorkerType(cronjob.WorkerType),
		CreatedAt:       now.Unix(),
		RescheduleCount: 0,
	}

	// Marshal adapted payload
	payloadBytes, err := json.Marshal(adaptedPayload)
	if err != nil {
		logger.WithError(err).Error("Failed to marshal payload")
		return
	}

	task.Payload = payloadBytes

	// Dispatch task
	ctx := context.Background()
	if cronjob.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(cronjob.Timeout)*time.Second)
		defer cancel()
	}

	if err := m.dispatch.Dispatch(ctx, task); err != nil {
		logger.WithError(err).Error("Failed to dispatch task")
		return
	}

	logger.WithField("task_id", task.ID).Info("Cronjob executed successfully")

	// Update next run time
	m.mu.RLock()
	entryID, exists := m.jobs[name]
	m.mu.RUnlock()

	if exists {
		entry := m.cron.Entry(entryID)
		if err := m.storage.UpdateNextRun(name, entry.Next); err != nil {
			logger.WithError(err).Warn("Failed to update next run time")
		}
	}
}

// processVariables processes variables in the payload
func (m *Manager) processVariables(cronjob *Cronjob) (map[string]interface{}, error) {
	// If no variables, return payload as is
	if len(cronjob.Variables) == 0 {
		return cronjob.Payload, nil
	}

	// TODO: Implement variable processing with JavaScript VM
	// For now, just return the payload as is
	return cronjob.Payload, nil
}

// CreateCronjob creates a new cronjob
func (m *Manager) CreateCronjob(cronjob *Cronjob) error {
	// Validate cronjob
	if cronjob.Name == "" {
		return fmt.Errorf("cronjob name is required")
	}
	if cronjob.Schedule == "" {
		return fmt.Errorf("cronjob schedule is required")
	}
	if cronjob.WorkerType == "" {
		return fmt.Errorf("cronjob worker type is required")
	}

	// Validate payload for worker type
	adapter, err := GetPayloadAdapter(cronjob.WorkerType)
	if err != nil {
		return fmt.Errorf("invalid worker type: %w", err)
	}

	// Validate payload
	if err := adapter.Validate(cronjob.Payload); err != nil {
		return fmt.Errorf("invalid payload for worker type %s: %w", cronjob.WorkerType, err)
	}

	// Adapt payload to match worker expectations
	adaptedPayload, err := adapter.Adapt(cronjob.Payload)
	if err != nil {
		return fmt.Errorf("failed to adapt payload: %w", err)
	}

	// Update payload with adapted version
	cronjob.Payload = adaptedPayload

	// Save cronjob to Redis
	if err := m.storage.SaveCronjob(cronjob); err != nil {
		return fmt.Errorf("failed to save cronjob: %w", err)
	}

	// Schedule cronjob if enabled
	if cronjob.Enabled {
		if err := m.scheduleCronjob(cronjob); err != nil {
			return fmt.Errorf("failed to schedule cronjob: %w", err)
		}
	}

	return nil
}

// UpdateCronjob updates an existing cronjob
func (m *Manager) UpdateCronjob(cronjob *Cronjob) error {
	// Check if cronjob exists
	_, err := m.storage.GetCronjob(cronjob.Name)
	if err != nil {
		return fmt.Errorf("cronjob not found: %w", err)
	}

	// Validate payload for worker type
	adapter, err := GetPayloadAdapter(cronjob.WorkerType)
	if err != nil {
		return fmt.Errorf("invalid worker type: %w", err)
	}

	// Validate payload
	if err := adapter.Validate(cronjob.Payload); err != nil {
		return fmt.Errorf("invalid payload for worker type %s: %w", cronjob.WorkerType, err)
	}

	// Adapt payload to match worker expectations
	adaptedPayload, err := adapter.Adapt(cronjob.Payload)
	if err != nil {
		return fmt.Errorf("failed to adapt payload: %w", err)
	}

	// Update payload with adapted version
	cronjob.Payload = adaptedPayload

	// Save cronjob to Redis
	if err := m.storage.SaveCronjob(cronjob); err != nil {
		return fmt.Errorf("failed to save cronjob: %w", err)
	}

	// Update schedule if enabled
	if cronjob.Enabled {
		if err := m.scheduleCronjob(cronjob); err != nil {
			return fmt.Errorf("failed to schedule cronjob: %w", err)
		}
	} else {
		// Remove from scheduler if disabled
		m.mu.Lock()
		if entryID, exists := m.jobs[cronjob.Name]; exists {
			m.cron.Remove(entryID)
			delete(m.jobs, cronjob.Name)
		}
		m.mu.Unlock()
	}

	return nil
}

// DeleteCronjob deletes a cronjob
func (m *Manager) DeleteCronjob(name string) error {
	// Remove from scheduler
	m.mu.Lock()
	if entryID, exists := m.jobs[name]; exists {
		m.cron.Remove(entryID)
		delete(m.jobs, name)
	}
	m.mu.Unlock()

	// Delete from Redis
	if err := m.storage.DeleteCronjob(name); err != nil {
		return fmt.Errorf("failed to delete cronjob: %w", err)
	}

	return nil
}

// GetCronjob gets a cronjob by name
func (m *Manager) GetCronjob(name string) (*Cronjob, error) {
	return m.storage.GetCronjob(name)
}

// ListCronjobs lists all cronjobs
func (m *Manager) ListCronjobs() ([]*Cronjob, error) {
	return m.storage.ListCronjobs()
}

// ListEnabledCronjobs lists all enabled cronjobs
func (m *Manager) ListEnabledCronjobs() ([]*Cronjob, error) {
	return m.storage.ListEnabledCronjobs()
}

// EnableCronjob enables a cronjob
func (m *Manager) EnableCronjob(name string) error {
	// Get cronjob
	cronjob, err := m.storage.GetCronjob(name)
	if err != nil {
		return fmt.Errorf("cronjob not found: %w", err)
	}

	// Enable cronjob
	cronjob.Enabled = true

	// Save cronjob
	if err := m.storage.SaveCronjob(cronjob); err != nil {
		return fmt.Errorf("failed to save cronjob: %w", err)
	}

	// Schedule cronjob
	if err := m.scheduleCronjob(cronjob); err != nil {
		return fmt.Errorf("failed to schedule cronjob: %w", err)
	}

	return nil
}

// DisableCronjob disables a cronjob
func (m *Manager) DisableCronjob(name string) error {
	// Get cronjob
	cronjob, err := m.storage.GetCronjob(name)
	if err != nil {
		return fmt.Errorf("cronjob not found: %w", err)
	}

	// Disable cronjob
	cronjob.Enabled = false

	// Save cronjob
	if err := m.storage.SaveCronjob(cronjob); err != nil {
		return fmt.Errorf("failed to save cronjob: %w", err)
	}

	// Remove from scheduler
	m.mu.Lock()
	if entryID, exists := m.jobs[name]; exists {
		m.cron.Remove(entryID)
		delete(m.jobs, name)
	}
	m.mu.Unlock()

	return nil
}

// ExecuteCronjobNow executes a cronjob immediately
func (m *Manager) ExecuteCronjobNow(name string) error {
	// Check if cronjob exists
	_, err := m.storage.GetCronjob(name)
	if err != nil {
		return fmt.Errorf("cronjob not found: %w", err)
	}

	// Execute cronjob
	go m.executeCronjob(name)

	return nil
}
