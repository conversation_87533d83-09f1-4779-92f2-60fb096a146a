package cronjob

import (
	"time"
)

// Cronjob represents a scheduled job configuration
type Cronjob struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Schedule    string                 `json:"schedule"`
	WorkerType  string                 `json:"worker_type"`
	Payload     map[string]interface{} `json:"payload"`
	Variables   map[string]string      `json:"variables"`
	RetryPolicy RetryPolicy            `json:"retry_policy"`
	Timeout     int                    `json:"timeout"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	LastRun     time.Time              `json:"last_run,omitempty"`
	NextRun     time.Time              `json:"next_run,omitempty"`
}

// RetryPolicy defines how to handle retries for failed cronjobs
type RetryPolicy struct {
	Attempts int `json:"attempts"`
	Delay    int `json:"delay"` // in seconds
}

// Storage defines the interface for cronjob storage
type Storage interface {
	SaveCronjob(cronjob *Cronjob) error
	GetCronjob(name string) (*Cronjob, error)
	ListCronjobs() ([]*Cronjob, error)
	ListEnabledCronjobs() ([]*Cronjob, error)
	DeleteCronjob(name string) error
	EnableCronjob(name string) error
	DisableCronjob(name string) error
	UpdateLastRun(name string, time time.Time) error
	UpdateNextRun(name string, time time.Time) error
}
