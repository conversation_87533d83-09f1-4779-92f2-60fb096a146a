package queue

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/user/workers/config"
	"github.com/user/workers/pkg/logger"
)

// RabbitMQ represents a RabbitMQ client
type RabbitMQ struct {
	conn             *amqp.Connection
	channel          *amqp.Channel
	queues           map[string]amqp.Queue
	log              *logger.Logger
	config           *config.Config
	workersConfig    *config.WorkersConfig
	connCloseChan    chan *amqp.Error
	channelCloseChan chan *amqp.Error
	reconnectMutex   sync.RWMutex
	isReconnecting   bool
}

// NewRabbitMQ creates a new RabbitMQ client
func NewRabbitMQ(cfg *config.Config, workersConfig *config.WorkersConfig, log *logger.Logger) (*RabbitMQ, error) {
	// Create RabbitMQ client
	client := &RabbitMQ{
		log:            log,
		config:         cfg,
		workersConfig:  workersConfig,
		isReconnecting: false,
		queues:         make(map[string]amqp.Queue),
	}

	// Connect to RabbitMQ
	if err := client.connect(); err != nil {
		return nil, err
	}

	// Start monitoring for connection and channel closures
	go client.monitorConnection()

	return client, nil
}

// connect connects to RabbitMQ
func (r *RabbitMQ) connect() error {
	r.reconnectMutex.Lock()
	defer r.reconnectMutex.Unlock()

	// Close existing connection and channel if they exist
	if r.conn != nil {
		_ = r.conn.Close()
	}

	// Build connection string
	// Use hardcoded values for testing
	connStr := fmt.Sprintf("amqp://%s:%s@%s:%s/",
		r.config.RabbitMQ.User,
		r.config.RabbitMQ.Password,
		r.config.RabbitMQ.Host,
		r.config.RabbitMQ.Port,
	)

	r.log.Debug("Connecting to RabbitMQ")

	// Connect to RabbitMQ
	var err error
	r.conn, err = amqp.Dial(connStr)
	if err != nil {
		return fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	// Create notification channel for connection closure
	r.connCloseChan = make(chan *amqp.Error)
	r.conn.NotifyClose(r.connCloseChan)

	// Create channel
	r.channel, err = r.conn.Channel()
	if err != nil {
		return fmt.Errorf("failed to open a channel: %w", err)
	}

	// Create notification channel for channel closure
	r.channelCloseChan = make(chan *amqp.Error)
	r.channel.NotifyClose(r.channelCloseChan)

	// Declare queues
	var queues []string

	// If workers config is available, use it to determine queues
	if r.workersConfig != nil {
		// Get all queues from workers config
		queues = r.workersConfig.GetAllQueues()
		r.log.WithField("queues", queues).Info("Using queues from workers configuration")
	} else {
		// Fallback to legacy queue configuration
		queues = []string{
			r.config.RabbitMQ.APIPullQueue,
			r.config.RabbitMQ.APICaveoPullQueue,
			r.config.RabbitMQ.DBCloneQueue,
		}
		r.log.Warn("Workers configuration not available, using legacy queue configuration")
	}

	r.queues = make(map[string]amqp.Queue)
	for _, queueName := range queues {
		// Skip empty queue names
		if queueName == "" {
			continue
		}

		q, err := r.channel.QueueDeclare(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		if err != nil {
			return fmt.Errorf("failed to declare queue %s: %w", queueName, err)
		}
		r.queues[queueName] = q
		r.log.Infof("Declared queue: %s", q.Name)
	}

	// Set QoS
	err = r.channel.Qos(
		1,     // prefetch count
		0,     // prefetch size
		false, // global
	)
	if err != nil {
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	r.log.Info("Connected to RabbitMQ")
	return nil
}

// Consume consumes messages from the queue
func (r *RabbitMQ) Consume(ctx context.Context, queueName string) (<-chan amqp.Delivery, error) {
	// Check if channel is closed and try to reconnect
	if r.channel == nil || r.channel.IsClosed() {
		r.log.Warn("Channel is closed, attempting to reconnect")
		if err := r.reconnect(); err != nil {
			return nil, fmt.Errorf("failed to reconnect: %w", err)
		}
	}

	// Check if queue exists
	queue, exists := r.queues[queueName]
	if !exists {
		return nil, fmt.Errorf("queue %s not found", queueName)
	}

	// Start consuming with retry logic
	maxRetries := 2
	var lastErr error

	for retry := 0; retry < maxRetries; retry++ {
		// Start consuming
		msgs, err := r.channel.Consume(
			queue.Name, // queue
			"",         // consumer
			false,      // auto-ack
			false,      // exclusive
			false,      // no-local
			false,      // no-wait
			nil,        // args
		)
		if err == nil {
			return msgs, nil
		}

		lastErr = err
		r.log.WithError(err).WithField("retry", retry+1).Warn("Failed to register consumer, retrying")

		// If channel is closed, try to reconnect
		if r.channel == nil || r.channel.IsClosed() {
			r.log.Warn("Channel is closed, attempting to reconnect")
			if err := r.reconnect(); err != nil {
				return nil, fmt.Errorf("failed to reconnect: %w", err)
			}
		} else {
			// Wait before retrying
			time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
		}
	}

	return nil, fmt.Errorf("failed to register a consumer after %d retries: %w", maxRetries, lastErr)
}

// Publish publishes a message to the specified queue
func (r *RabbitMQ) Publish(ctx context.Context, queueName string, body []byte) error {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Check if channel is closed and try to reconnect
	if r.channel == nil || r.channel.IsClosed() {
		r.log.Warn("Channel is closed, attempting to reconnect")
		if err := r.reconnect(); err != nil {
			return fmt.Errorf("failed to reconnect: %w", err)
		}
	}

	// Check if queue exists
	queue, exists := r.queues[queueName]
	if !exists {
		return fmt.Errorf("queue %s not found", queueName)
	}

	// Publish message with retry logic
	maxRetries := 2
	for retry := 0; retry < maxRetries; retry++ {
		// Publish message
		err := r.channel.PublishWithContext(
			ctx,
			"",         // exchange
			queue.Name, // routing key
			false,      // mandatory
			false,      // immediate
			amqp.Publishing{
				ContentType:  "application/json",
				Body:         body,
				DeliveryMode: amqp.Persistent,
			},
		)
		if err == nil {
			return nil
		}

		r.log.WithError(err).WithField("retry", retry+1).Warn("Failed to publish message, retrying")

		// If channel is closed, try to reconnect
		if r.channel == nil || r.channel.IsClosed() {
			r.log.Warn("Channel is closed, attempting to reconnect")
			if err := r.reconnect(); err != nil {
				return fmt.Errorf("failed to reconnect: %w", err)
			}
		} else {
			// Wait before retrying
			time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond)
		}
	}

	return fmt.Errorf("failed to publish a message after %d retries", maxRetries)
}

// monitorConnection monitors the connection and channel for closures
func (r *RabbitMQ) monitorConnection() {
	// Create a unique monitor ID for tracking this monitor in logs
	monitorID := fmt.Sprintf("monitor-%d", time.Now().UnixNano())
	r.log.WithField("monitor_id", monitorID).Info("Starting RabbitMQ connection monitor")

	// Handle panics to prevent the monitor from stopping
	defer func() {
		if p := recover(); p != nil {
			r.log.WithFields(map[string]interface{}{
				"monitor_id": monitorID,
				"panic":      p,
			}).Error("Panic in connection monitor, restarting")

			// Restart the monitor after a short delay
			time.Sleep(1 * time.Second)
			go r.monitorConnection()
		}
	}()

	// Track consecutive errors to implement circuit breaker pattern
	consecutiveErrors := 0
	maxConsecutiveErrors := 5
	backoffTime := 1 * time.Second
	maxBackoffTime := 30 * time.Second

	for {
		select {
		case err, ok := <-r.connCloseChan:
			if !ok {
				r.log.WithField("monitor_id", monitorID).Warn("Connection close channel was closed, creating new monitor")
				// Channel was closed, restart the monitor
				go r.monitorConnection()
				return
			}

			if err != nil {
				r.log.WithError(err).WithField("monitor_id", monitorID).Error("RabbitMQ connection closed unexpectedly")

				// Implement circuit breaker pattern
				if consecutiveErrors >= maxConsecutiveErrors {
					r.log.WithFields(map[string]interface{}{
						"monitor_id":         monitorID,
						"consecutive_errors": consecutiveErrors,
						"max_errors":         maxConsecutiveErrors,
						"backoff_time":       backoffTime.String(),
					}).Warn("Circuit breaker triggered, backing off before reconnection attempt")

					time.Sleep(backoffTime)

					// Increase backoff time for next failure
					backoffTime *= 2
					if backoffTime > maxBackoffTime {
						backoffTime = maxBackoffTime
					}
				}

				if reconnectErr := r.reconnect(); reconnectErr != nil {
					consecutiveErrors++
					r.log.WithError(reconnectErr).WithFields(map[string]interface{}{
						"monitor_id":         monitorID,
						"consecutive_errors": consecutiveErrors,
					}).Error("Failed to reconnect after connection closure")
				} else {
					// Reset consecutive errors on successful reconnection
					consecutiveErrors = 0
					backoffTime = 1 * time.Second
				}
			}
		case err, ok := <-r.channelCloseChan:
			if !ok {
				r.log.WithField("monitor_id", monitorID).Warn("Channel close channel was closed, creating new monitor")
				// Channel was closed, restart the monitor
				go r.monitorConnection()
				return
			}

			if err != nil {
				r.log.WithError(err).WithField("monitor_id", monitorID).Error("RabbitMQ channel closed unexpectedly")

				// Implement circuit breaker pattern
				if consecutiveErrors >= maxConsecutiveErrors {
					r.log.WithFields(map[string]interface{}{
						"monitor_id":         monitorID,
						"consecutive_errors": consecutiveErrors,
						"max_errors":         maxConsecutiveErrors,
						"backoff_time":       backoffTime.String(),
					}).Warn("Circuit breaker triggered, backing off before reconnection attempt")

					time.Sleep(backoffTime)

					// Increase backoff time for next failure
					backoffTime *= 2
					if backoffTime > maxBackoffTime {
						backoffTime = maxBackoffTime
					}
				}

				if reconnectErr := r.reconnect(); reconnectErr != nil {
					consecutiveErrors++
					r.log.WithError(reconnectErr).WithFields(map[string]interface{}{
						"monitor_id":         monitorID,
						"consecutive_errors": consecutiveErrors,
					}).Error("Failed to reconnect after channel closure")
				} else {
					// Reset consecutive errors on successful reconnection
					consecutiveErrors = 0
					backoffTime = 1 * time.Second
				}
			}
		}
	}
}

// reconnect attempts to reconnect to RabbitMQ with exponential backoff
func (r *RabbitMQ) reconnect() error {
	// Create a unique reconnection ID for tracking this reconnection in logs
	reconnectID := fmt.Sprintf("reconnect-%d", time.Now().UnixNano())

	// Prevent multiple reconnection attempts
	r.reconnectMutex.Lock()
	if r.isReconnecting {
		r.log.WithField("reconnect_id", reconnectID).Warn("Reconnection already in progress, skipping new attempt")
		r.reconnectMutex.Unlock()
		return fmt.Errorf("reconnection already in progress")
	}
	r.isReconnecting = true
	r.reconnectMutex.Unlock()

	// Ensure we reset the reconnecting flag when we're done
	defer func() {
		r.reconnectMutex.Lock()
		r.isReconnecting = false
		r.reconnectMutex.Unlock()

		// Also handle any panics that might occur during reconnection
		if p := recover(); p != nil {
			r.log.WithField("reconnect_id", reconnectID).WithField("panic", p).Error("Panic occurred during reconnection")
		}
	}()

	// Exponential backoff for reconnection attempts
	backoff := 1 * time.Second
	maxBackoff := 30 * time.Second
	maxAttempts := 10
	attempt := 0

	r.log.WithFields(map[string]interface{}{
		"reconnect_id": reconnectID,
		"max_attempts": maxAttempts,
		"max_backoff":  maxBackoff.String(),
	}).Info("Starting reconnection process")

	for attempt < maxAttempts {
		attempt++
		r.log.WithFields(map[string]interface{}{
			"reconnect_id": reconnectID,
			"attempt":      attempt,
			"backoff":      backoff.String(),
			"max_attempts": maxAttempts,
		}).Info("Attempting to reconnect to RabbitMQ")

		if err := r.connect(); err != nil {
			r.log.WithError(err).WithFields(map[string]interface{}{
				"reconnect_id": reconnectID,
				"attempt":      attempt,
				"max_attempts": maxAttempts,
			}).Error("Failed to reconnect to RabbitMQ")

			// Check if we've reached the maximum number of attempts
			if attempt >= maxAttempts {
				break
			}

			// Wait before next attempt with exponential backoff
			r.log.WithFields(map[string]interface{}{
				"reconnect_id": reconnectID,
				"backoff":      backoff.String(),
				"attempt":      attempt,
			}).Info("Waiting before next reconnection attempt")

			time.Sleep(backoff)

			// Calculate next backoff with jitter to avoid thundering herd problem
			jitter := time.Duration(rand.Int63n(int64(backoff / 4)))
			backoff = backoff*2 + jitter

			if backoff > maxBackoff {
				backoff = maxBackoff
			}
		} else {
			// Calculate the time taken for reconnection
			startTime := time.Now().Add(-time.Duration(attempt) * backoff)
			r.log.WithFields(map[string]interface{}{
				"reconnect_id": reconnectID,
				"attempt":      attempt,
				"total_time":   time.Since(startTime).String(),
			}).Info("Successfully reconnected to RabbitMQ")
			return nil
		}
	}

	err := fmt.Errorf("failed to reconnect to RabbitMQ after %d attempts", maxAttempts)
	// Calculate approximate total time spent trying to reconnect
	totalTime := time.Duration(attempt) * backoff / 2 // Rough estimate
	r.log.WithFields(map[string]interface{}{
		"reconnect_id": reconnectID,
		"max_attempts": maxAttempts,
		"attempts":     attempt,
		"total_time":   totalTime.String(),
	}).Error(err.Error())
	return err
}

// Close closes the connection to RabbitMQ
func (r *RabbitMQ) Close() error {
	if r.channel != nil {
		if err := r.channel.Close(); err != nil {
			r.log.WithError(err).Error("Failed to close RabbitMQ channel")
		}
	}

	if r.conn != nil {
		if err := r.conn.Close(); err != nil {
			r.log.WithError(err).Error("Failed to close RabbitMQ connection")
			return err
		}
	}

	r.log.Info("RabbitMQ connection closed")
	return nil
}

// IsChannelOpen checks if the channel is open
func (r *RabbitMQ) IsChannelOpen() bool {
	r.reconnectMutex.RLock()
	defer r.reconnectMutex.RUnlock()
	return r.channel != nil && !r.channel.IsClosed()
}

// UpdateQueues updates the queues based on the workers configuration
func (r *RabbitMQ) UpdateQueues(workersConfig *config.WorkersConfig) error {
	r.log.Info("Updating queues based on workers configuration")

	// Update workers config
	r.workersConfig = workersConfig

	// Check if channel is closed and try to reconnect
	if r.channel == nil || r.channel.IsClosed() {
		r.log.Warn("Channel is closed, attempting to reconnect")
		if err := r.reconnect(); err != nil {
			return fmt.Errorf("failed to reconnect: %w", err)
		}
	}

	// Get all queues from workers config
	queues := workersConfig.GetAllQueues()
	r.log.WithField("queues", queues).Info("Updating queues from workers configuration")

	// Declare any new queues
	for _, queueName := range queues {
		// Skip empty queue names
		if queueName == "" {
			continue
		}

		// Check if queue already exists
		if _, exists := r.queues[queueName]; exists {
			r.log.WithField("queue", queueName).Debug("Queue already exists, skipping declaration")
			continue
		}

		// Declare the queue
		q, err := r.channel.QueueDeclare(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		if err != nil {
			return fmt.Errorf("failed to declare queue %s: %w", queueName, err)
		}
		r.queues[queueName] = q
		r.log.Infof("Declared new queue: %s", q.Name)
	}

	return nil
}

// SafeChannelOp safely performs an operation on the channel, reconnecting if necessary
func (r *RabbitMQ) SafeChannelOp(op func(*amqp.Channel) error) error {
	// Create a unique operation ID for tracking this operation in logs
	opID := fmt.Sprintf("op-%d", time.Now().UnixNano())

	// Check if channel is closed and try to reconnect
	if !r.IsChannelOpen() {
		r.log.WithField("operation_id", opID).Warn("Channel is closed, attempting to reconnect before operation")
		if err := r.reconnect(); err != nil {
			r.log.WithError(err).WithField("operation_id", opID).Error("Failed to reconnect before operation")
			return fmt.Errorf("failed to reconnect before operation: %w", err)
		}
		r.log.WithField("operation_id", opID).Info("Successfully reconnected before operation")
	}

	// Double-check that the channel is open
	if !r.IsChannelOpen() {
		err := fmt.Errorf("channel is still closed after reconnection attempt")
		r.log.WithField("operation_id", opID).Error(err.Error())
		return err
	}

	// Perform the operation with detailed logging
	r.log.WithField("operation_id", opID).Debug("Performing channel operation")
	err := op(r.channel)

	if err != nil {
		r.log.WithError(err).WithField("operation_id", opID).Warn("Channel operation failed")

		// If the channel is closed, try to reconnect and retry once
		if r.channel != nil && r.channel.IsClosed() {
			r.log.WithField("operation_id", opID).Warn("Channel closed during operation, attempting to reconnect and retry")

			if reconnectErr := r.reconnect(); reconnectErr != nil {
				r.log.WithError(reconnectErr).WithField("operation_id", opID).Error("Failed to reconnect after channel closure")
				return fmt.Errorf("failed to reconnect after channel closure: %w (original error: %v)", reconnectErr, err)
			}

			r.log.WithField("operation_id", opID).Info("Successfully reconnected after channel closure, retrying operation")

			// Double-check that the channel is open after reconnection
			if !r.IsChannelOpen() {
				retryErr := fmt.Errorf("channel is still closed after reconnection retry")
				r.log.WithField("operation_id", opID).Error(retryErr.Error())
				return fmt.Errorf("%w (original error: %v)", retryErr, err)
			}

			// Retry the operation
			retryErr := op(r.channel)
			if retryErr != nil {
				r.log.WithError(retryErr).WithField("operation_id", opID).Error("Operation failed after reconnect")
				return fmt.Errorf("operation failed after reconnect: %w (original error: %v)", retryErr, err)
			}

			r.log.WithField("operation_id", opID).Info("Operation succeeded after reconnect")
			return nil
		}

		return fmt.Errorf("operation failed: %w", err)
	}

	r.log.WithField("operation_id", opID).Debug("Channel operation completed successfully")
	return nil
}
