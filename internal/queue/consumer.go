package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/user/workers/config"
	"github.com/user/workers/internal/worker"
	"github.com/user/workers/pkg/logger"
)

// min returns the smaller of x or y.
func min(x, y int) int {
	if x < y {
		return x
	}
	return y
}

// TaskStatus represents the status of a task
type TaskStatus string

const (
	// TaskStatusProcessing indicates that the task is being processed
	TaskStatusProcessing TaskStatus = "processing"
	// TaskStatusCompleted indicates that the task has been completed successfully
	TaskStatusCompleted TaskStatus = "completed"
	// TaskStatusFailed indicates that the task has failed
	TaskStatusFailed TaskStatus = "failed"
	// TaskStatusTimeout indicates that the task has timed out
	TaskStatusTimeout TaskStatus = "timeout"
)

// TaskState represents the state of a task
type TaskState struct {
	Status       TaskStatus
	DeliveryTag  uint64
	Acknowledged bool
	StartTime    time.Time
	EndTime      time.Time
}

// AckType represents the type of acknowledgment
type AckType int

const (
	// AckTypeAck indicates a positive acknowledgment
	AckTypeAck AckType = iota
	// AckTypeNack indicates a negative acknowledgment
	AckTypeNack
)

// AckRequest represents a request to acknowledge a message
type AckRequest struct {
	TaskID      string
	DeliveryTag uint64
	Type        AckType
	Requeue     bool
	Msg         amqp.Delivery
	WorkerType  worker.WorkerType
}

// Consumer represents a RabbitMQ consumer
type Consumer struct {
	rabbitMQ      *RabbitMQ
	pool          *worker.Pool
	log           *logger.Logger
	wg            sync.WaitGroup
	ctx           context.Context
	cancel        context.CancelFunc
	config        *config.Config
	workersConfig *config.WorkersConfig
	taskStates    map[string]*TaskState // Map of task ID to task state
	taskStateMu   sync.RWMutex          // Mutex to protect the task state map
	activeTasks   int                   // Counter for active tasks
	activeTasksMu sync.RWMutex          // Mutex to protect the active tasks counter

	// Worker-specific acknowledgment channels
	ackChannels   map[worker.WorkerType]chan AckRequest // Map of worker type to acknowledgment channel
	ackChannelsMu sync.RWMutex                          // Mutex to protect the acknowledgment channels map
}

// NewConsumer creates a new consumer
func NewConsumer(ctx context.Context, rabbitMQ *RabbitMQ, pool *worker.Pool, log *logger.Logger, config *config.Config, workersConfig *config.WorkersConfig) *Consumer {
	ctx, cancel := context.WithCancel(ctx)

	// Create a new consumer
	consumer := &Consumer{
		rabbitMQ:      rabbitMQ,
		pool:          pool,
		log:           log,
		ctx:           ctx,
		cancel:        cancel,
		config:        config,
		workersConfig: workersConfig,
		taskStates:    make(map[string]*TaskState),
		taskStateMu:   sync.RWMutex{},
		activeTasks:   0,
		activeTasksMu: sync.RWMutex{},
		ackChannels:   make(map[worker.WorkerType]chan AckRequest),
		ackChannelsMu: sync.RWMutex{},
	}

	// Initialize acknowledgment channels for each worker type
	// Use a buffer size that's large enough to prevent blocking
	const ackChannelBufferSize = 100

	// Get all registered worker types
	for workerType := range pool.GetRegisteredWorkers() {
		consumer.ackChannelsMu.Lock()
		consumer.ackChannels[workerType] = make(chan AckRequest, ackChannelBufferSize)
		consumer.ackChannelsMu.Unlock()

		// Start a dedicated goroutine to handle acknowledgments for this worker type
		consumer.wg.Add(1)
		go consumer.handleAcknowledgments(workerType)
	}

	return consumer
}

// Start starts the consumer
func (c *Consumer) Start() error {
	c.log.Info("Starting consumer")

	// Determine which queue to consume based on worker type
	var queueNames []string

	// If workers config is available, use it to determine the queues
	if c.workersConfig != nil {
		// Get all queues from workers config
		for workerType := range c.pool.GetRegisteredWorkers() {
			// Map worker type to config key
			var configKey string
			switch workerType {
			case worker.APIWorker:
				configKey = "api_pull"
			case worker.APICaveoWorker:
				configKey = "api_pull_caveo"
			case worker.DBWorker:
				configKey = "db_clone"
			case "caveo_payload_maker":
				configKey = "caveo_payload_maker"
			case worker.RescheduleCaveoWorker:
				configKey = "reschedule_caveo"
			case "caveo_to_influx":
				configKey = "caveo_to_influx"
			case worker.GeofDivisionWorkerType:
				configKey = "geof_division"
			case worker.GeofEstateWorkerType:
				configKey = "geof_estate"
			case worker.GeofBlockWorkerType:
				configKey = "geof_block"
			default:
				continue
			}

			// Get worker config
			workerConfig, exists := c.workersConfig.GetWorkerConfig(configKey)
			if exists && workerConfig.Enabled && workerConfig.Queue != "" {
				queueNames = append(queueNames, workerConfig.Queue)
				c.log.WithFields(map[string]interface{}{
					"worker_type": configKey,
					"queue":       workerConfig.Queue,
				})
			}
		}
	} else {
		// Fallback to legacy queue configuration
		for workerType := range c.pool.GetRegisteredWorkers() {
			switch workerType {
			case worker.APIWorker:
				if c.config.RabbitMQ.APIPullQueue != "" {
					queueNames = append(queueNames, c.config.RabbitMQ.APIPullQueue)
				}
			case worker.APICaveoWorker:
				if c.config.RabbitMQ.APICaveoPullQueue != "" {
					queueNames = append(queueNames, c.config.RabbitMQ.APICaveoPullQueue)
				}
			case worker.DBWorker:
				if c.config.RabbitMQ.DBCloneQueue != "" {
					queueNames = append(queueNames, c.config.RabbitMQ.DBCloneQueue)
				}
			case "caveo_payload_maker":
				queueNames = append(queueNames, "caveo_payload_maker_tasks")
			case worker.RescheduleCaveoWorker:
				queueNames = append(queueNames, "reschedule_caveo_tasks")
			case "caveo_to_influx":
				queueNames = append(queueNames, "caveo_to_influx_tasks")
			case worker.GeofDivisionWorkerType:
				queueNames = append(queueNames, "geof_division_tasks")
			case worker.GeofEstateWorkerType:
				queueNames = append(queueNames, "geof_estate_tasks")
			case worker.GeofBlockWorkerType:
				queueNames = append(queueNames, "geof_block_tasks")
			}
		}
	}

	// Start task state cleanup goroutine
	c.wg.Add(1)
	go c.cleanupTaskStates()

	// Start consumers for each queue
	for _, queueName := range queueNames {
		// Skip empty queue names
		if queueName == "" {
			continue
		}

		// Consume messages with retry logic
		maxRetries := 2
		var lastErr error

		for retry := 0; retry < maxRetries; retry++ {
			msgs, err := c.rabbitMQ.Consume(c.ctx, queueName)
			if err == nil {
				// Start consumer goroutine
				c.wg.Add(1)
				go c.consume(msgs, queueName)
				break
			}

			lastErr = err
			c.log.WithError(err).WithField("retry", retry+1).WithField("queue", queueName).Warn("Failed to start consumer, retrying")

			// Wait before retrying
			time.Sleep(time.Duration(retry+1) * time.Second)
		}

		if lastErr != nil {
			return fmt.Errorf("failed to start consumer for queue %s after %d retries: %w", queueName, maxRetries, lastErr)
		}
	}

	return nil
}

// cleanupTaskStates periodically cleans up old task states to prevent memory leaks
func (c *Consumer) cleanupTaskStates() {
	defer c.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			c.log.Info("Task state cleanup goroutine shutting down")
			return
		case <-ticker.C:
			c.cleanupOldTaskStates()
		}
	}
}

// cleanupOldTaskStates removes task states that are older than a certain threshold
func (c *Consumer) cleanupOldTaskStates() {
	c.taskStateMu.Lock()
	defer c.taskStateMu.Unlock()

	// Remove task states that are older than 1 hour and have been completed or failed
	threshold := time.Now().Add(-1 * time.Hour)
	var tasksToRemove []string

	for taskID, state := range c.taskStates {
		// Only remove tasks that have been completed, failed, or timed out
		if (state.Status == TaskStatusCompleted || state.Status == TaskStatusFailed || state.Status == TaskStatusTimeout) &&
			state.EndTime.Before(threshold) {
			tasksToRemove = append(tasksToRemove, taskID)
		}
	}

	// Remove the tasks
	for _, taskID := range tasksToRemove {
		delete(c.taskStates, taskID)
	}

	if len(tasksToRemove) > 0 {
		c.log.WithField("removed_count", len(tasksToRemove)).Info("Removed old task states")
	}
}

// consume consumes messages from the queue
func (c *Consumer) consume(msgs <-chan amqp.Delivery, queueName string) {
	defer c.wg.Done()

	for {
		select {
		case <-c.ctx.Done():
			c.log.Info("Consumer shutting down")
			return
		case msg, ok := <-msgs:
			if !ok {
				c.log.Warn("RabbitMQ channel closed, attempting to reconnect")

				// Try to reconnect
				for i := 0; i < 5; i++ {
					// Check if context is done
					select {
					case <-c.ctx.Done():
						c.log.Info("Consumer shutting down during reconnection attempt")
						return
					default:
						// Continue with reconnection
					}

					// Wait before retrying
					time.Sleep(time.Duration(i+1) * time.Second)

					c.log.WithField("attempt", i+1).Info("Attempting to reconnect consumer")

					// Try to reconnect
					newMsgs, err := c.rabbitMQ.Consume(c.ctx, queueName)
					if err != nil {
						c.log.WithError(err).Error("Failed to reconnect consumer")
						continue
					}

					c.log.Info("Consumer reconnected successfully")

					// Start consuming from new channel
					c.wg.Add(1)
					go c.consume(newMsgs, queueName)
					return
				}

				c.log.Error("Failed to reconnect consumer after multiple attempts")
				return
			}

			// Process message
			c.processMessage(msg)
		}
	}
}

// processMessage processes a message
func (c *Consumer) processMessage(msg amqp.Delivery) {
	c.log.WithFields(map[string]interface{}{
		"delivery_tag": msg.DeliveryTag,
		"exchange":     msg.Exchange,
		"routing_key":  msg.RoutingKey,
	}).Info("Received message")

	// Parse message
	var task worker.Task
	if err := json.Unmarshal(msg.Body, &task); err != nil {
		c.log.WithError(err).WithFields(map[string]interface{}{
			"delivery_tag": msg.DeliveryTag,
			"body_preview": string(msg.Body[:min(len(msg.Body), 100)]), // Show first 100 chars of body
		}).Error("Failed to unmarshal message")

		// Nack message without requeue for malformed messages
		if nackErr := c.safeNack(msg, "malformed", false); nackErr != nil {
			c.log.WithError(nackErr).WithField("delivery_tag", msg.DeliveryTag).Error("Failed to nack malformed message")
		}
		return
	}

	// Generate UUID for the task if it doesn't have an ID
	task.ID = fmt.Sprintf("task-%s", uuid.New().String())

	c.log.WithFields(map[string]interface{}{
		"task_id":          task.ID,
		"task_type":        task.Type,
		"reschedule_count": task.RescheduleCount,
	}).Info("Parsed message into task")

	// Set created at if not set
	if task.CreatedAt == 0 {
		task.CreatedAt = time.Now().Unix()
	}

	// Initialize task state
	taskState := &TaskState{
		Status:       TaskStatusProcessing,
		DeliveryTag:  msg.DeliveryTag,
		Acknowledged: false,
		StartTime:    time.Now(),
	}
	c.setTaskState(task.ID, taskState)

	// Create a context with timeout for this task
	taskCtx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()

	// Track active tasks
	activeTasks := c.incrementActiveTasks()
	c.log.WithFields(map[string]interface{}{
		"task_id":      task.ID,
		"active_tasks": activeTasks,
	}).Debug("Task tracking started")

	// Submit task to worker pool
	c.log.WithField("task_id", task.ID).Info("Submitting task to worker pool")
	c.pool.Submit(&task)

	// Create a channel to receive the result
	resultChan := make(chan worker.TaskResult, 1)

	// Create a done channel to signal when the task is complete
	doneChan := make(chan struct{}, 1)

	// Use a separate goroutine to monitor the results channel
	go func() {
		defer func() {
			// Signal that we're done processing this task
			select {
			case doneChan <- struct{}{}:
			default:
			}
		}()

		// Create a timeout for this goroutine
		timer := time.NewTimer(25 * time.Second) // Slightly less than the task context timeout
		defer timer.Stop()
		for {
			select {
			case <-taskCtx.Done():
				// Context timeout or cancellation
				c.log.WithField("task_id", task.ID).Warn("Task processing timed out or context cancelled in monitor goroutine")
				return
			case <-timer.C:
				// Timeout in this goroutine
				c.log.WithField("task_id", task.ID).Warn("Monitor goroutine timed out waiting for result")

				// Check if the task has already been acknowledged
				if c.isTaskAcknowledged(task.ID) {
					c.log.WithFields(map[string]interface{}{
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Warn("Task already acknowledged, skipping acknowledgment in timeout handler")
					return
				}

				// Update task state to completed
				c.updateTaskStatus(task.ID, TaskStatusCompleted)

				// Send an acknowledgment request to the appropriate channel
				if err := c.sendAcknowledgment(&task, msg, AckTypeAck, false); err != nil {
					c.log.WithError(err).WithFields(map[string]interface{}{
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Error("Failed to send acknowledgment request in timeout handler")

					// Fallback to direct ack if sending to channel fails
					if ackErr := c.safeAck(msg, task.ID); ackErr != nil {
						c.log.WithError(ackErr).WithFields(map[string]interface{}{
							"task_id":      task.ID,
							"delivery_tag": msg.DeliveryTag,
						}).Error("Failed to ack message in timeout handler")
					}
				}

				// Log task tracking ended
				activeTasks := c.decrementActiveTasks()
				c.log.WithFields(map[string]interface{}{
					"task_id":      task.ID,
					"active_tasks": activeTasks,
				}).Debug("Task tracking ended in timeout handler")

				return
			case result, ok := <-c.pool.GetResults():
				if !ok {
					// Result channel closed
					c.log.WithField("task_id", task.ID).Warn("Result channel closed")
					return
				}

				// Check if this is the result for our task
				if result.Task.ID == task.ID {
					c.log.WithField("task_id", task.ID).Info("Received result for task")

					// Send the result to our result channel
					select {
					case resultChan <- result:
						c.log.WithField("task_id", task.ID).Debug("Result sent to result channel")
					default:
						c.log.WithField("task_id", task.ID).Warn("Result channel is full, discarding result")
					}

					// Check if the task has already been acknowledged
					if c.isTaskAcknowledged(task.ID) {
						c.log.WithFields(map[string]interface{}{
							"task_id":      task.ID,
							"delivery_tag": msg.DeliveryTag,
						}).Warn("Task already acknowledged by another goroutine, skipping acknowledgment")

						// Log task tracking ended
						activeTasks := c.decrementActiveTasks()
						c.log.WithFields(map[string]interface{}{
							"task_id":      task.ID,
							"active_tasks": activeTasks,
						}).Debug("Task tracking ended")

						return
					}

					// Update task state based on result
					if result.Error != nil {
						if result.IsTimeout {
							c.updateTaskStatus(task.ID, TaskStatusTimeout)
							c.log.WithField("task_id", task.ID).Info("Timeout error detected, message will be requeued")

							// Send a negative acknowledgment request to the appropriate channel
							if err := c.sendAcknowledgment(result.Task, msg, AckTypeNack, true); err != nil {
								c.log.WithError(err).WithFields(map[string]interface{}{
									"task_id":      task.ID,
									"delivery_tag": msg.DeliveryTag,
								}).Error("Failed to send negative acknowledgment request")
							}
						} else {
							// For other errors, ack the message to remove it from the queue
							// This prevents endless reprocessing of messages that will always fail
							c.updateTaskStatus(task.ID, TaskStatusFailed)
							c.log.WithField("task_id", task.ID).Info("Non-timeout error, acknowledging message to prevent endless reprocessing")

							// Send an acknowledgment request to the appropriate channel
							if err := c.sendAcknowledgment(result.Task, msg, AckTypeAck, false); err != nil {
								c.log.WithError(err).WithFields(map[string]interface{}{
									"task_id":      task.ID,
									"delivery_tag": msg.DeliveryTag,
								}).Error("Failed to send acknowledgment request")
							}
						}
					} else {
						// Task processed successfully, ack the message
						c.updateTaskStatus(task.ID, TaskStatusCompleted)
						c.log.WithFields(map[string]interface{}{
							"task_id":      task.ID,
							"task_type":    task.Type,
							"delivery_tag": msg.DeliveryTag,
						}).Info("Task completed successfully, acknowledging message")

						// Send an acknowledgment request to the appropriate channel
						if err := c.sendAcknowledgment(result.Task, msg, AckTypeAck, false); err != nil {
							c.log.WithError(err).WithFields(map[string]interface{}{
								"task_id":      task.ID,
								"delivery_tag": msg.DeliveryTag,
							}).Error("Failed to send acknowledgment request")
						}
					}

					// Log task tracking ended
					activeTasks := c.decrementActiveTasks()
					c.log.WithFields(map[string]interface{}{
						"task_id":      task.ID,
						"active_tasks": activeTasks,
					}).Debug("Task tracking ended")

					return
				}

				// Not our task, continue waiting
				c.log.WithFields(map[string]interface{}{
					"expected_task_id": task.ID,
					"received_task_id": result.Task.ID,
				}).Debug("Received result for different task, continuing to wait")
			}
		}
	}()

	// Wait for the context to be cancelled (shutdown or timeout), for the result, or for the done signal
	select {
	case <-c.ctx.Done():
		// Consumer is shutting down
		c.log.WithField("task_id", task.ID).Warn("Consumer shutting down, message will be requeued")

		// Only NACK if the task hasn't been acknowledged yet
		if !c.isTaskAcknowledged(task.ID) {
			c.updateTaskStatus(task.ID, TaskStatusFailed)

			// Send a negative acknowledgment request to the appropriate channel
			if err := c.sendAcknowledgment(&task, msg, AckTypeNack, true); err != nil {
				c.log.WithError(err).WithFields(map[string]interface{}{
					"task_id":      task.ID,
					"delivery_tag": msg.DeliveryTag,
				}).Error("Failed to send negative acknowledgment request during shutdown")

				// Fallback to direct nack if sending to channel fails
				if nackErr := c.safeNack(msg, task.ID, true); nackErr != nil {
					c.log.WithError(nackErr).WithFields(map[string]interface{}{
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Error("Failed to nack message during shutdown")
				}
			}
		}
		return
	case <-doneChan:
		// The monitor goroutine has completed processing the task
		c.log.WithField("task_id", task.ID).Debug("Task processing completed via done channel")

		// Check if the task has already been acknowledged
		if c.isTaskAcknowledged(task.ID) {
			c.log.WithField("task_id", task.ID).Info("Task already acknowledged, skipping acknowledgment in done channel handler")
			return
		}

		// Update task state to completed
		c.updateTaskStatus(task.ID, TaskStatusCompleted)

		// Send an acknowledgment request to the appropriate channel
		if err := c.sendAcknowledgment(&task, msg, AckTypeAck, false); err != nil {
			c.log.WithError(err).WithFields(map[string]interface{}{
				"task_id":      task.ID,
				"delivery_tag": msg.DeliveryTag,
			}).Error("Failed to send acknowledgment request in done channel handler")

			// Fallback to direct ack if sending to channel fails
			if ackErr := c.safeAck(msg, task.ID); ackErr != nil {
				c.log.WithError(ackErr).WithFields(map[string]interface{}{
					"task_id":      task.ID,
					"delivery_tag": msg.DeliveryTag,
				}).Error("Failed to ack message in done channel handler")
			}
		}

		// Log task tracking ended
		activeTasks := c.decrementActiveTasks()
		c.log.WithFields(map[string]interface{}{
			"task_id":      task.ID,
			"active_tasks": activeTasks,
		}).Debug("Task tracking ended in done channel handler")

		return
	case <-taskCtx.Done():
		// Task processing timed out
		c.log.WithField("task_id", task.ID).Warn("Task processing timed out, message will be requeued")

		// Only NACK if the task hasn't been acknowledged yet
		if !c.isTaskAcknowledged(task.ID) {
			c.updateTaskStatus(task.ID, TaskStatusTimeout)

			// Send a negative acknowledgment request to the appropriate channel
			if err := c.sendAcknowledgment(&task, msg, AckTypeNack, true); err != nil {
				c.log.WithError(err).WithFields(map[string]interface{}{
					"task_id":      task.ID,
					"delivery_tag": msg.DeliveryTag,
				}).Error("Failed to send negative acknowledgment request during timeout")

				// Fallback to direct nack if sending to channel fails
				if nackErr := c.safeNack(msg, task.ID, true); nackErr != nil {
					c.log.WithError(nackErr).WithFields(map[string]interface{}{
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Error("Failed to nack message during timeout")
				}
			}
		}

		// Log task tracking ended
		activeTasks := c.decrementActiveTasks()
		c.log.WithFields(map[string]interface{}{
			"task_id":      task.ID,
			"active_tasks": activeTasks,
		}).Debug("Task tracking ended")

		return
	case result := <-resultChan:
		// Task processing completed (this is a fallback, as we should have handled this in the goroutine)
		c.log.WithField("task_id", task.ID).Info("Received result from result channel (fallback)")

		// Check if the task has already been acknowledged
		if c.isTaskAcknowledged(task.ID) {
			c.log.WithField("task_id", task.ID).Info("Task already acknowledged, skipping fallback acknowledgment")
		} else {
			// Process the result and send acknowledgment
			if result.Error != nil {
				if result.IsTimeout {
					c.updateTaskStatus(task.ID, TaskStatusTimeout)

					// Send a negative acknowledgment request to the appropriate channel
					if err := c.sendAcknowledgment(result.Task, msg, AckTypeNack, true); err != nil {
						c.log.WithError(err).WithFields(map[string]interface{}{
							"task_id":      task.ID,
							"delivery_tag": msg.DeliveryTag,
						}).Error("Failed to send negative acknowledgment request in fallback path")

						// Fallback to direct nack if sending to channel fails
						if nackErr := c.safeNack(msg, task.ID, true); nackErr != nil {
							c.log.WithError(nackErr).WithFields(map[string]interface{}{
								"task_id":      task.ID,
								"delivery_tag": msg.DeliveryTag,
							}).Error("Failed to nack message in fallback path (timeout)")
						}
					}
				} else {
					c.updateTaskStatus(task.ID, TaskStatusFailed)

					// Send an acknowledgment request to the appropriate channel
					if err := c.sendAcknowledgment(result.Task, msg, AckTypeAck, false); err != nil {
						c.log.WithError(err).WithFields(map[string]interface{}{
							"task_id":      task.ID,
							"delivery_tag": msg.DeliveryTag,
						}).Error("Failed to send acknowledgment request in fallback path")

						// Fallback to direct ack if sending to channel fails
						if ackErr := c.safeAck(msg, task.ID); ackErr != nil {
							c.log.WithError(ackErr).WithFields(map[string]interface{}{
								"task_id":      task.ID,
								"delivery_tag": msg.DeliveryTag,
							}).Error("Failed to ack message in fallback path (error)")
						}
					}
				}
			} else {
				c.updateTaskStatus(task.ID, TaskStatusCompleted)

				// Send an acknowledgment request to the appropriate channel
				if err := c.sendAcknowledgment(result.Task, msg, AckTypeAck, false); err != nil {
					c.log.WithError(err).WithFields(map[string]interface{}{
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Error("Failed to send acknowledgment request in fallback path")

					// Fallback to direct ack if sending to channel fails
					if ackErr := c.safeAck(msg, task.ID); ackErr != nil {
						c.log.WithError(ackErr).WithFields(map[string]interface{}{
							"task_id":      task.ID,
							"delivery_tag": msg.DeliveryTag,
						}).Error("Failed to ack message in fallback path (success)")
					}
				}
			}
		}

		// Log task tracking ended
		activeTasks := c.decrementActiveTasks()
		c.log.WithFields(map[string]interface{}{
			"task_id":      task.ID,
			"active_tasks": activeTasks,
		}).Debug("Task tracking ended")

		return
	}
}

// Shutdown gracefully shuts down the consumer
func (c *Consumer) Shutdown() {
	c.log.Info("Shutting down consumer")

	// Signal consumer to stop
	c.cancel()

	// Close all acknowledgment channels
	c.ackChannelsMu.Lock()
	for workerType, ackChan := range c.ackChannels {
		c.log.WithField("worker_type", workerType).Info("Closing acknowledgment channel")
		close(ackChan)
	}
	c.ackChannelsMu.Unlock()

	// Wait for consumer to finish
	c.wg.Wait()

	c.log.Info("Consumer shutdown complete")
}

// getTaskState gets the state of a task
func (c *Consumer) getTaskState(taskID string) (*TaskState, bool) {
	c.taskStateMu.RLock()
	defer c.taskStateMu.RUnlock()
	state, exists := c.taskStates[taskID]
	return state, exists
}

// setTaskState sets the state of a task
func (c *Consumer) setTaskState(taskID string, state *TaskState) {
	c.taskStateMu.Lock()
	defer c.taskStateMu.Unlock()
	c.taskStates[taskID] = state
}

// updateTaskStatus updates the status of a task
func (c *Consumer) updateTaskStatus(taskID string, status TaskStatus) bool {
	c.taskStateMu.Lock()
	defer c.taskStateMu.Unlock()

	state, exists := c.taskStates[taskID]
	if !exists {
		return false
	}

	state.Status = status
	if status == TaskStatusCompleted || status == TaskStatusFailed || status == TaskStatusTimeout {
		state.EndTime = time.Now()
	}

	return true
}

// markTaskAcknowledged marks a task as acknowledged
func (c *Consumer) markTaskAcknowledged(taskID string) bool {
	c.taskStateMu.Lock()
	defer c.taskStateMu.Unlock()

	state, exists := c.taskStates[taskID]
	if !exists {
		return false
	}

	state.Acknowledged = true
	return true
}

// isTaskAcknowledged checks if a task has been acknowledged
func (c *Consumer) isTaskAcknowledged(taskID string) bool {
	c.taskStateMu.RLock()
	defer c.taskStateMu.RUnlock()

	state, exists := c.taskStates[taskID]
	if !exists {
		return false
	}

	return state.Acknowledged
}

// incrementActiveTasks increments the active tasks counter
func (c *Consumer) incrementActiveTasks() int {
	c.activeTasksMu.Lock()
	defer c.activeTasksMu.Unlock()
	c.activeTasks++
	return c.activeTasks
}

// decrementActiveTasks decrements the active tasks counter
func (c *Consumer) decrementActiveTasks() int {
	c.activeTasksMu.Lock()
	defer c.activeTasksMu.Unlock()
	if c.activeTasks > 0 {
		c.activeTasks--
	}
	return c.activeTasks
}

// getActiveTasks returns the current number of active tasks
func (c *Consumer) getActiveTasks() int {
	c.activeTasksMu.RLock()
	defer c.activeTasksMu.RUnlock()
	return c.activeTasks
}

// handleAcknowledgments handles acknowledgments for a specific worker type
func (c *Consumer) handleAcknowledgments(workerType worker.WorkerType) {
	defer c.wg.Done()

	c.log.WithField("worker_type", workerType).Info("Started acknowledgment handler for worker type")

	// Get the acknowledgment channel for this worker type
	c.ackChannelsMu.RLock()
	ackChan, exists := c.ackChannels[workerType]
	c.ackChannelsMu.RUnlock()

	if !exists {
		c.log.WithField("worker_type", workerType).Error("Acknowledgment channel not found for worker type")
		return
	}

	for {
		select {
		case <-c.ctx.Done():
			c.log.WithField("worker_type", workerType).Info("Acknowledgment handler shutting down")
			return
		case req, ok := <-ackChan:
			if !ok {
				c.log.WithField("worker_type", workerType).Warn("Acknowledgment channel closed")
				return
			}

			// Process the acknowledgment request
			c.log.WithFields(map[string]interface{}{
				"worker_type":  workerType,
				"task_id":      req.TaskID,
				"delivery_tag": req.DeliveryTag,
				"ack_type":     req.Type,
				"requeue":      req.Requeue,
			}).Debug("Processing acknowledgment request")

			// Handle the acknowledgment based on the type
			var err error
			if req.Type == AckTypeAck {
				err = c.safeAck(req.Msg, req.TaskID)
			} else {
				err = c.safeNack(req.Msg, req.TaskID, req.Requeue)
			}

			if err != nil {
				c.log.WithError(err).WithFields(map[string]interface{}{
					"worker_type":  workerType,
					"task_id":      req.TaskID,
					"delivery_tag": req.DeliveryTag,
					"ack_type":     req.Type,
					"requeue":      req.Requeue,
				}).Error("Failed to process acknowledgment request")
			}
		}
	}
}

// GetStats returns statistics about the consumer
// This is a public method that can be called by external code
func (c *Consumer) GetStats() map[string]interface{} {
	c.taskStateMu.RLock()
	c.activeTasksMu.RLock()
	defer c.taskStateMu.RUnlock()
	defer c.activeTasksMu.RUnlock()

	// Count tasks by status
	processing := 0
	completed := 0
	failed := 0
	timeout := 0

	for _, state := range c.taskStates {
		switch state.Status {
		case TaskStatusProcessing:
			processing++
		case TaskStatusCompleted:
			completed++
		case TaskStatusFailed:
			failed++
		case TaskStatusTimeout:
			timeout++
		}
	}

	return map[string]interface{}{
		"active_tasks":     c.activeTasks,
		"total_tasks":      len(c.taskStates),
		"processing_tasks": processing,
		"completed_tasks":  completed,
		"failed_tasks":     failed,
		"timeout_tasks":    timeout,
		"channel_is_open":  c.rabbitMQ.IsChannelOpen(),
	}
}

// sendAcknowledgment sends an acknowledgment request to the appropriate channel
func (c *Consumer) sendAcknowledgment(task *worker.Task, msg amqp.Delivery, ackType AckType, requeue bool) error {
	// Get the worker type from the task
	workerType := task.Type

	// Create the acknowledgment request
	req := AckRequest{
		TaskID:      task.ID,
		DeliveryTag: msg.DeliveryTag,
		Type:        ackType,
		Requeue:     requeue,
		Msg:         msg,
		WorkerType:  workerType,
	}

	// Get the acknowledgment channel for this worker type
	c.ackChannelsMu.RLock()
	ackChan, exists := c.ackChannels[workerType]
	c.ackChannelsMu.RUnlock()

	if !exists {
		err := fmt.Errorf("acknowledgment channel not found for worker type %s", workerType)
		c.log.WithError(err).WithFields(map[string]interface{}{
			"worker_type":  workerType,
			"task_id":      task.ID,
			"delivery_tag": msg.DeliveryTag,
		}).Error("Failed to send acknowledgment request")
		return err
	}

	// Send the acknowledgment request to the channel
	select {
	case <-c.ctx.Done():
		return fmt.Errorf("consumer shutting down, acknowledgment request discarded")
	case ackChan <- req:
		c.log.WithFields(map[string]interface{}{
			"worker_type":  workerType,
			"task_id":      task.ID,
			"delivery_tag": msg.DeliveryTag,
			"ack_type":     ackType,
			"requeue":      requeue,
		}).Debug("Sent acknowledgment request to channel")
		return nil
	default:
		// If the channel is full, log a warning and try again in a goroutine
		c.log.WithFields(map[string]interface{}{
			"worker_type":  workerType,
			"task_id":      task.ID,
			"delivery_tag": msg.DeliveryTag,
		}).Warn("Acknowledgment channel is full, retrying in background")

		// Try again in a goroutine with a timeout
		go func() {
			timer := time.NewTimer(1 * time.Minute)
			defer timer.Stop()

			for {
				select {
				case <-c.ctx.Done():
					c.log.WithFields(map[string]interface{}{
						"worker_type":  workerType,
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Warn("Consumer shutting down, acknowledgment request discarded")
					return
				case <-timer.C:
					c.log.WithFields(map[string]interface{}{
						"worker_type":  workerType,
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Error("Timed out trying to send acknowledgment request")
					return
				case ackChan <- req:
					c.log.WithFields(map[string]interface{}{
						"worker_type":  workerType,
						"task_id":      task.ID,
						"delivery_tag": msg.DeliveryTag,
					}).Info("Successfully sent acknowledgment request after retry")
					return
				default:
					// Wait a bit before trying again
					time.Sleep(100 * time.Millisecond)
				}
			}
		}()

		return fmt.Errorf("acknowledgment channel is full, retrying in background")
	}
}

// safeAck safely acknowledges a message, checking if the channel is open
func (c *Consumer) safeAck(msg amqp.Delivery, taskID string) error {
	// Check if the task has already been acknowledged
	if c.isTaskAcknowledged(taskID) {
		c.log.WithFields(map[string]interface{}{
			"task_id":      taskID,
			"delivery_tag": msg.DeliveryTag,
		}).Warn("Task already acknowledged, skipping ack")
		return nil
	}

	// Use SafeChannelOp to safely perform the ack operation
	err := c.rabbitMQ.SafeChannelOp(func(ch *amqp.Channel) error {
		c.log.WithFields(map[string]interface{}{
			"task_id":      taskID,
			"delivery_tag": msg.DeliveryTag,
		}).Debug("Attempting to acknowledge message")
		return msg.Ack(false)
	})

	if err != nil {
		c.log.WithError(err).WithFields(map[string]interface{}{
			"task_id":      taskID,
			"delivery_tag": msg.DeliveryTag,
			"channel_open": c.rabbitMQ.IsChannelOpen(),
		}).Error("Failed to ack message")
		return err
	}

	// Mark the task as acknowledged
	c.markTaskAcknowledged(taskID)
	c.log.WithFields(map[string]interface{}{
		"task_id":      taskID,
		"delivery_tag": msg.DeliveryTag,
	}).Info("Message successfully acknowledged")

	return nil
}

// safeNack safely negative-acknowledges a message, checking if the channel is open
func (c *Consumer) safeNack(msg amqp.Delivery, taskID string, requeue bool) error {
	// Check if the task has already been acknowledged
	if c.isTaskAcknowledged(taskID) {
		c.log.WithFields(map[string]interface{}{
			"task_id":      taskID,
			"delivery_tag": msg.DeliveryTag,
			"requeue":      requeue,
		}).Warn("Task already acknowledged, skipping nack")
		return nil
	}

	// Use SafeChannelOp to safely perform the nack operation
	err := c.rabbitMQ.SafeChannelOp(func(ch *amqp.Channel) error {
		c.log.WithFields(map[string]interface{}{
			"task_id":      taskID,
			"delivery_tag": msg.DeliveryTag,
			"requeue":      requeue,
		}).Debug("Attempting to negative-acknowledge message")
		return msg.Nack(false, requeue)
	})

	if err != nil {
		c.log.WithError(err).WithFields(map[string]interface{}{
			"task_id":      taskID,
			"delivery_tag": msg.DeliveryTag,
			"requeue":      requeue,
			"channel_open": c.rabbitMQ.IsChannelOpen(),
		}).Error("Failed to nack message")
		return err
	}

	// Mark the task as acknowledged
	c.markTaskAcknowledged(taskID)
	c.log.WithFields(map[string]interface{}{
		"task_id":      taskID,
		"delivery_tag": msg.DeliveryTag,
		"requeue":      requeue,
	}).Info("Message successfully negative-acknowledged")

	return nil
}
