package worker

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// APIPullWorker is a worker that pulls data from an API with the new payload structure
type APIPullWorker struct {
	log         *logger.Logger
	client      *http.Client
	logWriter   *logwriter.LogWriter
	minioClient *minio.Client
	minioBucket string
	taskMap     map[string]bool // Map to track tasks that are being processed
	taskMapMu   sync.Mutex      // Mutex to protect the task map
}

// APIPullConfig holds configuration for the API pull worker
type APIPullConfig struct {
	Timeout int
	Minio   struct {
		Endpoint  string
		AccessKey string
		SecretKey string
		Bucket    string
		UseSSL    bool
	}
}

// APIPullPayload is the payload for the API pull worker
type APIPullPayload struct {
	WorkerType string                 `json:"worker_type"`
	Config     APIPullPayloadConfig   `json:"config"`
	Metadata   APIPullPayloadMetadata `json:"metadata"`
}

// APIPullPayloadConfig is the configuration for the API pull worker
type APIPullPayloadConfig struct {
	Endpoint string                 `json:"endpoint"`
	Method   string                 `json:"method"`
	Headers  map[string]string      `json:"headers"`
	Body     map[string]interface{} `json:"body,omitempty"`
	URL      string                 `json:"url"`
	Params   map[string]interface{} `json:"params,omitempty"`
	Auth     *APIPullAuth           `json:"auth,omitempty"`
}

// APIPullAuth is the authentication configuration for the API pull worker
type APIPullAuth struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// APIPullPayloadMetadata is the metadata for the API pull worker
type APIPullPayloadMetadata struct {
	PSM      string `json:"psm"`
	Estate   string `json:"estate"`
	Division string `json:"division"`
	NIK      string `json:"nik"`
	Name     string `json:"name"`
}

// NewAPIPullWorker creates a new API pull worker
func NewAPIPullWorker(log *logger.Logger, config interface{}) (Worker, error) {
	// Convert config to APIPullConfig
	var apiConfig APIPullConfig
	configMap, ok := config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config type: %T", config)
	}

	// Get Minio config
	minioConfig, ok := configMap["Minio"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid Minio config type: %T", configMap["Minio"])
	}

	// Set Minio config
	apiConfig.Minio.Endpoint = minioConfig["Endpoint"].(string)
	apiConfig.Minio.AccessKey = minioConfig["AccessKey"].(string)
	apiConfig.Minio.SecretKey = minioConfig["SecretKey"].(string)
	apiConfig.Minio.Bucket = minioConfig["Bucket"].(string)
	apiConfig.Minio.UseSSL = minioConfig["UseSSL"].(bool)

	// Set timeout
	if timeout, ok := configMap["API"].(map[string]interface{})["Timeout"].(int); ok {
		apiConfig.Timeout = timeout
	} else {
		apiConfig.Timeout = 30 // Default timeout
	}

	// Create Minio client
	var minioClient *minio.Client
	var err error

	if apiConfig.Minio.Endpoint != "" {
		minioClient, err = minio.New(apiConfig.Minio.Endpoint, &minio.Options{
			Creds:  credentials.NewStaticV4(apiConfig.Minio.AccessKey, apiConfig.Minio.SecretKey, ""),
			Secure: apiConfig.Minio.UseSSL,
		})
		if err != nil {
			log.WithError(err).Error("Failed to create Minio client, will continue without storage")
			minioClient = nil
		} else {
			// Check if bucket exists
			exists, err := minioClient.BucketExists(context.Background(), apiConfig.Minio.Bucket)
			if err != nil {
				log.WithError(err).Error("Failed to check if bucket exists, will continue without storage")
				minioClient = nil
			} else if !exists {
				// Create bucket if it doesn't exist
				err = minioClient.MakeBucket(context.Background(), apiConfig.Minio.Bucket, minio.MakeBucketOptions{})
				if err != nil {
					log.WithError(err).Error("Failed to create bucket, will continue without storage")
					minioClient = nil
				}
			}
		}
	}

	// Set default timeout if not provided
	timeout := time.Duration(apiConfig.Timeout) * time.Second
	if timeout == 0 {
		timeout = 30 * time.Second
	}

	worker := &APIPullWorker{
		log:         log,
		client:      &http.Client{Timeout: timeout},
		logWriter:   nil, // Will be set later via SetLogWriter
		minioClient: minioClient,
		minioBucket: apiConfig.Minio.Bucket,
		taskMap:     make(map[string]bool),
	}

	// Log configuration information
	log.WithFields(map[string]interface{}{
		"timeout":       timeout.String(),
		"minio_bucket":  apiConfig.Minio.Bucket,
		"minio_enabled": minioClient != nil,
	}).Info("API Pull Worker initialized")

	return worker, nil
}

// Process processes a task
func (w *APIPullWorker) Process(ctx context.Context, task *Task) error {
	// Check if this task is already being processed
	w.taskMapMu.Lock()
	if _, exists := w.taskMap[task.ID]; exists {
		w.taskMapMu.Unlock()
		w.log.WithField("worker_id", task.ID).Warn("Task is already being processed, skipping")
		return fmt.Errorf("task %s is already being processed", task.ID)
	}

	// Mark this task as being processed
	w.taskMap[task.ID] = true
	w.taskMapMu.Unlock()

	// Ensure we remove the task from the map when we're done
	defer func() {
		w.taskMapMu.Lock()
		delete(w.taskMap, task.ID)
		w.taskMapMu.Unlock()
	}()

	w.log.WithField("worker_id", task.ID).Info("Processing API pull task")

	// Create a context with timeout for this task
	taskCtx, cancel := context.WithTimeout(ctx, w.client.Timeout)
	defer cancel()

	var payload APIPullPayload
	if err := json.Unmarshal(task.Payload, &payload); err != nil {
		w.log.WithError(err).Error("Failed to unmarshal payload")

		// Log invalid payload to Redis - this must succeed for the message to be acknowledged
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":     "Invalid payload: " + err.Error(),
				"worker_id": task.ID,
			}

			logKey := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(logKey, "api_pull", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
				return fmt.Errorf("failed to unmarshal payload: %w, and failed to log to Redis: %v", err, logErr)
			}

			// Store invalid payload in Redis
			key := fmt.Sprintf("invalid-payload:%s:%s", "api_pull", task.ID)
			if storeErr := w.storeInvalidPayload(key, task.Payload); storeErr != nil {
				w.log.WithError(storeErr).Error("Failed to store invalid payload in Redis")
				return fmt.Errorf("failed to unmarshal payload: %w, and failed to store in Redis: %v", err, storeErr)
			}
		}

		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	// Validate payload
	if err := w.validatePayload(&payload); err != nil {
		w.log.WithError(err).Error("Invalid payload")

		// Log invalid payload to Redis - this must succeed for the message to be acknowledged
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":     "Invalid payload: " + err.Error(),
				"worker_id": task.ID,
			}

			logKey := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(logKey, "api_pull", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
				return fmt.Errorf("invalid payload: %w, and failed to log to Redis: %v", err, logErr)
			}

			// Store invalid payload in Redis
			key := fmt.Sprintf("invalid-payload:%s:%s", "api_pull", task.ID)
			if storeErr := w.storeInvalidPayload(key, task.Payload); storeErr != nil {
				w.log.WithError(storeErr).Error("Failed to store invalid payload in Redis")
				return fmt.Errorf("invalid payload: %w, and failed to store in Redis: %v", err, storeErr)
			}
		}

		return fmt.Errorf("invalid payload: %w", err)
	}

	// Process the API request
	return w.processAPIRequest(taskCtx, task, &payload)
}

// validatePayload validates the payload
func (w *APIPullWorker) validatePayload(payload *APIPullPayload) error {
	// Check worker type
	if payload.WorkerType != "api_pull" {
		return fmt.Errorf("invalid worker type: %s", payload.WorkerType)
	}

	// Check mandatory fields
	if payload.Config.URL == "" {
		return fmt.Errorf("missing URL")
	}

	if payload.Config.Method == "" {
		return fmt.Errorf("missing method")
	}

	if payload.Config.Headers == nil {
		return fmt.Errorf("missing headers")
	}

	// Check Content-Type header
	contentType, ok := payload.Config.Headers["Content-Type"]
	if !ok || contentType != "application/json" {
		return fmt.Errorf("missing or invalid Content-Type header")
	}

	// Method-specific validation
	switch strings.ToUpper(payload.Config.Method) {
	case http.MethodGet:
		// No additional validation for GET
	case http.MethodPost:
		// No additional validation for POST
	default:
		return fmt.Errorf("unsupported method: %s", payload.Config.Method)
	}

	return nil
}

// storeInvalidPayload stores an invalid payload in Redis
func (w *APIPullWorker) storeInvalidPayload(key string, payload json.RawMessage) error {
	if w.logWriter == nil {
		return fmt.Errorf("logWriter is nil")
	}

	// Store payload in Redis
	return w.logWriter.StoreInvalidPayload(key, payload)
}

// processAPIRequest processes an API request
func (w *APIPullWorker) processAPIRequest(ctx context.Context, task *Task, payload *APIPullPayload) error {
	// Prepare request
	var req *http.Request
	var err error

	// Process authentication if provided
	if payload.Config.Auth != nil {
		// Generate token from username and password
		token := w.generateToken(payload.Config.Auth.Username, payload.Config.Auth.Password)

		// Add Authorization header
		payload.Config.Headers["Authorization"] = fmt.Sprintf("Bearer %s", token)
	}

	// Create request based on method
	switch strings.ToUpper(payload.Config.Method) {
	case http.MethodGet:
		// Create URL with query parameters
		reqURL, err := w.buildURLWithParams(payload.Config.URL, payload.Config.Params)
		if err != nil {
			return fmt.Errorf("failed to build URL with params: %w", err)
		}

		req, err = http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
		if err != nil {
			return fmt.Errorf("failed to create GET request: %w", err)
		}

	case http.MethodPost:
		// Marshal body to JSON
		var bodyJSON []byte
		if payload.Config.Body != nil {
			bodyJSON, err = json.Marshal(payload.Config.Body)
			if err != nil {
				return fmt.Errorf("failed to marshal request body: %w", err)
			}
		}

		req, err = http.NewRequestWithContext(ctx, http.MethodPost, payload.Config.URL, bytes.NewBuffer(bodyJSON))
		if err != nil {
			return fmt.Errorf("failed to create POST request: %w", err)
		}
	}

	// Add headers
	for key, value := range payload.Config.Headers {
		req.Header.Add(key, value)
	}

	// Send request with retry for transient errors
	var resp *http.Response
	maxRetries := 2
	retryDelay := 500 * time.Millisecond

	for retry := 0; retry < maxRetries; retry++ {
		// Create a new request for each retry to avoid "http: ContentLength=... with Body length 0" errors
		if retry > 0 {
			w.log.WithField("retry", retry).Info("Retrying API request")
			// Recreate the request for retry
			switch strings.ToUpper(payload.Config.Method) {
			case http.MethodGet:
				// Create URL with query parameters
				reqURL, err := w.buildURLWithParams(payload.Config.URL, payload.Config.Params)
				if err != nil {
					return fmt.Errorf("failed to build URL with params for retry: %w", err)
				}
				req, err = http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
				if err != nil {
					return fmt.Errorf("failed to create GET request for retry: %w", err)
				}
			case http.MethodPost:
				// Marshal body to JSON
				var bodyJSON []byte
				if payload.Config.Body != nil {
					bodyJSON, err = json.Marshal(payload.Config.Body)
					if err != nil {
						return fmt.Errorf("failed to marshal request body for retry: %w", err)
					}
				}
				req, err = http.NewRequestWithContext(ctx, http.MethodPost, payload.Config.URL, bytes.NewBuffer(bodyJSON))
				if err != nil {
					return fmt.Errorf("failed to create POST request for retry: %w", err)
				}
			}
			// Add headers
			for key, value := range payload.Config.Headers {
				req.Header.Add(key, value)
			}
		}

		// Send the request
		resp, err = w.client.Do(req)

		// If successful, break out of retry loop
		if err == nil {
			break
		}

		// Check if it's a timeout error
		isTimeout := false
		if strings.Contains(err.Error(), "timeout") ||
			strings.Contains(err.Error(), "deadline exceeded") ||
			strings.Contains(err.Error(), "Client.Timeout") {
			isTimeout = true
			w.log.WithError(err).Warn("Request timed out")
		}

		// For the last retry or timeout errors, log the failure
		if retry == maxRetries-1 || isTimeout {
			// Log failure - this must succeed for the message to be acknowledged
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"error":      err.Error(),
					"worker_id":  task.ID,
					"url":        payload.Config.URL,
					"method":     payload.Config.Method,
					"endpoint":   payload.Config.Endpoint,
					"is_timeout": isTimeout,
					"retry":      retry,
					"timestamp":  time.Now().Format(time.RFC3339),
				}

				logKey := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
				if logErr := w.logWriter.LogFailure(logKey, "api_pull", metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write to Redis")
					return fmt.Errorf("failed to send request: %w, and failed to log to Redis: %v", err, logErr)
				}

				// Log failure with detailed information
				w.log.WithFields(map[string]interface{}{
					"worker_id":  task.ID,
					"is_timeout": isTimeout,
					"retry":      retry,
					"redis_key":  fmt.Sprintf("logs:api_pull:%s:client-error:%s", time.Now().Format("2006-01-02"), task.ID),
				}).Info("Successfully logged request failure to Redis")
			}

			// Don't retry timeout errors
			if isTimeout {
				return fmt.Errorf("TIMEOUT_ERROR: failed to send request (timeout): %w", err)
			}
		}

		// If this is not the last retry, wait before retrying
		if retry < maxRetries-1 {
			select {
			case <-ctx.Done():
				// Context cancelled, don't retry
				return ctx.Err()
			case <-time.After(retryDelay * time.Duration(retry+1)):
				// Wait before retrying with exponential backoff
			}
		}
	}

	// If we still have an error after all retries, return it
	if err != nil {
		return fmt.Errorf("failed to send request after %d retries: %w", maxRetries, err)
	}

	defer resp.Body.Close()

	// Read response body with retry for large responses
	var respBody []byte
	maxReadRetries := 2
	for readRetry := 0; readRetry <= maxReadRetries; readRetry++ {
		// If this is a retry, we need to get a new response since the body was already consumed
		if readRetry > 0 {
			w.log.WithField("retry", readRetry).Info("Retrying response body read")

			// Close the previous response body
			resp.Body.Close()

			// Create a new request
			var newReq *http.Request
			switch strings.ToUpper(payload.Config.Method) {
			case http.MethodGet:
				reqURL, urlErr := w.buildURLWithParams(payload.Config.URL, payload.Config.Params)
				if urlErr != nil {
					return fmt.Errorf("failed to build URL with params for retry: %w", urlErr)
				}
				newReq, err = http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
			case http.MethodPost:
				var bodyJSON []byte
				if payload.Config.Body != nil {
					bodyJSON, err = json.Marshal(payload.Config.Body)
					if err != nil {
						return fmt.Errorf("failed to marshal request body for retry: %w", err)
					}
				}
				newReq, err = http.NewRequestWithContext(ctx, http.MethodPost, payload.Config.URL, bytes.NewBuffer(bodyJSON))
			}

			if err != nil {
				return fmt.Errorf("failed to create request for retry: %w", err)
			}

			// Add headers
			for key, value := range payload.Config.Headers {
				newReq.Header.Add(key, value)
			}

			// Send the request again
			resp, err = w.client.Do(newReq)
			if err != nil {
				// Log failure to Redis
				if w.logWriter != nil {
					metadata := map[string]interface{}{
						"error":      "Failed to retry request: " + err.Error(),
						"worker_id":  task.ID,
						"url":        payload.Config.URL,
						"method":     payload.Config.Method,
						"endpoint":   payload.Config.Endpoint,
						"read_retry": readRetry,
						"timestamp":  time.Now().Format(time.RFC3339),
					}

					logKey := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
					if logErr := w.logWriter.LogFailure(logKey, "api_pull", metadata); logErr != nil {
						w.log.WithError(logErr).Error("Failed to write to Redis")
						return fmt.Errorf("failed to retry request: %w, and failed to log to Redis: %v", err, logErr)
					}

					// Log failure with detailed information
					w.log.WithFields(map[string]interface{}{
						"worker_id":  task.ID,
						"read_retry": readRetry,
						"redis_key":  fmt.Sprintf("logs:api-pull:%s:client-error:%s:%s", time.Now().Format("2006-01-02"), task.ID, time.Now().Format("150405.000")),
					}).Info("Successfully logged retry request failure to Redis")
				}
				return fmt.Errorf("failed to retry request: %w", err)
			}
			defer resp.Body.Close()
		}

		// Try to read the response body
		respBody, err = io.ReadAll(resp.Body)
		if err == nil {
			// Successfully read the response body
			break
		}

		// If this is the last retry, log the failure and return an error
		if readRetry == maxReadRetries {
			// Log failure to Redis
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"error":      "Failed to read response body after retries: " + err.Error(),
					"worker_id":  task.ID,
					"url":        payload.Config.URL,
					"method":     payload.Config.Method,
					"endpoint":   payload.Config.Endpoint,
					"read_retry": readRetry,
					"timestamp":  time.Now().Format(time.RFC3339),
				}
				logKey := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
				if logErr := w.logWriter.LogFailure(logKey, "api_pull", metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write to Redis")
					// Return both errors to prevent message acknowledgment
					return fmt.Errorf("failed to read response body: %w, and failed to log to Redis: %v", err, logErr)
				}

				// Log failure with detailed information
				w.log.WithFields(map[string]interface{}{
					"worker_id":  task.ID,
					"read_retry": readRetry,
					"redis_key":  fmt.Sprintf("logs:api-pull:%s:client-error:%s:%s", time.Now().Format("2006-01-02"), task.ID, time.Now().Format("150405.000")),
				}).Info("Successfully logged response body read failure to Redis")
			}
			return fmt.Errorf("failed to read response body after %d retries: %w", maxReadRetries, err)
		}

		// Wait before retrying
		time.Sleep(time.Duration(readRetry+1) * 500 * time.Millisecond)
	}

	// Parse response as JSON if possible
	var responseData interface{}
	if err := json.Unmarshal(respBody, &responseData); err != nil {
		w.log.WithError(err).Warn("Failed to parse response as JSON, will store raw response")
		responseData = map[string]interface{}{
			"raw_response": string(respBody),
		}

		// Log JSON parsing error to Redis - this must succeed for the message to be acknowledged
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":       "Failed to parse response as JSON: " + err.Error(),
				"worker_id":   task.ID,
				"url":         payload.Config.URL,
				"method":      payload.Config.Method,
				"endpoint":    payload.Config.Endpoint,
				"status_code": resp.StatusCode,
				"timestamp":   time.Now().Format(time.RFC3339),
			}

			// Only include response in metadata for non-200 status codes
			if resp.StatusCode < 200 || resp.StatusCode >= 300 {
				metadata["response"] = string(respBody)
			}

			logKey := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(logKey, "api_pull", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write JSON parsing error to Redis")
				// Return error to prevent message acknowledgment if Redis logging fails
				return fmt.Errorf("failed to parse response as JSON and failed to log to Redis: %w, %v", err, logErr)
			}

			// Log success with detailed information
			// Determine status code category for the log message
			var category string
			if resp.StatusCode >= 200 && resp.StatusCode < 300 {
				category = "success"
			} else if resp.StatusCode >= 300 && resp.StatusCode < 400 {
				category = "redirect"
			} else if resp.StatusCode >= 400 && resp.StatusCode < 500 {
				category = "client-error"
			} else if resp.StatusCode >= 500 && resp.StatusCode < 600 {
				category = "server-error"
			} else {
				category = "unknown"
			}

			w.log.WithFields(map[string]interface{}{
				"worker_id":   task.ID,
				"status_code": resp.StatusCode,
				"category":    category,
				"redis_key":   fmt.Sprintf("logs:api-pull:%s:%s:%s:%s", time.Now().Format("2006-01-02"), category, task.ID, time.Now().Format("150405.000")),
			}).Info("Successfully logged JSON parsing error to Redis")
		}
	}

	// Check if Redis logging is available
	if w.logWriter == nil {
		w.log.Warn("LogWriter is not available, cannot log to Redis")
		// If Redis logging is required but not available, return an error
		return fmt.Errorf("LogWriter is not available, cannot log to Redis")
	}

	// Prepare common metadata for logging
	metadata := map[string]interface{}{
		"status_code": resp.StatusCode,
		"worker_id":   task.ID,
		"url":         payload.Config.URL,
		"method":      payload.Config.Method,
		"endpoint":    payload.Config.Endpoint,
		"timestamp":   time.Now().Format(time.RFC3339),
	}

	// Only include response in metadata for non-200 status codes
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		metadata["response"] = string(respBody)
	}

	// Determine status code category for the log message
	var category string
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		category = "success"
	} else if resp.StatusCode >= 300 && resp.StatusCode < 400 {
		category = "redirect"
	} else if resp.StatusCode >= 400 && resp.StatusCode < 500 {
		category = "client-error"
	} else if resp.StatusCode >= 500 && resp.StatusCode < 600 {
		category = "server-error"
	} else {
		category = "unknown"
	}

	timestamp := time.Now().Format("150405.000")
	redisKey := fmt.Sprintf("logs:api-pull:%s:%s:%s:%s", time.Now().Format("2006-01-02"), category, task.ID, timestamp)
	metadata["redis_key"] = redisKey

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		w.log.WithField("status_code", resp.StatusCode).Info("API request successful")

		var minioErr error
		if w.minioClient != nil {
			maxMinioRetries := 3
			for minioRetry := 0; minioRetry < maxMinioRetries; minioRetry++ {
				minioErr = w.storeInMinio(ctx, responseData, task.ID, payload)
				if minioErr == nil {
					w.log.WithFields(map[string]interface{}{
						"worker_id":   task.ID,
						"bucket":      w.minioBucket,
						"status_code": resp.StatusCode,
						"retry":       minioRetry,
					})
					break
				}
				w.log.WithError(minioErr).WithFields(map[string]interface{}{
					"worker_id":   task.ID,
					"retry":       minioRetry,
					"max_retries": maxMinioRetries,
				}).Warn("Failed to store in Minio, retrying")

				// Wait before retrying
				select {
				case <-ctx.Done():
					return ctx.Err()
				case <-time.After(time.Duration(minioRetry+1) * 500 * time.Millisecond):
					// Wait with exponential backoff
				}
			}

			if minioErr != nil {
				w.log.WithError(minioErr).Error("Failed to store in Minio after all retries")

				metadata["error"] = fmt.Sprintf("Failed to store in Minio: %v", minioErr)
				metadata["minio_error"] = true

				key := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID, timestamp)
				logErr := w.logWriter.LogFailure(key, "api_pull", metadata)
				if logErr != nil {
					w.log.WithError(logErr).Error("Failed to write Minio failure to Redis")
					return fmt.Errorf("API request succeeded with status code %d, but failed to store in Minio: %w and failed to log to Redis: %v",
						resp.StatusCode, minioErr, logErr)
				}

				return fmt.Errorf("API request succeeded with status code %d, but failed to store in Minio: %w",
					resp.StatusCode, minioErr)
			}
		} else {
			w.log.Warn("Minio client not available, skipping storage")
			metadata["warning"] = "Minio client not available, response not stored"
		}

		key := fmt.Sprintf("logs:api-pull:%s:success:%s", time.Now().Format("2006-01-02"), task.ID)
		logErr := w.logWriter.LogSuccess(key, "api_pull", metadata)
		if logErr != nil {
			w.log.WithError(logErr).Error("Failed to write success log to Redis")
			return fmt.Errorf("API request succeeded with status code %d, but failed to log to Redis: %w", resp.StatusCode, logErr)
		}
		w.log.WithFields(map[string]interface{}{
			"worker_id":   task.ID,
			"status_code": resp.StatusCode,
			"redis_key":   redisKey,
		})

		return nil
	} else {
		w.log.WithField("status_code", resp.StatusCode).Error("API request failed")

		metadata["error"] = fmt.Sprintf("API request failed with status code: %d", resp.StatusCode)
		if w.minioClient != nil {
			if err := w.storeInMinio(ctx, responseData, task.ID, payload); err != nil {
				w.log.WithError(err).Warn("Failed to store non-2xx response in Minio, continuing")
				metadata["minio_error"] = err.Error()
			} else {
				w.log.WithFields(map[string]interface{}{
					"worker_id":   task.ID,
					"bucket":      w.minioBucket,
					"status_code": resp.StatusCode,
				}).Info("Stored non-2xx API response in Minio")
			}
		}

		key := fmt.Sprintf("logs:api-pull:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID, timestamp)
		logErr := w.logWriter.LogFailure(key, "api_pull", metadata)
		if logErr != nil {
			w.log.WithError(logErr).Error("Failed to write failure log to Redis")
			return fmt.Errorf("API request failed with status code %d, and failed to log to Redis: %w", resp.StatusCode, logErr)
		}
		w.log.WithFields(map[string]interface{}{
			"worker_id":   task.ID,
			"status_code": resp.StatusCode,
			"category":    category,
			"redis_key":   redisKey,
		})

		return fmt.Errorf("API request failed with status code: %d", resp.StatusCode)
	}
}

// generateToken generates a token from username and password
func (w *APIPullWorker) generateToken(username, password string) string {
	// Simple hash-based token generation
	data := fmt.Sprintf("%s:%s", username, password)
	hash := sha256.Sum256([]byte(data))
	return base64.StdEncoding.EncodeToString(hash[:])
}

// buildURLWithParams builds a URL with query parameters
func (w *APIPullWorker) buildURLWithParams(baseURL string, params map[string]interface{}) (string, error) {
	if len(params) == 0 {
		return baseURL, nil
	}

	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	query := parsedURL.Query()
	for key, value := range params {
		// Convert value to string
		var strValue string
		switch v := value.(type) {
		case string:
			strValue = v
		case int, int64, float64:
			strValue = fmt.Sprintf("%v", v)
		default:
			// Try to marshal to JSON
			jsonValue, err := json.Marshal(v)
			if err != nil {
				return "", fmt.Errorf("failed to marshal param value to JSON: %w", err)
			}
			strValue = string(jsonValue)
		}

		query.Add(key, strValue)
	}

	parsedURL.RawQuery = query.Encode()
	return parsedURL.String(), nil
}

func (w *APIPullWorker) storeInMinio(ctx context.Context, response interface{}, taskID string, payload *APIPullPayload) error {
	folderName := time.Now().Format("2006-01-02")
	// Gunakan UUID sebagai nama file
	objectName := filepath.Join(folderName, fmt.Sprintf("%s.json", taskID))

	// Convert response to JSON
	responseJSON, err := json.Marshal(response)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %w", err)
	}
	// Create metadata
	metadata := map[string]string{
		"x-amz-meta-worker-id": taskID,
		"x-amz-meta-url":       payload.Config.URL,
		"x-amz-meta-method":    payload.Config.Method,
		"x-amz-meta-endpoint":  payload.Config.Endpoint,
		"x-amz-meta-timestamp": time.Now().Format(time.RFC3339),
	}

	// Add metadata from payload
	if payload.Metadata.PSM != "" {
		metadata["x-amz-meta-psm"] = payload.Metadata.PSM
	}
	if payload.Metadata.Estate != "" {
		metadata["x-amz-meta-estate"] = payload.Metadata.Estate
	}
	if payload.Metadata.Division != "" {
		metadata["x-amz-meta-division"] = payload.Metadata.Division
	}
	if payload.Metadata.NIK != "" {
		metadata["x-amz-meta-nik"] = payload.Metadata.NIK
	}
	if payload.Metadata.Name != "" {
		metadata["x-amz-meta-name"] = payload.Metadata.Name
	}

	// Set content type and metadata
	putOptions := minio.PutObjectOptions{
		ContentType:  "application/json",
		UserMetadata: metadata,
	}

	// Upload to Minio
	info, err := w.minioClient.PutObject(
		ctx,
		w.minioBucket,
		objectName,
		bytes.NewReader(responseJSON),
		int64(len(responseJSON)),
		putOptions,
	)
	if err != nil {
		return fmt.Errorf("failed to upload to Minio: %w", err)
	}

	w.log.WithFields(map[string]interface{}{
		"bucket":       w.minioBucket,
		"object_name":  objectName,
		"size":         info.Size,
		"etag":         info.ETag,
		"has_metadata": len(metadata) > 0,
	})

	return nil
}

// Type returns the type of worker
func (w *APIPullWorker) Type() WorkerType {
	return "api_pull"
}

// SetLogWriter sets the log writer for the worker
func (w *APIPullWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker (not used by this worker)
func (w *APIPullWorker) SetDispatcher(dispatcher interface{}) {
	// This worker doesn't need a dispatcher
}
