package worker

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// DBConfig holds database configuration
type DBConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	Name     string
}

// DBCloningWorker is a worker that clones data from one database to another
type DBCloningWorker struct {
	log         *logger.Logger
	sourceDB    *DBConfig
	targetDB    *DBConfig
	logWriter   *logwriter.LogWriter
	minioClient *minio.Client
	minioBucket string
}

// DBCloningPayload is the payload for the DB cloning worker
type DBCloningPayload struct {
	Table       string   `json:"table"`
	Columns     []string `json:"columns,omitempty"`
	Conditions  string   `json:"conditions,omitempty"`
	BatchSize   int      `json:"batch_size,omitempty"`
	Source      string   `json:"source,omitempty"`
	Destination string   `json:"destination,omitempty"`
}

// NewDBCloningWorker creates a new DB cloning worker
func NewDBCloningWorker(log *logger.Logger, config interface{}) (Worker, error) {
	// Try to extract from main config
	mainCfg, ok := config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config type for DB cloning worker")
	}

	// Extract source DB config
	sourceDB := &DBConfig{}
	if src, ok := mainCfg["SourceDB"].(map[string]interface{}); ok {
		if host, ok := src["Host"].(string); ok {
			sourceDB.Host = host
		}
		if port, ok := src["Port"].(string); ok {
			sourceDB.Port = port
		}
		if user, ok := src["User"].(string); ok {
			sourceDB.User = user
		}
		if password, ok := src["Password"].(string); ok {
			sourceDB.Password = password
		}
		if name, ok := src["Name"].(string); ok {
			sourceDB.Name = name
		}
	}

	// Extract target DB config
	targetDB := &DBConfig{}
	if tgt, ok := mainCfg["TargetDB"].(map[string]interface{}); ok {
		if host, ok := tgt["Host"].(string); ok {
			targetDB.Host = host
		}
		if port, ok := tgt["Port"].(string); ok {
			targetDB.Port = port
		}
		if user, ok := tgt["User"].(string); ok {
			targetDB.User = user
		}
		if password, ok := tgt["Password"].(string); ok {
			targetDB.Password = password
		}
		if name, ok := tgt["Name"].(string); ok {
			targetDB.Name = name
		}
	}

	// Extract Minio config
	var minioBucket string
	var minioClient *minio.Client

	if minioMap, ok := mainCfg["Minio"].(map[string]interface{}); ok {
		var endpoint, accessKey, secretKey string
		var useSSL bool

		if endpointVal, ok := minioMap["Endpoint"].(string); ok {
			endpoint = endpointVal
		}
		if accessKeyVal, ok := minioMap["AccessKey"].(string); ok {
			accessKey = accessKeyVal
		}
		if secretKeyVal, ok := minioMap["SecretKey"].(string); ok {
			secretKey = secretKeyVal
		}
		if bucketVal, ok := minioMap["Bucket"].(string); ok {
			minioBucket = bucketVal
		}
		if sslVal, ok := minioMap["UseSSL"].(bool); ok {
			useSSL = sslVal
		}

		// Try to get configuration from environment variables if not set
		if endpoint == "" {
			endpoint = os.Getenv("MINIO_ENDPOINT")
		}
		if accessKey == "" {
			accessKey = os.Getenv("MINIO_ACCESS_KEY")
		}
		if secretKey == "" {
			secretKey = os.Getenv("MINIO_SECRET_KEY")
		}
		if minioBucket == "" {
			minioBucket = os.Getenv("DB_CLONE_BUCKET")
			if minioBucket == "" {
				minioBucket = "db_clone" // Default bucket name
			}
		}
		if !useSSL {
			if useSSLStr := os.Getenv("MINIO_USE_SSL"); useSSLStr == "true" {
				useSSL = true
			}
		}

		// Initialize Minio client if endpoint is provided
		if endpoint != "" {
			var err error
			minioOpts := minio.Options{
				Creds:  credentials.NewStaticV4(accessKey, secretKey, ""),
				Secure: useSSL,
			}
			minioClient, err = minio.New(endpoint, &minioOpts)
			if err != nil {
				log.WithError(err).Warn("Failed to create Minio client, continuing without Minio storage")
			}
		}
	}

	return &DBCloningWorker{
		log:         log,
		sourceDB:    sourceDB,
		targetDB:    targetDB,
		logWriter:   nil, // Will be set later via SetLogWriter
		minioClient: minioClient,
		minioBucket: minioBucket,
	}, nil
}

// Process processes a task
func (w *DBCloningWorker) Process(ctx context.Context, task *Task) error {
	w.log.WithField("task_id", task.ID).Info("Processing DB cloning task")

	// Log the task payload for debugging
	// w.log.WithField("payload", string(task.Payload)).Debug("Task payload")

	var payload DBCloningPayload
	if err := json.Unmarshal(task.Payload, &payload); err != nil {
		w.log.WithError(err).Warn("Failed to unmarshal payload as DBCloningPayload, trying JobRequest format")

		// Try to unmarshal as a JobRequest from the API server
		var jobRequest struct {
			WorkerType  string                 `json:"worker_type"`
			Config      map[string]interface{} `json:"config"`
			Table       string                 `json:"table"`
			Source      string                 `json:"source"`
			Destination string                 `json:"destination"`
		}

		if err := json.Unmarshal(task.Payload, &jobRequest); err != nil {
			// Log failure
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"error":   err.Error(),
					"task_id": task.ID,
				}

				key := fmt.Sprintf("logs:db-cloning:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
				if logErr := w.logWriter.LogFailure(key, string(DBWorker), metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write to Redis")
				}
			}
			return fmt.Errorf("failed to unmarshal payload: %w", err)
		}

		// Extract table name from config or top-level field
		if jobRequest.Table != "" {
			payload.Table = jobRequest.Table
		} else if table, ok := jobRequest.Config["table"].(string); ok {
			payload.Table = table
		}

		// Extract columns from config
		if columns, ok := jobRequest.Config["columns"].([]interface{}); ok {
			for _, col := range columns {
				if colStr, ok := col.(string); ok {
					payload.Columns = append(payload.Columns, colStr)
				}
			}
		}

		// Extract conditions from config
		if conditions, ok := jobRequest.Config["conditions"].(string); ok {
			payload.Conditions = conditions
		}

		// Extract batch size from config
		if batchSize, ok := jobRequest.Config["batch_size"].(float64); ok {
			payload.BatchSize = int(batchSize)
		}

		// Extract source from config or top-level field
		if jobRequest.Source != "" {
			payload.Source = jobRequest.Source
		} else if source, ok := jobRequest.Config["source"].(string); ok {
			payload.Source = source
		}

		// Extract destination from config or top-level field
		if jobRequest.Destination != "" {
			payload.Destination = jobRequest.Destination
		} else if destination, ok := jobRequest.Config["destination"].(string); ok {
			payload.Destination = destination
		}
	}

	// Validate payload
	if payload.Table == "" {
		// Try to extract table name directly from the payload
		var rawPayload map[string]interface{}
		if err := json.Unmarshal(task.Payload, &rawPayload); err == nil {
			// Log the raw payload for debugging
			rawPayloadJSON, _ := json.Marshal(rawPayload)
			w.log.WithField("raw_payload", string(rawPayloadJSON)).Debug("Raw payload")

			if tableName, ok := rawPayload["table"].(string); ok && tableName != "" {
				payload.Table = tableName
				w.log.WithField("table", tableName).Info("Extracted table name from raw payload")
			} else {
				w.log.WithField("has_table_field", rawPayload["table"] != nil).
					WithField("table_type", fmt.Sprintf("%T", rawPayload["table"])).
					Debug("Table field details")
			}
		} else {
			w.log.WithError(err).Error("Failed to unmarshal raw payload")
		}
	}

	if payload.Table == "" {
		// Log failure
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":   "table name is required",
				"task_id": task.ID,
			}
			key := fmt.Sprintf("logs:db-cloning:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(key, string(DBWorker), metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}
		return fmt.Errorf("table name is required")
	}

	// Set default batch size if not provided
	if payload.BatchSize <= 0 {
		payload.BatchSize = 1000
	}

	// Simulate DB cloning process
	// In a real implementation, you would:
	// 1. Connect to source DB
	// 2. Connect to target DB
	// 3. Query data from source DB
	// 4. Insert data into target DB

	// Simulate some work
	select {
	case <-ctx.Done():
		// Log failure
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":   ctx.Err().Error(),
				"task_id": task.ID,
				"table":   payload.Table,
			}

			key := fmt.Sprintf("logs:db-cloning:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(key, string(DBWorker), metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}
		return ctx.Err()
	case <-time.After(2 * time.Second):
		// Simulating work being done
	}

	// Simulated number of rows processed
	rowsProcessed := 1000

	// Store result in Minio if client is available
	if w.minioClient != nil {
		if err := w.storeInMinio(ctx, payload, rowsProcessed, task); err != nil {
			w.log.WithError(err).Warn("Failed to store result in Minio, continuing without storage")
		}
	}

	// Log success
	if w.logWriter != nil {
		metadata := map[string]interface{}{
			"task_id":    task.ID,
			"table":      payload.Table,
			"batch_size": payload.BatchSize,
			"rows":       rowsProcessed,
		}

		key := fmt.Sprintf("logs:db-cloning:%s:success:%s", time.Now().Format("2006-01-02"), task.ID)
		if logErr := w.logWriter.LogSuccess(key, string(DBWorker), metadata); logErr != nil {
			w.log.WithError(logErr).Error("Failed to write to Redis")
		}
	}

	w.log.WithField("task_id", task.ID).
		WithField("table", payload.Table).
		Info("DB cloning task completed successfully")

	return nil
}

// storeInMinio stores the DB cloning result in Minio
func (w *DBCloningWorker) storeInMinio(ctx context.Context, payload DBCloningPayload, rowsProcessed int, task *Task) error {
	// Create folder name based on current date
	folderName := time.Now().Format("2006-01-02")

	// Create object name
	objectName := filepath.Join(folderName, fmt.Sprintf("%s-%s.json", payload.Table, time.Now().Format("150405")))

	// Create result object
	result := struct {
		Table         string   `json:"table"`
		Columns       []string `json:"columns,omitempty"`
		Conditions    string   `json:"conditions,omitempty"`
		BatchSize     int      `json:"batch_size"`
		RowsProcessed int      `json:"rows_processed"`
		Source        string   `json:"source"`
		Destination   string   `json:"destination"`
		CloneDatetime string   `json:"clone_datetime"`
	}{
		Table:         payload.Table,
		Columns:       payload.Columns,
		Conditions:    payload.Conditions,
		BatchSize:     payload.BatchSize,
		RowsProcessed: rowsProcessed,
		Source:        payload.Source,
		Destination:   payload.Destination,
		CloneDatetime: time.Now().Format("2006-01-02 15:04:05"),
	}

	// Convert result to JSON
	resultJSON, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %w", err)
	}

	// Check if bucket exists, create if not
	exists, err := w.minioClient.BucketExists(ctx, w.minioBucket)
	if err != nil {
		return fmt.Errorf("failed to check if bucket exists: %w", err)
	}
	if !exists {
		if err := w.minioClient.MakeBucket(ctx, w.minioBucket, minio.MakeBucketOptions{}); err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		w.log.WithField("bucket", w.minioBucket).Info("Created Minio bucket")
	}

	// Try to create metadata for the object
	// This is optional and should not block the process if it fails
	metadata := make(map[string]string)

	// Try to extract metadata from the payload
	var metadataObj struct {
		CloneDatetime string `json:"clone_datetime"`
		Source        string `json:"source"`
		Destination   string `json:"destination"`
	}

	// Set default values
	metadataObj.CloneDatetime = time.Now().Format("2006-01-02 15:04:05")
	metadataObj.Source = payload.Source
	metadataObj.Destination = payload.Destination

	// Try to extract metadata from the task payload
	var rawPayload map[string]interface{}
	if err := json.Unmarshal(task.Payload, &rawPayload); err == nil {
		if metadataMap, ok := rawPayload["metadata"].(map[string]interface{}); ok {
			if cd, ok := metadataMap["clone_datetime"].(string); ok && cd != "" {
				metadataObj.CloneDatetime = cd
			}
			if src, ok := metadataMap["source"].(string); ok && src != "" {
				metadataObj.Source = src
			}
			if dst, ok := metadataMap["destination"].(string); ok && dst != "" {
				metadataObj.Destination = dst
			}
		}
	}

	// Convert metadata to JSON
	metadataJSON, err := json.Marshal(metadataObj)
	if err == nil {
		metadata["x-amz-meta-db-clone-metadata"] = string(metadataJSON)
	} else {
		// Log error but continue with the upload
		w.log.WithError(err).Warn("Failed to create metadata for Minio object, continuing without metadata")
	}

	// Set content type and metadata
	putOptions := minio.PutObjectOptions{
		ContentType: "application/json",
	}

	// Add metadata if available
	if len(metadata) > 0 {
		putOptions.UserMetadata = metadata
	}

	// Upload to Minio
	info, err := w.minioClient.PutObject(
		ctx,
		w.minioBucket,
		objectName,
		bytes.NewReader(resultJSON),
		int64(len(resultJSON)),
		putOptions,
	)
	if err != nil {
		return fmt.Errorf("failed to upload to Minio: %w", err)
	}

	w.log.WithFields(map[string]interface{}{
		"bucket":       w.minioBucket,
		"object_name":  objectName,
		"size":         info.Size,
		"etag":         info.ETag,
		"has_metadata": len(metadata) > 0,
	}).Info("Stored DB cloning result in Minio")

	return nil
}

// Type returns the type of worker
func (w *DBCloningWorker) Type() WorkerType {
	return DBWorker
}

// SetLogWriter sets the log writer for the worker
func (w *DBCloningWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker (not used by this worker)
func (w *DBCloningWorker) SetDispatcher(dispatcher interface{}) {
	// This worker doesn't need a dispatcher
}
