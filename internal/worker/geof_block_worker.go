package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"github.com/paulmach/orb"
	"github.com/paulmach/orb/geojson"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// GeofBlockWorker performs geofencing operations by calculating intersections
// between GPS tracking points (as 3-meter radius polygons) and tree locations (as 5-meter radius polygons)
type GeofBlockWorker struct {
	log       *logger.Logger
	logWriter *logwriter.LogWriter
}

// GeofBlockPayload represents the input payload structure from RabbitMQ
type GeofBlockPayload struct {
	Date            string               `json:"date"`
	Device          BlockDeviceInfo      `json:"device"`
	Tracking        []BlockTrackingPoint `json:"tracking"`
	GeojsTrees      []BlockGeoJSTree     `json:"geojsTrees"`
	RescheduleCount int                  `json:"reschedule_count"`
}

// BlockDeviceInfo represents device information for block worker
type BlockDeviceInfo struct {
	Division string `json:"division"`
	Estate   string `json:"estate"`
	Ident    string `json:"ident"`
	Name     string `json:"name"`
	NIK      string `json:"nik"`
	PSM      string `json:"psm"`
	Type     string `json:"type"`
}

// BlockTrackingPoint represents a GPS tracking point for block worker
type BlockTrackingPoint struct {
	Timestamp string  `json:"timestamp"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// BlockGeoJSTree represents a GeoJSON tree with properties and geometry for block worker
type BlockGeoJSTree struct {
	Type       string                 `json:"type"`
	Properties BlockTreeProperties    `json:"properties"`
	Geometry   map[string]interface{} `json:"geometry"`
}

// BlockTreeProperties represents the properties of a GeoJSON tree
type BlockTreeProperties struct {
	PSM         string      `json:"psm"`
	Region      string      `json:"region"`
	Estate      string      `json:"estate"`
	Divisi      string      `json:"divisi"`
	Blok        string      `json:"blok"`
	PointX      float64     `json:"point_x"`
	PointY      float64     `json:"point_y"`
	PeriodeUkur interface{} `json:"periode_ukur"`
}

// GeofBlockResult represents the output structure to be stored in Redis
type GeofBlockResult struct {
	Date        string                `json:"date"`
	Device      BlockOutputDeviceInfo `json:"device"`
	GeoDuration float64               `json:"geo_duration"`
	Intersect   []BlockTrackingPoint  `json:"intersect"`
	CreatedAt   string                `json:"created_at"`
}

// BlockOutputDeviceInfo represents device information in the output format
type BlockOutputDeviceInfo struct {
	Division string `json:"division"`
	Estate   string `json:"estate"`
	Ident    string `json:"ident"`
	Name     string `json:"name"`
	NIK      string `json:"nik"`
	PSM      string `json:"psm"`
	Type     string `json:"type"`
}

// NewGeofBlockWorker creates a new GeofBlockWorker
func NewGeofBlockWorker(log *logger.Logger, config interface{}) (Worker, error) {
	return &GeofBlockWorker{
		log: log,
	}, nil
}

// Process processes a geofencing block task
func (w *GeofBlockWorker) Process(ctx context.Context, task *Task) error {
	startTime := time.Now()

	w.log.WithField("task_id", task.ID).Info("Starting geofencing block processing")

	// Parse the payload - try both formats (direct payload and API JobRequest format)
	var payload GeofBlockPayload

	// First try to unmarshal as JobRequest from API
	var jobRequest struct {
		WorkerType string `json:"worker_type"`
		Config     struct {
			Date       string               `json:"date"`
			Device     BlockDeviceInfo      `json:"device"`
			Tracking   []BlockTrackingPoint `json:"tracking"`
			GeojsTrees []BlockGeoJSTree     `json:"geojsTrees"`
		} `json:"config"`
		RescheduleCount int `json:"reschedule_count,omitempty"`
	}

	if err := json.Unmarshal(task.Payload, &jobRequest); err == nil && jobRequest.WorkerType == "geof_block" {
		// Convert JobRequest format to internal payload format
		payload = GeofBlockPayload{
			Date:            jobRequest.Config.Date,
			Device:          jobRequest.Config.Device,
			Tracking:        jobRequest.Config.Tracking,
			GeojsTrees:      jobRequest.Config.GeojsTrees,
			RescheduleCount: jobRequest.RescheduleCount,
		}
	} else {
		// Try to unmarshal as direct payload format
		if err := json.Unmarshal(task.Payload, &payload); err != nil {
			w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to unmarshal payload in both formats")

			// Log error to Redis
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"error":   err.Error(),
					"task_id": task.ID,
				}
				key := fmt.Sprintf("logs:geof_block:%s:error:%s", time.Now().Format("2006-01-02"), task.ID)
				if logErr := w.logWriter.LogFailure(key, "geof_block", metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write error log to Redis")
				}
			}
			return fmt.Errorf("failed to unmarshal payload: %w", err)
		}
	}

	w.log.WithFields(map[string]interface{}{
		"task_id":        task.ID,
		"date":           payload.Date,
		"device_ident":   payload.Device.Ident,
		"tracking_count": len(payload.Tracking),
		"trees_count":    len(payload.GeojsTrees),
	}).Info("Processing geofencing block task")

	// Perform geofencing calculations
	intersections, firstTreeCoords, err := w.calculateBlockIntersections(payload)
	if err != nil {
		w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to calculate intersections")

		// Log error to Redis
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":   err.Error(),
				"task_id": task.ID,
				"ident":   payload.Device.Ident,
			}
			key := fmt.Sprintf("logs:geof_block:%s:error:%s", payload.Date, task.ID)
			if logErr := w.logWriter.LogFailure(key, "geof_block", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write error log to Redis")
			}
		}
		return fmt.Errorf("failed to calculate intersections: %w", err)
	}

	// Calculate processing duration in seconds
	geoDuration := time.Since(startTime).Seconds()

	// Create result structure
	result := GeofBlockResult{
		Date: payload.Date,
		Device: BlockOutputDeviceInfo{
			Division: payload.Device.Division,
			Estate:   payload.Device.Estate,
			Ident:    payload.Device.Ident,
			Name:     payload.Device.Name,
			NIK:      payload.Device.NIK,
			PSM:      payload.Device.PSM,
			Type:     payload.Device.Type,
		},
		GeoDuration: geoDuration,
		Intersect:   intersections,
		CreatedAt:   time.Now().UTC().Format(time.RFC3339),
	}

	// Only store result in Redis if there are intersections
	if len(intersections) > 0 {
		if err := w.storeResultInRedis(result, firstTreeCoords); err != nil {
			w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to store result in Redis")

			// Log error to Redis
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"error":   err.Error(),
					"task_id": task.ID,
					"ident":   payload.Device.Ident,
				}
				key := fmt.Sprintf("logs:geof_block:%s:error:%s", payload.Date, task.ID)
				if logErr := w.logWriter.LogFailure(key, "geof_block", metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write error log to Redis")
				}
			}
			return fmt.Errorf("failed to store result in Redis: %w", err)
		}
	} else {
		w.log.WithFields(map[string]interface{}{
			"task_id":      task.ID,
			"geo_duration": geoDuration,
			"device_ident": payload.Device.Ident,
		}).Info("No intersections found, skipping Redis storage")
	}

	w.log.WithFields(map[string]interface{}{
		"task_id":       task.ID,
		"geo_duration":  geoDuration,
		"intersections": len(intersections),
	}).Info("Successfully completed geofencing block processing")

	return nil
}

// calculateBlockIntersections performs geofencing calculations to find intersections
// between tracking points (as 3-meter radius polygons) and tree locations (as 5-meter radius polygons)
func (w *GeofBlockWorker) calculateBlockIntersections(payload GeofBlockPayload) ([]BlockTrackingPoint, *BlockTreeProperties, error) {
	// Convert tracking points to 3-meter radius polygons
	var trackingPolygons []orb.Polygon
	for _, track := range payload.Tracking {
		polygon := w.createCirclePolygon(track.Longitude, track.Latitude, 3.0)
		trackingPolygons = append(trackingPolygons, polygon)
	}

	// Find the tree with the most intersections to use as the primary tree
	var bestTree *BlockTreeProperties
	var bestIntersections []BlockTrackingPoint
	maxIntersections := 0

	// Process each tree location
	for _, tree := range payload.GeojsTrees {
		// Convert tree point to 5-meter radius polygon
		var treePolygon orb.Polygon

		// Handle tree geometry - should be a Point
		geometry, err := w.convertGeoJSONToOrb(tree.Geometry)
		if err != nil {
			w.log.WithError(err).WithFields(map[string]interface{}{
				"point_x": tree.Properties.PointX,
				"point_y": tree.Properties.PointY,
				"blok":    tree.Properties.Blok,
			}).Warn("Failed to convert tree GeoJSON geometry, using properties coordinates")

			// Fallback to using properties coordinates
			treePolygon = w.createCirclePolygon(tree.Properties.PointX, tree.Properties.PointY, 5.0)
		} else {
			// Extract coordinates from geometry
			switch geom := geometry.(type) {
			case orb.Point:
				treePolygon = w.createCirclePolygon(geom[0], geom[1], 5.0)
			default:
				w.log.WithFields(map[string]interface{}{
					"point_x": tree.Properties.PointX,
					"point_y": tree.Properties.PointY,
					"blok":    tree.Properties.Blok,
				}).Warn("Tree geometry is not a Point, using properties coordinates")

				// Fallback to using properties coordinates
				treePolygon = w.createCirclePolygon(tree.Properties.PointX, tree.Properties.PointY, 5.0)
			}
		}

		// Find tracking points that intersect with this specific tree
		var currentTreeIntersections []BlockTrackingPoint
		for i, trackingPolygon := range trackingPolygons {
			if w.polygonIntersects(trackingPolygon, treePolygon) {
				currentTreeIntersections = append(currentTreeIntersections, payload.Tracking[i])
			}
		}

		// If this tree has more intersections than the current best, use it
		if len(currentTreeIntersections) > maxIntersections {
			maxIntersections = len(currentTreeIntersections)
			bestTree = &tree.Properties
			bestIntersections = currentTreeIntersections
		}
	}

	return bestIntersections, bestTree, nil
}

// createCirclePolygon creates a high-precision polygon representing a circle with the given center and radius in meters
func (w *GeofBlockWorker) createCirclePolygon(longitude, latitude, radiusMeters float64) orb.Polygon {
	const earthRadius = 6378137.0 // Earth radius in meters (WGS84)
	const numPoints = 32          // Increased points for better accuracy (was 16)

	var ring orb.Ring

	// Pre-calculate common values for better performance
	latRad := latitude * math.Pi / 180
	cosLat := math.Cos(latRad)

	// Calculate offset in radians for better precision
	latOffsetRad := radiusMeters / earthRadius
	lonOffsetRad := radiusMeters / (earthRadius * cosLat)

	for i := 0; i < numPoints; i++ {
		angle := float64(i) * 2 * math.Pi / float64(numPoints)

		// Use more precise trigonometric calculations
		sinAngle := math.Sin(angle)
		cosAngle := math.Cos(angle)

		// Calculate point coordinates in radians then convert to degrees
		pointLatRad := latRad + latOffsetRad*sinAngle
		pointLonRad := longitude*math.Pi/180 + lonOffsetRad*cosAngle

		// Convert back to degrees
		pointLat := pointLatRad * 180 / math.Pi
		pointLon := pointLonRad * 180 / math.Pi

		ring = append(ring, orb.Point{pointLon, pointLat})
	}

	// Close the ring by adding the first point at the end
	if len(ring) > 0 {
		ring = append(ring, ring[0])
	}

	return orb.Polygon{ring}
}

// polygonIntersects checks if two circular polygons intersect using distance-based algorithm
func (w *GeofBlockWorker) polygonIntersects(poly1, poly2 orb.Polygon) bool {
	// For circular polygons, use center-to-center distance comparison
	center1, radius1 := w.getPolygonCenterAndRadius(poly1)
	center2, radius2 := w.getPolygonCenterAndRadius(poly2)

	// Calculate distance between centers using Haversine formula
	distance := w.haversineDistance(center1[1], center1[0], center2[1], center2[0])

	// Two circles intersect if the distance between centers is less than or equal to sum of radii
	return distance <= (radius1 + radius2)
}

// boundingBoxesOverlap checks if bounding boxes of two polygons overlap
func (w *GeofBlockWorker) boundingBoxesOverlap(poly1, poly2 orb.Polygon) bool {
	bbox1 := w.getBoundingBox(poly1)
	bbox2 := w.getBoundingBox(poly2)

	// Check if bounding boxes overlap
	return !(bbox1.maxLon < bbox2.minLon || bbox2.maxLon < bbox1.minLon ||
		bbox1.maxLat < bbox2.minLat || bbox2.maxLat < bbox1.minLat)
}

// BoundingBox represents a bounding box
type BoundingBox struct {
	minLon, maxLon, minLat, maxLat float64
}

// getBoundingBox calculates the bounding box of a polygon
func (w *GeofBlockWorker) getBoundingBox(poly orb.Polygon) BoundingBox {
	if len(poly) == 0 || len(poly[0]) == 0 {
		return BoundingBox{}
	}

	bbox := BoundingBox{
		minLon: poly[0][0][0], maxLon: poly[0][0][0],
		minLat: poly[0][0][1], maxLat: poly[0][0][1],
	}

	for _, ring := range poly {
		for _, point := range ring {
			if point[0] < bbox.minLon {
				bbox.minLon = point[0]
			}
			if point[0] > bbox.maxLon {
				bbox.maxLon = point[0]
			}
			if point[1] < bbox.minLat {
				bbox.minLat = point[1]
			}
			if point[1] > bbox.maxLat {
				bbox.maxLat = point[1]
			}
		}
	}

	return bbox
}

// getPolygonCenterAndRadius calculates the center and radius of a circular polygon
func (w *GeofBlockWorker) getPolygonCenterAndRadius(poly orb.Polygon) (orb.Point, float64) {
	if len(poly) == 0 || len(poly[0]) == 0 {
		return orb.Point{0, 0}, 0
	}

	ring := poly[0]
	if len(ring) < 2 {
		return orb.Point{0, 0}, 0
	}

	// Calculate center as average of all points (excluding the closing point)
	var sumLon, sumLat float64
	pointCount := len(ring) - 1 // Exclude closing point

	for i := 0; i < pointCount; i++ {
		sumLon += ring[i][0]
		sumLat += ring[i][1]
	}

	center := orb.Point{sumLon / float64(pointCount), sumLat / float64(pointCount)}

	// Calculate radius as average distance from center to all points (more accurate for circles)
	var totalDistance float64
	for i := 0; i < pointCount; i++ {
		distance := w.haversineDistance(center[1], center[0], ring[i][1], ring[i][0])
		totalDistance += distance
	}
	radius := totalDistance / float64(pointCount)

	return center, radius
}

// haversineDistance calculates the distance between two points using Haversine formula
func (w *GeofBlockWorker) haversineDistance(lat1, lon1, lat2, lon2 float64) float64 {
	const earthRadius = 6378137.0 // Earth radius in meters (WGS84)

	// Convert degrees to radians
	lat1Rad := lat1 * math.Pi / 180
	lon1Rad := lon1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lon2Rad := lon2 * math.Pi / 180

	// Calculate differences
	deltaLat := lat2Rad - lat1Rad
	deltaLon := lon2Rad - lon1Rad

	// Haversine formula
	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLon/2)*math.Sin(deltaLon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return earthRadius * c
}

// lineSegmentsIntersect checks if two line segments intersect
func (w *GeofBlockWorker) lineSegmentsIntersect(p1, q1, p2, q2 orb.Point) bool {
	// Find the four orientations needed for general and special cases
	o1 := w.orientation(p1, q1, p2)
	o2 := w.orientation(p1, q1, q2)
	o3 := w.orientation(p2, q2, p1)
	o4 := w.orientation(p2, q2, q1)

	// General case
	if o1 != o2 && o3 != o4 {
		return true
	}

	// Special cases
	// p1, q1 and p2 are colinear and p2 lies on segment p1q1
	if o1 == 0 && w.onSegment(p1, p2, q1) {
		return true
	}

	// p1, q1 and q2 are colinear and q2 lies on segment p1q1
	if o2 == 0 && w.onSegment(p1, q2, q1) {
		return true
	}

	// p2, q2 and p1 are colinear and p1 lies on segment p2q2
	if o3 == 0 && w.onSegment(p2, p1, q2) {
		return true
	}

	// p2, q2 and q1 are colinear and q1 lies on segment p2q2
	if o4 == 0 && w.onSegment(p2, q1, q2) {
		return true
	}

	return false
}

// orientation finds orientation of ordered triplet (p, q, r)
// Returns 0 if p, q and r are colinear
// Returns 1 if clockwise
// Returns 2 if counterclockwise
func (w *GeofBlockWorker) orientation(p, q, r orb.Point) int {
	val := (q[1]-p[1])*(r[0]-q[0]) - (q[0]-p[0])*(r[1]-q[1])
	if math.Abs(val) < 1e-10 { // Use small epsilon for floating point comparison
		return 0 // colinear
	}
	if val > 0 {
		return 1 // clockwise
	}
	return 2 // counterclockwise
}

// onSegment checks if point q lies on line segment pr
func (w *GeofBlockWorker) onSegment(p, q, r orb.Point) bool {
	return q[0] <= math.Max(p[0], r[0]) && q[0] >= math.Min(p[0], r[0]) &&
		q[1] <= math.Max(p[1], r[1]) && q[1] >= math.Min(p[1], r[1])
}

// convertGeoJSONToOrb converts a GeoJSON geometry to orb.Geometry
func (w *GeofBlockWorker) convertGeoJSONToOrb(geometry map[string]interface{}) (orb.Geometry, error) {
	// Convert map to JSON bytes
	geometryBytes, err := json.Marshal(geometry)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal geometry: %w", err)
	}

	// Parse as GeoJSON geometry
	geoGeometry, err := geojson.UnmarshalGeometry(geometryBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal GeoJSON geometry: %w", err)
	}

	return geoGeometry.Geometry(), nil
}

// storeResultInRedis stores the geofencing result in Redis using JSON.SET
func (w *GeofBlockWorker) storeResultInRedis(result GeofBlockResult, firstTreeCoords *BlockTreeProperties) error {
	if w.logWriter == nil {
		return fmt.Errorf("log writer is not available")
	}

	// Store result with block-based key
	// Key format: "transaction:geof_block:YYYY-MM-DD:success:${ident}_${point_x}_${point_y}"
	// Use the first tree's coordinates if available, otherwise use device ident only
	var keyIdentifier string
	if len(result.Intersect) > 0 && firstTreeCoords != nil {
		// Use first intersecting tree's coordinates
		keyIdentifier = fmt.Sprintf("%s_%.8f_%.8f", result.Device.Ident, firstTreeCoords.PointX, firstTreeCoords.PointY)
	} else {
		// No intersections, use device ident only
		keyIdentifier = result.Device.Ident
	}

	key := fmt.Sprintf("transaction:geof_block:%s:success:%s", result.Date, keyIdentifier)

	// Create result with all intersections as a combined array
	// intersect structure: [{tree:{}, tracking:[]},{tree:{}, tracking:[]}]
	resultData := map[string]interface{}{
		"date":         result.Date,
		"device":       result.Device,
		"geo_duration": result.GeoDuration,
		"intersect":    result.Intersect, // All intersections as combined array
		"created_at":   result.CreatedAt,
	}

	// Store in Redis using logwriter's StoreInRedis method
	if err := w.logWriter.StoreInRedis(key, resultData); err != nil {
		w.log.WithError(err).WithFields(map[string]interface{}{
			"redis_key":      key,
			"key_identifier": keyIdentifier,
		}).Error("Failed to store geofencing result in Redis")
		return fmt.Errorf("failed to store result in Redis for key %s: %w", keyIdentifier, err)
	}

	w.log.WithFields(map[string]interface{}{
		"redis_key":      key,
		"key_identifier": keyIdentifier,
		"intersections":  len(result.Intersect),
		"geo_duration":   result.GeoDuration,
	}).Info("Successfully stored geofencing result in Redis")

	return nil
}

// Type returns the type of worker
func (w *GeofBlockWorker) Type() WorkerType {
	return "geof_block"
}

// SetLogWriter sets the log writer for the worker
func (w *GeofBlockWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker (not needed for this worker)
func (w *GeofBlockWorker) SetDispatcher(dispatcher interface{}) {
	// Not needed for geof_block worker
}
