package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/user/workers/pkg/influxdb"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// CaveoToInfluxWorker is a worker that reads Caveo files from Minio and writes to InfluxDB
type CaveoToInfluxWorker struct {
	log          *logger.Logger
	minioClient  *minio.Client
	minioBucket  string
	logWriter    *logwriter.LogWriter
	influxClient *influxdb.Client
}

// CaveoToInfluxConfig holds configuration for the Caveo to InfluxDB worker
type CaveoToInfluxConfig struct {
	Minio struct {
		Endpoint  string
		AccessKey string
		SecretKey string
		UseSSL    bool
	}
	Bucket   string
	InfluxDB struct {
		URL   string
		Token string
		Org   string
	}
}

// CaveoToInfluxPayload is the payload for the Caveo to InfluxDB worker
type CaveoToInfluxPayload struct {
	WorkerType string                     `json:"worker_type"`
	Config     CaveoToInfluxPayloadConfig `json:"config"`
}

// CaveoToInfluxPayloadConfig represents the configuration for the Caveo to InfluxDB worker
type CaveoToInfluxPayloadConfig struct {
	Date     string `json:"date"`
	IMEI     string `json:"imei"`
	Psm      string `json:"psm"`
	Estate   string `json:"estate"`
	Division string `json:"division"`
	NIK      string `json:"nik"`
	Type     string `json:"type"`
}

// NewCaveoToInfluxWorker creates a new Caveo to InfluxDB worker
func NewCaveoToInfluxWorker(log *logger.Logger, config interface{}) (Worker, error) {
	var caveoToInfluxConfig CaveoToInfluxConfig

	// Try to extract from main config
	mainCfg, ok := config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config type for Caveo to InfluxDB worker")
	}

	// Extract Minio config
	if minio, ok := mainCfg["Minio"].(map[string]interface{}); ok {
		if endpoint, ok := minio["Endpoint"].(string); ok {
			caveoToInfluxConfig.Minio.Endpoint = endpoint
		}
		if accessKey, ok := minio["AccessKey"].(string); ok {
			caveoToInfluxConfig.Minio.AccessKey = accessKey
		}
		if secretKey, ok := minio["SecretKey"].(string); ok {
			caveoToInfluxConfig.Minio.SecretKey = secretKey
		}
		if useSSL, ok := minio["UseSSL"].(bool); ok {
			caveoToInfluxConfig.Minio.UseSSL = useSSL
		}
		if bucket, ok := minio["Bucket"].(string); ok {
			caveoToInfluxConfig.Bucket = bucket
		}
	}

	// Try to get configuration from environment variables if not set
	if caveoToInfluxConfig.Minio.Endpoint == "" {
		caveoToInfluxConfig.Minio.Endpoint = os.Getenv("MINIO_ENDPOINT")
	}
	if caveoToInfluxConfig.Minio.AccessKey == "" {
		caveoToInfluxConfig.Minio.AccessKey = os.Getenv("MINIO_ACCESS_KEY")
	}
	if caveoToInfluxConfig.Minio.SecretKey == "" {
		caveoToInfluxConfig.Minio.SecretKey = os.Getenv("MINIO_SECRET_KEY")
	}
	if caveoToInfluxConfig.Bucket == "" {
		caveoToInfluxConfig.Bucket = "api-pull-caveo-files" // Default bucket
	}
	if !caveoToInfluxConfig.Minio.UseSSL {
		if useSSLStr := os.Getenv("MINIO_USE_SSL"); useSSLStr == "true" {
			caveoToInfluxConfig.Minio.UseSSL = true
		}
	}

	// Get InfluxDB configuration from environment variables
	caveoToInfluxConfig.InfluxDB.URL = fmt.Sprintf("http://localhost:%s", os.Getenv("INFLUX_PORT"))
	if caveoToInfluxConfig.InfluxDB.URL == "http://localhost:" {
		caveoToInfluxConfig.InfluxDB.URL = "http://localhost:8086" // Default
	}
	caveoToInfluxConfig.InfluxDB.Token = os.Getenv("INFLUX_TOKEN")
	caveoToInfluxConfig.InfluxDB.Org = os.Getenv("INFLUX_ORG")

	// Validate required configuration
	if caveoToInfluxConfig.Minio.Endpoint == "" {
		return nil, fmt.Errorf("minio endpoint is required")
	}
	if caveoToInfluxConfig.InfluxDB.Token == "" {
		return nil, fmt.Errorf("influxdb token is required")
	}
	if caveoToInfluxConfig.InfluxDB.Org == "" {
		return nil, fmt.Errorf("influxdb organization is required")
	}

	// Initialize Minio client
	var minioClient *minio.Client
	if caveoToInfluxConfig.Minio.Endpoint != "" {
		var err error
		minioClient, err = minio.New(caveoToInfluxConfig.Minio.Endpoint, &minio.Options{
			Creds:  credentials.NewStaticV4(caveoToInfluxConfig.Minio.AccessKey, caveoToInfluxConfig.Minio.SecretKey, ""),
			Secure: caveoToInfluxConfig.Minio.UseSSL,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to create Minio client: %w", err)
		}
	}

	// Initialize InfluxDB client
	log.WithFields(map[string]interface{}{
		"url":          caveoToInfluxConfig.InfluxDB.URL,
		"org":          caveoToInfluxConfig.InfluxDB.Org,
		"token_length": len(caveoToInfluxConfig.InfluxDB.Token),
	}).Info("Initializing InfluxDB client")

	influxClient, err := influxdb.NewInfluxDBClient(
		caveoToInfluxConfig.InfluxDB.URL,
		caveoToInfluxConfig.InfluxDB.Token,
		caveoToInfluxConfig.InfluxDB.Org,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create InfluxDB client: %w", err)
	}

	worker := &CaveoToInfluxWorker{
		log:          log,
		minioClient:  minioClient,
		minioBucket:  caveoToInfluxConfig.Bucket,
		logWriter:    nil, // Will be set later via SetLogWriter
		influxClient: influxClient,
	}

	return worker, nil
}

// Process processes a task
func (w *CaveoToInfluxWorker) Process(ctx context.Context, task *Task) error {
	w.log.WithField("task_id", task.ID).Info("Starting caveo_to_influx worker processing")

	// Try to unmarshal the payload as a CaveoToInfluxPayload
	var payload CaveoToInfluxPayload

	if err := json.Unmarshal(task.Payload, &payload); err != nil {
		w.log.WithError(err).Error("Failed to unmarshal payload")
		return fmt.Errorf("invalid payload: %w", err)
	}

	w.log.WithField("payload", string(task.Payload)).Info("Payload unmarshaled successfully")

	// Validate payload
	if payload.Config.Date == "" {
		w.log.Error("Date is required in payload config")
		return fmt.Errorf("date is required in payload config")
	}
	if payload.Config.IMEI == "" {
		w.log.Error("IMEI is required in payload config")
		return fmt.Errorf("imei is required in payload config")
	}

	w.log.WithFields(map[string]interface{}{
		"date": payload.Config.Date,
		"imei": payload.Config.IMEI,
	}).Info("Processing Caveo to InfluxDB task")

	// Create prefix for Minio objects
	prefix := fmt.Sprintf("%s/%s/", payload.Config.Date, payload.Config.IMEI)

	w.log.WithFields(map[string]interface{}{
		"prefix": prefix,
		"bucket": w.minioBucket,
	}).Info("Reading files from Minio bucket")

	// Check if Minio client is available
	if w.minioClient == nil {
		w.log.Error("Minio client is not available")
		return fmt.Errorf("minio client is not available")
	}

	// List objects with the prefix
	w.log.WithField("bucket", w.minioBucket).Info("Listing objects from Minio")
	objectCh := w.minioClient.ListObjects(ctx, w.minioBucket, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})

	fileCount := 0
	processedCount := 0
	for object := range objectCh {
		if object.Err != nil {
			w.log.WithError(object.Err).Error("Error listing objects")
			continue
		}

		w.log.WithFields(map[string]interface{}{
			"object_key": object.Key,
			"size":       object.Size,
		}).Info("Found object in Minio")

		// Get the object
		obj, err := w.minioClient.GetObject(ctx, w.minioBucket, object.Key, minio.GetObjectOptions{})
		if err != nil {
			w.log.WithError(err).WithField("object_key", object.Key).Error("Failed to get object")
			continue
		}

		// Read the object content using io.ReadAll for better handling
		content, err := io.ReadAll(obj)
		obj.Close()

		if err != nil {
			w.log.WithError(err).WithField("object_key", object.Key).Error("Failed to read object content")
			continue
		}

		// Check if content is empty
		if len(content) == 0 {
			w.log.WithField("object_key", object.Key).Warn("Object is empty, skipping")
			continue
		}

		w.log.WithFields(map[string]interface{}{
			"object_key":     object.Key,
			"content_length": len(content),
		}).Info("Successfully read object content")

		// Extract filename for logging
		filename := w.extractFilename(object.Key)

		// Parse JSON to validate it and extract data
		var docData influxdb.DocData
		if err := json.Unmarshal(content, &docData); err != nil {
			w.log.WithError(err).WithField("object_key", object.Key).Error("Invalid JSON content")
			// Still print the content even if it's not valid JSON
			fmt.Printf("File invalid content: %s\n", object.Key)
			fmt.Println("---")

			// Log failed file processing to Redis
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"worker_id":  task.ID,
					"filename":   filename,
					"object_key": object.Key,
					"error":      "Invalid JSON content",
					"date":       payload.Config.Date,
					"imei":       payload.Config.IMEI,
				}
				if logErr := w.logWriter.WriteLogWithDate("caveo_to_influx", "failed", payload.Config.Date, metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write failed log to Redis")
				}
			}
			continue
		}

		// Process data and write to InfluxDB
		if err := w.processAndWriteToInfluxDB(ctx, docData, payload); err != nil {
			w.log.WithError(err).WithField("object_key", object.Key).Error("Failed to write to InfluxDB")

			// Log failed file processing to Redis
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"worker_id":  task.ID,
					"filename":   filename,
					"object_key": object.Key,
					"error":      err.Error(),
					"date":       payload.Config.Date,
					"imei":       payload.Config.IMEI,
				}
				if logErr := w.logWriter.WriteLogWithDate("caveo_to_influx", "failed", payload.Config.Date, metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write failed log to Redis")
				}
			}
			continue
		}

		w.log.WithField("object_key", object.Key).Info("Successfully processed and written to InfluxDB")

		// Log successful file processing to Redis
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"worker_id":   task.ID,
				"filename":    filename,
				"object_key":  object.Key,
				"data_points": len(docData.Data),
				"date":        payload.Config.Date,
				"imei":        payload.Config.IMEI,
			}
			if logErr := w.logWriter.WriteLogWithDate("caveo_to_influx", "success", payload.Config.Date, metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write success log to Redis")
			}
		}

		processedCount++
		fileCount++
	}

	w.log.WithFields(map[string]interface{}{
		"files_found":     fileCount,
		"files_processed": processedCount,
	}).Info("Completed processing files")
	w.log.WithField("task_id", task.ID).Info("Caveo to InfluxDB worker processing completed successfully")

	return nil
}

// processAndWriteToInfluxDB processes the document data and writes it to InfluxDB
func (w *CaveoToInfluxWorker) processAndWriteToInfluxDB(ctx context.Context, docData influxdb.DocData, payload CaveoToInfluxPayload) error {
	// Create payload for InfluxDB
	influxPayload := influxdb.Payload{
		Ident:    payload.Config.IMEI,     // Use IMEI as ident
		Psm:      payload.Config.Psm,      // Default PSM value
		Estate:   payload.Config.Estate,   // Default Estate value
		Division: payload.Config.Division, // Default Division value
		Nik:      payload.Config.NIK,      // Default NIK value
		Type:     payload.Config.Type,     // Default NIK value
	}

	// Create bucket name using IMEI
	bucketName := fmt.Sprintf("caveo-%s", payload.Config.IMEI)

	w.log.WithFields(map[string]interface{}{
		"bucket_name": bucketName,
		"imei":        payload.Config.IMEI,
	}).Info("Creating InfluxDB bucket if it doesn't exist")

	// Create bucket if it doesn't exist
	err := w.influxClient.CreateBucket(ctx, bucketName)
	if err != nil {
		w.log.WithError(err).WithField("bucket_name", bucketName).Warn("Failed to create bucket, but continuing with write operation")
		// Don't return error here, as the bucket might already exist or we might still be able to write
	} else {
		w.log.WithField("bucket_name", bucketName).Info("Successfully ensured bucket exists")
	}

	// Write GPS data to InfluxDB
	err = w.influxClient.WriteGPSData(docData, influxPayload)
	if err != nil {
		return fmt.Errorf("failed to write GPS data to InfluxDB: %w", err)
	}

	w.log.WithFields(map[string]interface{}{
		"imei":        payload.Config.IMEI,
		"data_points": len(docData.Data),
		"bucket":      fmt.Sprintf("caveo-%s", influxPayload.Ident),
	}).Info("Successfully wrote data to InfluxDB")

	return nil
}

// Type returns the type of worker
func (w *CaveoToInfluxWorker) Type() WorkerType {
	return "caveo_to_influx"
}

// SetLogWriter sets the log writer for the worker
func (w *CaveoToInfluxWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker (not needed for this worker)
func (w *CaveoToInfluxWorker) SetDispatcher(dispatcher interface{}) {
	// This worker doesn't need a dispatcher
}

// extractFilename extracts the filename from a Minio object key
// Example: "api-pull-caveo-files/2025-05-28/868738070028481/868738070028481 00:00:00-02:00:00.json" -> "868738070028481-0000"
func (w *CaveoToInfluxWorker) extractFilename(objectKey string) string {
	// Split the path to get the filename
	parts := strings.Split(objectKey, "/")
	if len(parts) == 0 {
		return "unknown"
	}

	// Get the last part (filename)
	filename := parts[len(parts)-1]

	// Remove the .json extension
	filename = strings.TrimSuffix(filename, ".json")

	// Replace spaces and colons with dashes to create a clean filename
	// "868738070028481 00:00:00-02:00:00" -> "868738070028481-0000"
	filename = strings.ReplaceAll(filename, " ", "-")
	filename = strings.ReplaceAll(filename, ":", "")

	return filename
}
