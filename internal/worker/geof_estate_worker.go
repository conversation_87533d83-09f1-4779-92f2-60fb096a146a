package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/paulmach/orb"
	"github.com/paulmach/orb/geojson"
	"github.com/paulmach/orb/planar"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// GeofEstateWorker performs geofencing operations by calculating intersections
// between GPS tracking points (as 3-meter radius polygons) and estate division geometries
type GeofEstateWorker struct {
	log       *logger.Logger
	logWriter *logwriter.LogWriter
}

// GeofEstatePayload represents the input payload structure from RabbitMQ
type GeofEstatePayload struct {
	Date            string                `json:"date"`
	Device          EstateDeviceInfo      `json:"device"`
	Tracking        []EstateTrackingPoint `json:"tracking"`
	GeojsDivs       []EstateGeoJSDiv      `json:"geojsDivs"`
	RescheduleCount int                   `json:"reschedule_count"`
}

// EstateDeviceInfo represents device information for estate worker
type EstateDeviceInfo struct {
	Division string `json:"division"`
	Estate   string `json:"estate"`
	Ident    string `json:"ident"`
	Name     string `json:"name"`
	NIK      string `json:"nik"`
	PSM      string `json:"psm"`
	Type     string `json:"type"`
}

// EstateTrackingPoint represents a GPS tracking point for estate worker
type EstateTrackingPoint struct {
	Timestamp string  `json:"timestamp"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// EstateGeoJSDiv represents a GeoJSON division with properties and geometry for estate worker
type EstateGeoJSDiv struct {
	Type       string                 `json:"type"`
	Properties EstateDivProperties    `json:"properties"`
	Geometry   map[string]interface{} `json:"geometry"`
}

// EstateDivProperties represents the properties of a GeoJSON estate division
type EstateDivProperties struct {
	Divisi      string `json:"divisi"`
	Estate      string `json:"estate"`
	PeriodeUkur int    `json:"periode_ukur"`
	PSM         string `json:"psm"`
	PT          string `json:"pt"`
}

// GeofEstateResult represents the output structure to be stored in Redis
type GeofEstateResult struct {
	Date        string                 `json:"date"`
	Device      EstateOutputDeviceInfo `json:"device"`
	GeoDuration string                 `json:"geo_duration"`
	Intersect   []EstateIntersection   `json:"intersect"`
	CreatedAt   string                 `json:"created_at"`
}

// EstateOutputDeviceInfo represents device information in the output format
type EstateOutputDeviceInfo struct {
	Division string `json:"division"`
	Estate   string `json:"estate"`
	Ident    string `json:"ident"` // Note: use "ident" instead of "imei"
	Name     string `json:"name"`
	NIK      string `json:"nik"`
	PSM      string `json:"psm"`
	Type     string `json:"type"`
}

// EstateIntersection represents an intersection between tracking points and an estate division
type EstateIntersection struct {
	Division EstateDivisionInfo    `json:"division"`
	Tracking []EstateTrackingPoint `json:"tracking"`
}

// EstateDivisionInfo represents division information in intersection results
type EstateDivisionInfo struct {
	Divisi      string `json:"divisi"`
	Estate      string `json:"estate"`
	PeriodeUkur int    `json:"periode_ukur"`
	PSM         string `json:"psm"`
	PT          string `json:"pt"`
}

// NewGeofEstateWorker creates a new GeofEstateWorker
func NewGeofEstateWorker(log *logger.Logger, config interface{}) (Worker, error) {
	return &GeofEstateWorker{
		log: log,
	}, nil
}

// Process processes a geofencing estate task
func (w *GeofEstateWorker) Process(ctx context.Context, task *Task) error {
	startTime := time.Now()

	w.log.WithField("task_id", task.ID).Info("Starting geofencing estate processing")

	// Parse the payload - try both formats (direct payload and API JobRequest format)
	var payload GeofEstatePayload

	// First try to unmarshal as JobRequest from API
	var jobRequest struct {
		WorkerType string `json:"worker_type"`
		Config     struct {
			Date      string                `json:"date"`
			Device    EstateDeviceInfo      `json:"device"`
			Tracking  []EstateTrackingPoint `json:"tracking"`
			GeojsDivs []EstateGeoJSDiv      `json:"geojsDivs"`
		} `json:"config"`
		RescheduleCount int `json:"reschedule_count,omitempty"`
	}

	if err := json.Unmarshal(task.Payload, &jobRequest); err == nil && jobRequest.WorkerType == "geof_estate" {
		// Convert JobRequest format to internal payload format
		payload = GeofEstatePayload{
			Date:            jobRequest.Config.Date,
			Device:          jobRequest.Config.Device,
			Tracking:        jobRequest.Config.Tracking,
			GeojsDivs:       jobRequest.Config.GeojsDivs,
			RescheduleCount: jobRequest.RescheduleCount,
		}
	} else {
		// Try to unmarshal as direct payload format
		if err := json.Unmarshal(task.Payload, &payload); err != nil {
			w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to unmarshal payload in both formats")

			// Log error to Redis
			if w.logWriter != nil {
				metadata := map[string]interface{}{
					"error":   err.Error(),
					"task_id": task.ID,
				}
				key := fmt.Sprintf("logs:geof_estate:%s:error:%s", time.Now().Format("2006-01-02"), task.ID)
				if logErr := w.logWriter.LogFailure(key, string(GeofEstateWorkerType), metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write error log to Redis")
				}
			}
			return fmt.Errorf("failed to unmarshal payload: %w", err)
		}
	}

	w.log.WithFields(map[string]interface{}{
		"task_id":         task.ID,
		"date":            payload.Date,
		"device_ident":    payload.Device.Ident,
		"tracking_count":  len(payload.Tracking),
		"divisions_count": len(payload.GeojsDivs),
	}).Info("Processing geofencing estate task")

	// Perform geofencing calculations
	intersections, err := w.calculateEstateIntersections(payload)
	if err != nil {
		w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to calculate intersections")

		// Log error to Redis
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":   err.Error(),
				"task_id": task.ID,
				"ident":   payload.Device.Ident,
				"estate":  payload.Device.Estate,
			}
			key := fmt.Sprintf("logs:geof_estate:%s:error:%s", payload.Date, task.ID)
			if logErr := w.logWriter.LogFailure(key, string(GeofEstateWorkerType), metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write error log to Redis")
			}
		}
		return fmt.Errorf("failed to calculate intersections: %w", err)
	}

	// Calculate processing duration in milliseconds
	geoDuration := time.Since(startTime)
	geoDurationStr := fmt.Sprintf("%.1fms", float64(geoDuration.Nanoseconds())/1e6)

	// Create result structure
	result := GeofEstateResult{
		Date: payload.Date,
		Device: EstateOutputDeviceInfo{
			Division: payload.Device.Division,
			Estate:   payload.Device.Estate,
			Ident:    payload.Device.Ident, // Use ident field
			Name:     payload.Device.Name,
			NIK:      payload.Device.NIK,
			PSM:      payload.Device.PSM,
			Type:     payload.Device.Type,
		},
		GeoDuration: geoDurationStr,
		Intersect:   intersections,
		CreatedAt:   time.Now().UTC().Format(time.RFC3339),
	}

	// Store result in Redis
	if err := w.storeResultInRedis(result); err != nil {
		w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to store result in Redis")

		// Log error to Redis
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"error":   err.Error(),
				"task_id": task.ID,
				"ident":   payload.Device.Ident,
				"estate":  payload.Device.Estate,
			}
			key := fmt.Sprintf("logs:geof_estate:%s:error:%s", payload.Date, task.ID)
			if logErr := w.logWriter.LogFailure(key, string(GeofEstateWorkerType), metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write error log to Redis")
			}
		}
		return fmt.Errorf("failed to store result in Redis: %w", err)
	}

	w.log.WithFields(map[string]interface{}{
		"task_id":       task.ID,
		"geo_duration":  geoDurationStr,
		"intersections": len(intersections),
	}).Info("Successfully completed geofencing estate processing")

	return nil
}

// calculateEstateIntersections performs geofencing calculations to find intersections
// between tracking points (as 3-meter radius polygons) and estate division geometries
func (w *GeofEstateWorker) calculateEstateIntersections(payload GeofEstatePayload) ([]EstateIntersection, error) {
	var intersections []EstateIntersection

	// Convert tracking points to 3-meter radius polygons
	var trackingPolygons []orb.Polygon
	for _, track := range payload.Tracking {
		polygon := w.createCirclePolygon(track.Longitude, track.Latitude, 3.0)
		trackingPolygons = append(trackingPolygons, polygon)
	}

	// Process each estate division
	for _, division := range payload.GeojsDivs {
		// Convert GeoJSON geometry to orb.Geometry
		geometry, err := w.convertGeoJSONToOrb(division.Geometry)
		if err != nil {
			w.log.WithError(err).WithField("division", division.Properties.Divisi).Warn("Failed to convert GeoJSON geometry, skipping division")
			continue
		}

		// Handle different geometry types
		var divisionGeometry orb.Geometry
		switch geom := geometry.(type) {
		case orb.Polygon:
			divisionGeometry = geom
		case orb.MultiPolygon:
			divisionGeometry = geom
		default:
			w.log.WithField("division", division.Properties.Divisi).Warn("Geometry is not a polygon or multipolygon, skipping division")
			continue
		}

		// Find tracking points that intersect with this division
		var intersectingTracks []EstateTrackingPoint
		for i, trackingPolygon := range trackingPolygons {
			if w.geometryIntersects(trackingPolygon, divisionGeometry) {
				intersectingTracks = append(intersectingTracks, payload.Tracking[i])
			}
		}

		// If there are intersecting tracks, add to results
		if len(intersectingTracks) > 0 {
			intersection := EstateIntersection{
				Division: EstateDivisionInfo{
					Divisi:      division.Properties.Divisi,
					Estate:      division.Properties.Estate,
					PeriodeUkur: division.Properties.PeriodeUkur,
					PSM:         division.Properties.PSM,
					PT:          division.Properties.PT,
				},
				Tracking: intersectingTracks,
			}
			intersections = append(intersections, intersection)
		}
	}

	return intersections, nil
}

// createCirclePolygon creates a polygon representing a circle with the given center and radius in meters
func (w *GeofEstateWorker) createCirclePolygon(longitude, latitude, radiusMeters float64) orb.Polygon {
	const earthRadius = 6378137.0 // Earth radius in meters
	const numPoints = 16          // Number of points to approximate the circle

	var ring orb.Ring

	for i := 0; i < numPoints; i++ {
		angle := float64(i) * 2 * math.Pi / float64(numPoints)

		// Calculate offset in degrees
		latOffset := (radiusMeters / earthRadius) * (180 / math.Pi)
		lonOffset := (radiusMeters / earthRadius) * (180 / math.Pi) / math.Cos(latitude*math.Pi/180)

		// Calculate point coordinates
		pointLat := latitude + latOffset*math.Sin(angle)
		pointLon := longitude + lonOffset*math.Cos(angle)

		ring = append(ring, orb.Point{pointLon, pointLat})
	}

	// Close the ring by adding the first point at the end
	if len(ring) > 0 {
		ring = append(ring, ring[0])
	}

	return orb.Polygon{ring}
}

// geometryIntersects checks if a tracking polygon intersects with a division geometry
func (w *GeofEstateWorker) geometryIntersects(trackingPolygon orb.Polygon, divisionGeometry orb.Geometry) bool {
	switch geom := divisionGeometry.(type) {
	case orb.Polygon:
		return w.polygonIntersects(trackingPolygon, geom)
	case orb.MultiPolygon:
		for _, polygon := range geom {
			if w.polygonIntersects(trackingPolygon, polygon) {
				return true
			}
		}
		return false
	default:
		return false
	}
}

// polygonIntersects checks if two polygons intersect
func (w *GeofEstateWorker) polygonIntersects(poly1, poly2 orb.Polygon) bool {
	// Check if any point of poly1 is inside poly2
	for _, ring := range poly1 {
		for _, point := range ring {
			if planar.PolygonContains(poly2, point) {
				return true
			}
		}
	}

	// Check if any point of poly2 is inside poly1
	for _, ring := range poly2 {
		for _, point := range ring {
			if planar.PolygonContains(poly1, point) {
				return true
			}
		}
	}

	// Check for edge intersections (simplified check)
	// This is a basic implementation - for production use, consider more robust intersection algorithms
	return false
}

// convertGeoJSONToOrb converts a GeoJSON geometry to orb.Geometry
func (w *GeofEstateWorker) convertGeoJSONToOrb(geometry map[string]interface{}) (orb.Geometry, error) {
	// Convert map to JSON bytes
	geometryBytes, err := json.Marshal(geometry)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal geometry: %w", err)
	}

	// Parse as GeoJSON geometry
	geoGeometry, err := geojson.UnmarshalGeometry(geometryBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal GeoJSON geometry: %w", err)
	}

	return geoGeometry.Geometry(), nil
}

// storeResultInRedis stores the geofencing result in Redis using JSON.SET
func (w *GeofEstateWorker) storeResultInRedis(result GeofEstateResult) error {
	if w.logWriter == nil {
		return fmt.Errorf("log writer is not available")
	}

	// Store result with estate-based key
	// Key format: "transaction:geof_estate:YYYY-MM-DD:success:${ident}_${estate}"
	// Replace spaces in estate with underscores for key compatibility
	estate := strings.ReplaceAll(result.Device.Estate, " ", "_")
	key := fmt.Sprintf("transaction:geof_estate:%s:success:%s_%s",
		result.Date, result.Device.Ident, estate)

	// Create result with all intersections as a combined array
	// intersect structure: [{division:{}, tracking:[]},{division:{}, tracking:[]}]
	resultData := map[string]interface{}{
		"date":         result.Date,
		"device":       result.Device,
		"geo_duration": result.GeoDuration,
		"intersect":    result.Intersect, // All intersections as combined array
		"created_at":   result.CreatedAt,
	}

	// Store in Redis using logwriter's StoreInRedis method
	if err := w.logWriter.StoreInRedis(key, resultData); err != nil {
		w.log.WithError(err).WithFields(map[string]interface{}{
			"redis_key": key,
			"estate":    estate,
		}).Error("Failed to store geofencing result in Redis")
		return fmt.Errorf("failed to store result in Redis for estate %s: %w", estate, err)
	}

	w.log.WithFields(map[string]interface{}{
		"redis_key":     key,
		"estate":        estate,
		"intersections": len(result.Intersect),
		"geo_duration":  result.GeoDuration,
	}).Info("Successfully stored geofencing result in Redis")

	return nil
}

// Type returns the type of worker
func (w *GeofEstateWorker) Type() WorkerType {
	return "geof_estate"
}

// SetLogWriter sets the log writer for the worker
func (w *GeofEstateWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker (not needed for this worker)
func (w *GeofEstateWorker) SetDispatcher(dispatcher interface{}) {
	// Not needed for geof_estate worker
}
