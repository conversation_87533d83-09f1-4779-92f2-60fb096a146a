package worker

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"github.com/user/workers/pkg/logger"
)

// TaskResult represents the result of a task
type TaskResult struct {
	Task  *Task
	Error error
	// IsTimeout indicates if the error is a timeout error
	IsTimeout bool
}

// Pool represents a worker pool
type Pool struct {
	workers     map[WorkerType]Worker
	taskQueue   chan *Task
	resultChan  chan TaskResult
	log         *logger.Logger
	wg          sync.WaitGroup
	workerCount int
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewPool creates a new worker pool
func NewPool(ctx context.Context, workerCount int, log *logger.Logger) *Pool {
	ctx, cancel := context.WithCancel(ctx)

	return &Pool{
		workers:     make(map[WorkerType]Worker),
		taskQueue:   make(chan *Task, workerCount*2),      // Buffer size is twice the worker count
		resultChan:  make(chan TaskResult, workerCount*2), // Buffer size is twice the worker count
		log:         log,
		workerCount: workerCount,
		ctx:         ctx,
		cancel:      cancel,
	}
}

// RegisterWorker registers a worker with the pool
func (p *Pool) RegisterWorker(worker Worker) {
	p.workers[worker.Type()] = worker
	p.log.WithField("worker_type", worker.Type()).Info("Worker registered")
}

// Start starts the worker pool
func (p *Pool) Start() {
	p.log.WithField("worker_count", p.workerCount).Info("Starting worker pool")

	// Start workers
	for i := 0; i < p.workerCount; i++ {
		p.wg.Add(1)
		go p.startWorker(i)
	}
}

// startWorker starts a worker goroutine
func (p *Pool) startWorker(id int) {
	defer p.wg.Done()

	p.log.WithField("worker_id", id).Debug("Worker started")

	for {
		select {
		case <-p.ctx.Done():
			p.log.WithField("worker_id", id).Debug("Worker shutting down")
			return
		case task := <-p.taskQueue:
			p.processTask(id, task)
		}
	}
}

// processTask processes a task
func (p *Pool) processTask(workerID int, task *Task) {
	logger := p.log.WithField("worker_id", workerID).
		WithField("task_id", task.ID).
		WithField("task_type", task.Type)

	// Get the appropriate worker for this task
	worker, exists := p.workers[task.Type]
	if !exists {
		logger.Error("No worker registered for this task type")
		// Send result with error
		p.sendResult(task, errors.New("no worker registered for this task type"), false)
		return
	}

	// Process the task
	err := worker.Process(p.ctx, task)
	if err != nil {
		logger.WithError(err).Error("Failed to process task")

		// Check if it's a timeout error
		isTimeout := false
		if strings.Contains(err.Error(), "TIMEOUT_ERROR") ||
			strings.Contains(err.Error(), "timeout") ||
			strings.Contains(err.Error(), "deadline exceeded") ||
			strings.Contains(err.Error(), "Client.Timeout") {
			isTimeout = true
			logger.WithError(err).Warn("Task failed due to timeout, will be requeued")
		}

		// Send result with error
		p.sendResult(task, err, isTimeout)
		return
	}

	// Send successful result
	p.sendResult(task, nil, false)
}

// sendResult sends a task result to the result channel
func (p *Pool) sendResult(task *Task, err error, isTimeout bool) {
	logger := p.log.WithField("task_id", task.ID).WithField("task_type", task.Type)

	select {
	case <-p.ctx.Done():
		logger.Warn("Worker pool is shutting down, result discarded")
		return
	case p.resultChan <- TaskResult{
		Task:      task,
		Error:     err,
		IsTimeout: isTimeout,
	}:
		// Result sent successfully
		if err != nil {
			if isTimeout {
				logger.WithError(err).Warn("Task result with timeout error sent to result channel")
			} else {
				logger.WithError(err).Error("Task result with error sent to result channel")
			}
		} else {
			logger.Info("Successful task result sent to result channel")
		}
	default:
		// This should rarely happen as the channel is buffered
		logger.Error("Failed to send task result: result channel is full")
		// Try again after a short delay
		go func() {
			time.Sleep(100 * time.Millisecond)
			p.sendResult(task, err, isTimeout)
		}()
	}
}

// Submit submits a task to the worker pool
func (p *Pool) Submit(task *Task) {
	select {
	case <-p.ctx.Done():
		p.log.WithField("task_id", task.ID).Warn("Worker pool is shutting down, task rejected")
		return
	case p.taskQueue <- task:
		p.log.WithField("task_id", task.ID)
	}
}

// GetResults returns a channel that receives task results
func (p *Pool) GetResults() <-chan TaskResult {
	return p.resultChan
}

// Shutdown gracefully shuts down the worker pool
func (p *Pool) Shutdown() {
	p.log.Info("Shutting down worker pool")

	// Signal all workers to stop
	p.cancel()

	// Wait for all workers to finish
	p.wg.Wait()

	p.log.Info("Worker pool shutdown complete")
}

// GetRegisteredWorkers returns a map of registered workers
func (p *Pool) GetRegisteredWorkers() map[WorkerType]Worker {
	return p.workers
}
