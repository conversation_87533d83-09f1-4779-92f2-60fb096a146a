package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/paulmach/orb"
	"github.com/paulmach/orb/geojson"
	"github.com/paulmach/orb/planar"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// GeofDivisionWorker performs geofencing operations by calculating intersections
// between GPS tracking points and GeoJSON polygon blocks
type GeofDivisionWorker struct {
	log       *logger.Logger
	logWriter *logwriter.LogWriter
}

// GeofDivisionPayload represents the input payload structure from RabbitMQ
type GeofDivisionPayload struct {
	Date            string          `json:"date"`
	Device          DeviceInfo      `json:"device"`
	Division        string          `json:"division"`
	Tracking        []TrackingPoint `json:"tracking"`
	GeojsBlocks     []GeoJSBlock    `json:"geojsBlocks"`
	RescheduleCount int             `json:"reschedule_count"`
}

// DeviceInfo represents device information
type DeviceInfo struct {
	Division string `json:"division"`
	Estate   string `json:"estate"`
	Ident    string `json:"ident"`
	Name     string `json:"name"`
	NIK      string `json:"nik"`
	PSM      string `json:"psm"`
	Type     string `json:"type"`
}

// TrackingPoint represents a GPS tracking point
type TrackingPoint struct {
	Timestamp string  `json:"timestamp"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// GeoJSBlock represents a GeoJSON block with properties and geometry
type GeoJSBlock struct {
	Type       string                 `json:"type"`
	Properties BlockProperties        `json:"properties"`
	Geometry   map[string]interface{} `json:"geometry"`
}

// BlockProperties represents the properties of a GeoJSON block
type BlockProperties struct {
	Blok        string `json:"blok"`
	Divisi      string `json:"divisi"`
	Estate      string `json:"estate"`
	PeriodeUkur int    `json:"periode_ukur"`
	PSM         string `json:"psm"`
	PT          string `json:"pt"`
}

// GeofDivisionResult represents the output structure to be stored in Redis
type GeofDivisionResult struct {
	Date        string           `json:"date"`
	Device      OutputDeviceInfo `json:"device"`
	GeoDuration float64          `json:"geo_duration"`
	Intersect   []Intersection   `json:"intersect"`
	CreatedAt   string           `json:"created_at"`
}

// OutputDeviceInfo represents device information in the output format
type OutputDeviceInfo struct {
	Division string `json:"division"`
	Estate   string `json:"estate"`
	Ident    string `json:"ident"` // Note: use "ident" instead of "imei"
	Name     string `json:"name"`
	NIK      string `json:"nik"`
	PSM      string `json:"psm"`
	Type     string `json:"type"`
}

// Intersection represents an intersection between tracking points and a block
type Intersection struct {
	Block    IntersectionBlock `json:"block"`
	Tracking []TrackingPoint   `json:"tracking"`
}

// IntersectionBlock represents block information in intersection results
type IntersectionBlock struct {
	PSM         string `json:"psm"`
	PT          string `json:"pt"`
	Estate      string `json:"estate"`
	Divisi      string `json:"divisi"`
	Blok        string `json:"blok"`
	PeriodeUkur int    `json:"periode_ukur"`
}

// NewGeofDivisionWorker creates a new GeofDivisionWorker
func NewGeofDivisionWorker(log *logger.Logger, config interface{}) (Worker, error) {
	return &GeofDivisionWorker{
		log: log,
	}, nil
}

// Process processes a geofencing task
func (w *GeofDivisionWorker) Process(ctx context.Context, task *Task) error {
	startTime := time.Now()

	w.log.WithField("task_id", task.ID).Info("Starting geofencing division processing")

	// Parse the payload
	var payload GeofDivisionPayload
	if err := json.Unmarshal(task.Payload, &payload); err != nil {
		w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to unmarshal payload")
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	w.log.WithFields(map[string]interface{}{
		"task_id":        task.ID,
		"date":           payload.Date,
		"device_imei":    payload.Device.Ident,
		"tracking_count": len(payload.Tracking),
		"blocks_count":   len(payload.GeojsBlocks),
	}).Info("Processing geofencing task")

	// Perform geofencing calculations
	intersections, err := w.calculateIntersections(payload)
	if err != nil {
		w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to calculate intersections")
		return fmt.Errorf("failed to calculate intersections: %w", err)
	}

	// Calculate processing duration
	geoDuration := time.Since(startTime).Seconds()

	// Create result structure
	result := GeofDivisionResult{
		Date: payload.Date,
		Device: OutputDeviceInfo{
			Division: payload.Device.Division,
			Estate:   payload.Device.Estate,
			Ident:    payload.Device.Ident, // Use ident field
			Name:     payload.Device.Name,
			NIK:      payload.Device.NIK,
			PSM:      payload.Device.PSM,
			Type:     payload.Device.Type,
		},
		GeoDuration: geoDuration,
		Intersect:   intersections,
		CreatedAt:   time.Now().UTC().Format(time.RFC3339),
	}

	// Store result in Redis
	if err := w.storeResultInRedis(result); err != nil {
		w.log.WithError(err).WithField("task_id", task.ID).Error("Failed to store result in Redis")
		return fmt.Errorf("failed to store result in Redis: %w", err)
	}

	w.log.WithFields(map[string]interface{}{
		"task_id":       task.ID,
		"geo_duration":  geoDuration,
		"intersections": len(intersections),
	}).Info("Successfully completed geofencing division processing")

	return nil
}

// calculateIntersections performs geofencing calculations to find intersections
// between tracking points and GeoJSON polygon blocks
func (w *GeofDivisionWorker) calculateIntersections(payload GeofDivisionPayload) ([]Intersection, error) {
	var intersections []Intersection

	// Convert tracking points to orb.Point objects
	var trackingPoints []orb.Point
	for _, track := range payload.Tracking {
		point := orb.Point{track.Longitude, track.Latitude}
		trackingPoints = append(trackingPoints, point)
	}

	// Process each GeoJSON block
	for _, block := range payload.GeojsBlocks {
		// Convert GeoJSON geometry to orb.Geometry
		geometry, err := w.convertGeoJSONToOrb(block.Geometry)
		if err != nil {
			w.log.WithError(err).WithField("block", block.Properties.Blok).Warn("Failed to convert GeoJSON geometry, skipping block")
			continue
		}

		// Find tracking points that intersect with this geometry
		var intersectingTracks []TrackingPoint

		// Handle different geometry types
		switch geom := geometry.(type) {
		case orb.Polygon:
			// Handle single polygon
			intersectingTracks = w.********************************(geom, trackingPoints, payload.Tracking)

		case orb.MultiPolygon:
			// Handle multipolygon - check intersections with each polygon within it
			for _, polygon := range geom {
				polygonTracks := w.********************************(polygon, trackingPoints, payload.Tracking)
				// Merge tracks, avoiding duplicates
				intersectingTracks = w.mergeTrackingPoints(intersectingTracks, polygonTracks)
			}

		default:
			// Skip geometries that are neither Polygon nor MultiPolygon (Point, LineString, etc.)
			w.log.WithField("block", block.Properties.Blok).Warn("Geometry is neither polygon nor multipolygon, skipping block")
			continue
		}

		// If there are intersecting tracks, add to results
		if len(intersectingTracks) > 0 {
			intersection := Intersection{
				Block: IntersectionBlock{
					PSM:         block.Properties.PSM,
					PT:          block.Properties.PT,
					Estate:      block.Properties.Estate,
					Divisi:      block.Properties.Divisi,
					Blok:        block.Properties.Blok,
					PeriodeUkur: block.Properties.PeriodeUkur,
				},
				Tracking: intersectingTracks,
			}
			intersections = append(intersections, intersection)
		}
	}

	return intersections, nil
}

// convertGeoJSONToOrb converts a GeoJSON geometry to orb.Geometry
func (w *GeofDivisionWorker) convertGeoJSONToOrb(geometry map[string]interface{}) (orb.Geometry, error) {
	// Convert map to JSON bytes
	geometryBytes, err := json.Marshal(geometry)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal geometry: %w", err)
	}

	// Parse as GeoJSON geometry
	geoGeometry, err := geojson.UnmarshalGeometry(geometryBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal GeoJSON geometry: %w", err)
	}

	return geoGeometry.Geometry(), nil
}

// ******************************** finds tracking points that intersect with a single polygon
func (w *GeofDivisionWorker) ********************************(polygon orb.Polygon, trackingPoints []orb.Point, originalTracks []TrackingPoint) []TrackingPoint {
	var intersectingTracks []TrackingPoint
	for i, point := range trackingPoints {
		if planar.PolygonContains(polygon, point) {
			intersectingTracks = append(intersectingTracks, originalTracks[i])
		}
	}
	return intersectingTracks
}

// mergeTrackingPoints merges two slices of tracking points, avoiding duplicates
// Duplicates are identified by matching timestamp, latitude, and longitude
func (w *GeofDivisionWorker) mergeTrackingPoints(existing, new []TrackingPoint) []TrackingPoint {
	// Create a map to track existing points for efficient duplicate detection
	existingMap := make(map[string]bool)
	for _, track := range existing {
		key := fmt.Sprintf("%s_%.6f_%.6f", track.Timestamp, track.Latitude, track.Longitude)
		existingMap[key] = true
	}

	// Start with existing tracks
	result := make([]TrackingPoint, len(existing))
	copy(result, existing)

	// Add new tracks that don't already exist
	for _, track := range new {
		key := fmt.Sprintf("%s_%.6f_%.6f", track.Timestamp, track.Latitude, track.Longitude)
		if !existingMap[key] {
			result = append(result, track)
			existingMap[key] = true
		}
	}

	return result
}

// storeResultInRedis stores the geofencing result in Redis using JSON.SET
func (w *GeofDivisionWorker) storeResultInRedis(result GeofDivisionResult) error {
	if w.logWriter == nil {
		return fmt.Errorf("log writer is not available")
	}

	// Store result with division-based key
	// Key format: "transaction:geof_division:YYYY-MM-DD:success:${ident}_${division}"
	// Replace spaces in division with underscores for key compatibility
	division := strings.ReplaceAll(result.Device.Division, " ", "_")
	key := fmt.Sprintf("transaction:geof_division:%s:success:%s_%s",
		result.Date, result.Device.Ident, division)

	// Create result with all intersections as a combined array
	// intersect structure: [{block:{}, tracking:[]},{block:{}, tracking:[]}]
	resultData := map[string]interface{}{
		"date":         result.Date,
		"device":       result.Device,
		"geo_duration": result.GeoDuration,
		"intersect":    result.Intersect, // All intersections as combined array
		"created_at":   result.CreatedAt,
	}

	// Store in Redis using logwriter's StoreInRedis method
	if err := w.logWriter.StoreInRedis(key, resultData); err != nil {
		w.log.WithError(err).WithFields(map[string]interface{}{
			"redis_key": key,
			"division":  division,
		}).Error("Failed to store geofencing result in Redis")
		return fmt.Errorf("failed to store result in Redis for division %s: %w", division, err)
	}

	w.log.WithFields(map[string]interface{}{
		"redis_key":     key,
		"division":      division,
		"intersections": len(result.Intersect),
		"geo_duration":  result.GeoDuration,
	}).Info("Successfully stored geofencing result in Redis")

	return nil
}

// Type returns the type of worker
func (w *GeofDivisionWorker) Type() WorkerType {
	return "geof_division"
}

// SetLogWriter sets the log writer for the worker
func (w *GeofDivisionWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker (not needed for this worker)
func (w *GeofDivisionWorker) SetDispatcher(dispatcher interface{}) {
	// Not needed for geof_division worker
}
