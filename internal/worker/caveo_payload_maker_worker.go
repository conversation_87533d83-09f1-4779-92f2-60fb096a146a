package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/google/uuid"
	"github.com/user/workers/pkg/couchdb"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// CaveoPayloadMakerWorker is a worker that prints a message to the terminal
type CaveoPayloadMakerWorker struct {
	log         *logger.Logger
	logWriter   *logwriter.LogWriter
	couchClient *couchdb.Client
	dispatcher  interface{} // Dispatcher interface for sending tasks
}

// CaveoPayloadMakerConfig is the configuration for the Caveo Payload Maker worker
type CaveoPayloadMakerConfig struct {
	Message string `json:"message"`
}

type WorkerConfig struct {
	WorkerType string  `json:"worker_type"`
	Payload    Payload `json:"payload"`
}

type Payload struct {
	Config Config `json:"config"`
}

type Config struct {
	Device    Device `json:"device"`
	TimeStart string `json:"timeStart"`
	TimeEnd   string `json:"timeEnd"`
}

type Device struct {
	Psm      string `json:"psm"`
	Estate   string `json:"estate"`
	Division string `json:"division"`
	Ident    string `json:"ident"`
	Nik      string `json:"nik"`
	Name     string `json:"name"`
	Type     string `json:"type"`
}

// NewCaveoPayloadMakerWorker creates a new Caveo Payload Maker worker
func NewCaveoPayloadMakerWorker(log *logger.Logger, _ interface{}) (Worker, error) {
	// Create CouchDB client
	configPath := "config/couchdb_config.json"

	// Check if the file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// Try with a relative path
		configPath = "../config/couchdb_config.json"
		if _, err := os.Stat(configPath); os.IsNotExist(err) {
			// Try with another relative path
			configPath = "../../config/couchdb_config.json"
		}
	}

	couchClient, err := couchdb.NewClient(configPath)
	if err != nil {
		log.WithError(err).Error("Failed to create CouchDB client")
		return nil, fmt.Errorf("failed to create CouchDB client: %w", err)
	}

	return &CaveoPayloadMakerWorker{
		log:         log,
		couchClient: couchClient,
	}, nil
}

// Process processes a task
func (w *CaveoPayloadMakerWorker) Process(ctx context.Context, task *Task) error {
	// Try to unmarshal the payload
	var config struct {
		Message string `json:"message"`
	}
	if err := json.Unmarshal(task.Payload, &config); err != nil {
		w.log.WithError(err).Error("Failed to unmarshal config")
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Print the message to the terminal
	message := config.Message

	// Get document from CouchDB using the View method
	// Use the exact key format as in the curl command
	viewResp, err := w.couchClient.View(ctx, "doc-master", "references", "data", "by-topic", "\"wt-party-ident\"", true)

	if err != nil {
		w.log.WithError(err).Error("Failed to get document from CouchDB")

		// Log failure if log writer is available
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"task_id": task.ID,
				"message": message,
				"error":   err.Error(),
			}

			key := fmt.Sprintf("logs:caveo-payload-maker:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(key, "caveo_payload_maker", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}

		return fmt.Errorf("failed to get document from CouchDB: %w", err)
	}

	// Check if we have any rows
	if len(viewResp.Rows) == 0 {
		errMsg := "No documents found in CouchDB view"
		w.log.Error(errMsg)

		// Log failure if log writer is available
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"task_id": task.ID,
				"message": message,
				"error":   errMsg,
			}

			key := fmt.Sprintf("logs:caveo-payload-maker:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(key, "caveo_payload_maker", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}

		return fmt.Errorf("%s", errMsg)
	}

	// Get the data from the first row's document
	data, ok := viewResp.Rows[0].Doc["data"]
	if !ok {
		errMsg := "Document does not contain 'data' field"
		w.log.Error(errMsg)

		// Log failure if log writer is available
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"task_id": task.ID,
				"message": message,
				"error":   errMsg,
			}

			key := fmt.Sprintf("logs:caveo-payload-maker:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(key, "caveo_payload_maker", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}

		return fmt.Errorf("%s", errMsg)
	}

	// Convert data to a slice to get its length
	dataSlice, ok := data.([]interface{})
	if !ok {
		errMsg := "Data field is not a slice"
		w.log.Error(errMsg)

		// Log failure if log writer is available
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"task_id": task.ID,
				"message": message,
				"error":   errMsg,
			}

			key := fmt.Sprintf("logs:caveo-payload-maker:%s:failed:%s:%s", time.Now().Format("2006-01-02"), task.ID)
			if logErr := w.logWriter.LogFailure(key, "caveo_payload_maker", metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}

		return fmt.Errorf("%s", errMsg)
	}

	// Print the length of the data
	// Get current time
	now := time.Now()

	// Hitung 3 hari yang lalu
	yesterday := now.AddDate(0, 0, -1)

	// Buat start time jam 09:00 dan end time jam 11:00
	startTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 5, 0, 0, 0, yesterday.Location())
	endTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 7, 0, 0, 0, yesterday.Location())

	// Format ke string jika perlu
	start := startTime.Format("2006-01-02 15:04:05")
	end := endTime.Format("2006-01-02 15:04:05")

	var results []WorkerConfig

	for _, rawItem := range dataSlice {
		item, ok := rawItem.(map[string]interface{})
		if !ok {
			continue
		}

		device := Device{
			Psm:      convertToString(item["psm"]),
			Estate:   convertToString(item["estate"]),
			Division: convertToString(item["division"]),
			Ident:    convertToString(item["ident"]),
			Nik:      convertToString(item["nik"]),
			Name:     convertToString(item["name"]),
			Type:     convertToString(item["type"]),
		}
		wc := WorkerConfig{
			WorkerType: "api_pull_caveo",
			Payload: Payload{
				Config: Config{
					Device:    device,
					TimeStart: start,
					TimeEnd:   end,
				},
			},
		}
		results = append(results, wc)
	}

	// Send each result as a task to the api_pull_caveo worker queue
	if w.dispatcher != nil {
		// Type assert the dispatcher to get the Dispatch method
		type Dispatcher interface {
			Dispatch(ctx context.Context, task *Task) error
		}

		if dispatcher, ok := w.dispatcher.(Dispatcher); ok {
			successCount := 0
			failureCount := 0

			for _, result := range results {
				// Create a task for the api_pull_caveo worker
				// Convert the payload to the correct format
				payloadData := map[string]interface{}{
					"worker_type": "api_pull_caveo",
					"config": map[string]interface{}{
						"device": map[string]interface{}{
							"psm":      result.Payload.Config.Device.Psm,
							"estate":   result.Payload.Config.Device.Estate,
							"division": result.Payload.Config.Device.Division,
							"ident":    result.Payload.Config.Device.Ident,
							"nik":      result.Payload.Config.Device.Nik,
							"name":     result.Payload.Config.Device.Name,
							"type":     result.Payload.Config.Device.Type,
						},
						"timeStart": result.Payload.Config.TimeStart,
						"timeEnd":   result.Payload.Config.TimeEnd,
					},
				}

				// Marshal the payload
				payloadJSON, err := json.Marshal(payloadData)
				if err != nil {
					w.log.WithError(err).WithField("ident", result.Payload.Config.Device.Ident).Error("Failed to marshal payload for task")
					failureCount++
					continue
				}

				// Generate a unique task ID
				taskID := fmt.Sprintf("cpm-%d", uuid.New().String())

				// Create the task
				task := &Task{
					ID:        taskID,
					Type:      APICaveoWorker,
					Payload:   json.RawMessage(payloadJSON),
					CreatedAt: time.Now().Unix(),
				}

				// Dispatch the task
				if err := dispatcher.Dispatch(ctx, task); err != nil {
					w.log.WithError(err).WithField("task_id", taskID).WithField("ident", result.Payload.Config.Device.Ident).Error("Failed to dispatch task to api_pull_caveo worker")
					failureCount++
				} else {
					w.log.WithField("task_id", taskID).WithField("ident", result.Payload.Config.Device.Ident).Info("Successfully dispatched task to api_pull_caveo worker")
					successCount++
				}
			}

			w.log.WithField("success_count", successCount).WithField("failure_count", failureCount).WithField("total_count", len(results)).Info("Completed dispatching tasks to api_pull_caveo worker")
		} else {
			w.log.Error("Dispatcher does not implement the expected interface")
		}
	} else {
		w.log.Warn("Dispatcher is not set, cannot send tasks to api_pull_caveo worker")
	}

	// Log success if log writer is available
	if w.logWriter != nil {
		metadata := map[string]interface{}{
			"task_id":          task.ID,
			"message":          message,
			"data_length":      len(dataSlice),
			"tasks_dispatched": len(results),
		}
		key := fmt.Sprintf("logs:caveo-payload-maker:%s:success:%s", time.Now().Format("2006-01-02"), task.ID)
		if logErr := w.logWriter.LogSuccess(key, "caveo_payload_maker", metadata); logErr != nil {
			w.log.WithError(logErr).Error("Failed to write to Redis")
		}
	}

	return nil
}

// Type returns the type of worker
func (w *CaveoPayloadMakerWorker) Type() WorkerType {
	return PayloadMakerWorker
}

// SetLogWriter sets the log writer for the worker
func (w *CaveoPayloadMakerWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker
func (w *CaveoPayloadMakerWorker) SetDispatcher(dispatcher interface{}) {
	w.dispatcher = dispatcher
}

func convertToString(v interface{}) string {
	switch val := v.(type) {
	case string:
		return val
	case float64:
		return fmt.Sprintf("%.0f", val) // tanpa notasi ilmiah
	default:
		return convertToString(val)
	}
}
