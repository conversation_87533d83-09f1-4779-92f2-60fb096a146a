package worker

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// WorkerType defines the type of worker
type WorkerType string

const (
	// APIWorker is a worker that pulls data from an API
	APIWorker WorkerType = "api_pull"

	// DBWorker is a worker that clones data from one database to another
	DBWorker WorkerType = "db_cloning_worker"

	// APICaveoWorker is a worker that pulls data from the Caveo API
	APICaveoWorker WorkerType = "api_pull_caveo"

	// PayloadMakerWorker is a worker that makes Caveo payloads
	PayloadMakerWorker WorkerType = "caveo_payload_maker"

	// RescheduleCaveoWorker is a worker that reschedules Caveo jobs
	RescheduleCaveoWorker WorkerType = "reschedule_caveo"

	// CaveoToInfluxWorkerType is a worker that reads Caveo files from Minio and writes to InfluxDB
	CaveoToInfluxWorkerType WorkerType = "caveo_to_influx"

	// GeofDivisionWorkerType is a worker that performs geofencing between tracking points and division blocks
	GeofDivisionWorkerType WorkerType = "geof_division"

	// GeofEstateWorkerType is a worker that performs geofencing between tracking points and estate divisions
	GeofEstateWorkerType WorkerType = "geof_estate"

	// GeofBlockWorkerType is a worker that performs geofencing between tracking points and tree locations
	GeofBlockWorkerType WorkerType = "geof_block"
)

// Task represents a task to be processed by a worker
type Task struct {
	ID              string          `json:"id"`
	Type            WorkerType      `json:"type"`
	Payload         json.RawMessage `json:"payload"`
	CreatedAt       int64           `json:"created_at"`
	RescheduleCount int             `json:"reschedule_count"`
}

// Worker interface defines the methods that all workers must implement
type Worker interface {
	// Process processes a task
	Process(ctx context.Context, task *Task) error

	// Type returns the type of worker
	Type() WorkerType

	// SetLogWriter sets the log writer for the worker
	SetLogWriter(lw *logwriter.LogWriter)

	// SetDispatcher sets the dispatcher for the worker (optional, only needed for some workers)
	SetDispatcher(dispatcher interface{})
}

// NewWorker creates a new worker based on the worker type
func NewWorker(workerType WorkerType, log *logger.Logger, config interface{}) (Worker, error) {
	switch workerType {
	case APIWorker:
		return NewAPIPullWorker(log, config)
	case DBWorker:
		return NewDBCloningWorker(log, config)
	case APICaveoWorker:
		return NewCaveoAPIPullWorker(log, config)
	case PayloadMakerWorker:
		return NewCaveoPayloadMakerWorker(log, config)
	case RescheduleCaveoWorker:
		return NewRescheduleCaveoWorker(log, config)
	case CaveoToInfluxWorkerType:
		return NewCaveoToInfluxWorker(log, config)
	case GeofDivisionWorkerType:
		return NewGeofDivisionWorker(log, config)
	case GeofEstateWorkerType:
		return NewGeofEstateWorker(log, config)
	case GeofBlockWorkerType:
		return NewGeofBlockWorker(log, config)
	default:
		return nil, errors.New("unknown worker type")
	}
}
