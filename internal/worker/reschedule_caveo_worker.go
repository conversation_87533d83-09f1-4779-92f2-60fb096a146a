package worker

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/redis/go-redis/v9"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// RescheduleCaveoWorkerImpl is a worker that reschedules Caveo jobs
type RescheduleCaveoWorkerImpl struct {
	log               *logger.Logger
	redisClient       *redis.Client
	customRedisClient interface{} // Custom Redis client from the API server
	logWriter         *logwriter.LogWriter
	dispatcher        interface{} // Dispatcher for sending tasks to RabbitMQ
	minioClient       *minio.Client
}

// RescheduleCaveoConfig holds configuration for the reschedule Caveo worker
type RescheduleCaveoConfig struct {
	Redis struct {
		Host     string
		Port     string
		Password string
		DB       int
	}
	Minio struct {
		Endpoint  string
		AccessKey string
		SecretKey string
		UseSSL    bool
	}
}

// RescheduleJobData represents the structure of a re-schedule job from Redis
type RescheduleJobData struct {
	Config struct {
		Device struct {
			Division string `json:"division"`
			Estate   string `json:"estate"`
			Ident    string `json:"ident"`
			Name     string `json:"name"`
			Nik      string `json:"nik"`
			Psm      string `json:"psm"`
			Type     string `json:"type"`
		} `json:"device"`
		TimeEnd   string `json:"timeEnd"`
		TimeStart string `json:"timeStart"`
	} `json:"config"`
	WorkerType      string `json:"worker_type"`
	RescheduleCount int    `json:"reschedule_count"`
}

// NewRescheduleCaveoWorker creates a new reschedule Caveo worker
func NewRescheduleCaveoWorker(log *logger.Logger, config interface{}) (Worker, error) {
	worker := &RescheduleCaveoWorkerImpl{
		log: log,
	}

	// Initialize Redis client if config is provided
	if config != nil {
		if configMap, ok := config.(map[string]interface{}); ok {
			// Initialize Redis client
			if redisConfig, exists := configMap["redis"]; exists {
				if redisMap, ok := redisConfig.(map[string]interface{}); ok {
					host := "localhost"
					port := "6379"
					password := ""
					db := 0

					if h, ok := redisMap["host"].(string); ok {
						host = h
					}
					if p, ok := redisMap["port"].(string); ok {
						port = p
					}
					if pw, ok := redisMap["password"].(string); ok {
						password = pw
					}
					if d, ok := redisMap["db"].(float64); ok {
						db = int(d)
					}

					// Create Redis client
					client := redis.NewClient(&redis.Options{
						Addr:     fmt.Sprintf("%s:%s", host, port),
						Password: password,
						DB:       db,
					})

					// Test connection
					ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
					defer cancel()

					if err := client.Ping(ctx).Err(); err != nil {
						log.WithError(err).Warn("Failed to connect to Redis, worker will continue without Redis")
					} else {
						worker.redisClient = client
						log.Info("Successfully connected to Redis for reschedule Caveo worker")
					}
				}
			}

			// Initialize Minio client
			if minioConfig, exists := configMap["Minio"]; exists {
				if minioMap, ok := minioConfig.(map[string]interface{}); ok {
					endpoint := ""
					accessKey := ""
					secretKey := ""
					useSSL := false

					if e, ok := minioMap["Endpoint"].(string); ok {
						endpoint = e
					}
					if ak, ok := minioMap["AccessKey"].(string); ok {
						accessKey = ak
					}
					if sk, ok := minioMap["SecretKey"].(string); ok {
						secretKey = sk
					}
					if ssl, ok := minioMap["UseSSL"].(bool); ok {
						useSSL = ssl
					}

					if endpoint != "" && accessKey != "" && secretKey != "" {
						minioClient, err := minio.New(endpoint, &minio.Options{
							Creds:  credentials.NewStaticV4(accessKey, secretKey, ""),
							Secure: useSSL,
						})
						if err != nil {
							log.WithError(err).Warn("Failed to create Minio client, worker will continue without Minio")
						} else {
							worker.minioClient = minioClient
							log.Info("Successfully initialized Minio client for reschedule Caveo worker")
						}
					} else {
						log.Warn("Minio configuration incomplete, worker will continue without Minio")
					}
				}
			}
		}
	}

	return worker, nil
}

// Type returns the worker type
func (w *RescheduleCaveoWorkerImpl) Type() WorkerType {
	return RescheduleCaveoWorker
}

// SetLogWriter sets the log writer for the worker
func (w *RescheduleCaveoWorkerImpl) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker
func (w *RescheduleCaveoWorkerImpl) SetDispatcher(dispatcher interface{}) {
	w.dispatcher = dispatcher
}

// SetRedisClient sets the Redis client for the worker
func (w *RescheduleCaveoWorkerImpl) SetRedisClient(client interface{}) {
	w.customRedisClient = client
}

// Process processes a task
func (w *RescheduleCaveoWorkerImpl) Process(ctx context.Context, task *Task) error {
	w.log.WithField("task_id", task.ID).Info("Processing reschedule Caveo task")

	// Check if Redis client is available
	if w.customRedisClient == nil && w.redisClient == nil {
		return fmt.Errorf("redis client is not available")
	}

	// Get time threshold from environment variable (default to 4 hours)
	thresholdHours := 4
	if thresholdEnv := os.Getenv("RESCHEDULE_TIME_THRESHOLD_HOURS"); thresholdEnv != "" {
		if hours, err := strconv.Atoi(thresholdEnv); err == nil && hours > 0 {
			thresholdHours = hours
		}
	}

	// Calculate threshold time (current time - threshold hours)
	thresholdTime := time.Now().Add(-time.Duration(thresholdHours) * time.Hour)

	// Get all re-schedule jobs for api_pull_caveo
	pattern := "re-schedule-jobs:api_pull_caveo:*"
	var keys []string
	var err error

	// Use custom Redis client if available, otherwise use direct Redis client
	if w.customRedisClient != nil {
		if customClient, ok := w.customRedisClient.(interface {
			GetKeysByPattern(context.Context, string) ([]string, error)
		}); ok {
			keys, err = customClient.GetKeysByPattern(ctx, pattern)
		} else {
			return fmt.Errorf("custom redis client does not support GetKeysByPattern method")
		}
	} else {
		keys, err = w.redisClient.Keys(ctx, pattern).Result()
	}

	if err != nil {
		return fmt.Errorf("failed to get re-schedule jobs keys: %w", err)
	}

	if len(keys) == 0 {
		w.log.Info("No re-schedule jobs found for api_pull_caveo")
		fmt.Printf("[%s] RescheduleCaveo - No re-schedule jobs found for api_pull_caveo\n",
			time.Now().Format("2006-01-02 15:04:05"))
		return nil
	}

	w.log.WithField("job_count", len(keys)).Info("Found re-schedule jobs")
	fmt.Printf("[%s] RescheduleCaveo - Found %d re-schedule jobs for api_pull_caveo\n",
		time.Now().Format("2006-01-02 15:04:05"), len(keys))

	// Check if dispatcher is available
	if w.dispatcher == nil {
		return fmt.Errorf("dispatcher is not available")
	}

	// Process each re-schedule job
	processedJobs := 0
	sentJobs := 0
	deletedKeys := 0
	skippedJobs := 0
	for _, key := range keys {
		// Get job data from Redis
		var jobData string
		if w.customRedisClient != nil {
			// Try to get data using custom Redis client
			if customClient, ok := w.customRedisClient.(interface {
				Get(context.Context, string) (string, error)
			}); ok {
				jobData, err = customClient.Get(ctx, key)
				if err != nil {
					w.log.WithError(err).WithField("key", key).Warn("Failed to get job data from Redis")
					continue
				}
			} else {
				w.log.WithField("key", key).Warn("Custom redis client does not support Get method")
				continue
			}
		} else {
			// Try JSON.GET first (for new format), then fallback to regular GET
			result, err := w.redisClient.Do(ctx, "JSON.GET", key, ".").Result()
			if err == nil {
				// Successfully retrieved using JSON.GET
				if jsonStr, ok := result.(string); ok {
					jobData = jsonStr
				} else {
					w.log.WithField("key", key).WithField("result_type", fmt.Sprintf("%T", result)).Warn("JSON.GET result is not a string")
					continue
				}
			} else {
				// Fallback to regular GET for backward compatibility
				result, err := w.redisClient.Get(ctx, key).Result()
				if err != nil {
					w.log.WithError(err).WithField("key", key).Warn("Failed to get job data from Redis")
					continue
				}
				jobData = result
			}
		}

		// Parse job data
		var rescheduleJob RescheduleJobData
		if err := json.Unmarshal([]byte(jobData), &rescheduleJob); err != nil {
			w.log.WithError(err).WithField("key", key).Error("Failed to parse JSON data - corrupted data detected")

			// Handle corrupted data: delete from Redis and save to Minio
			if err := w.handleCorruptedData(ctx, key, jobData); err != nil {
				w.log.WithError(err).WithField("key", key).Error("Failed to handle corrupted data")
			}
			continue
		}

		// Parse timeStart and check if it meets the threshold condition
		timeStart, err := time.Parse("2006-01-02 15:04:05", rescheduleJob.Config.TimeStart)
		if err != nil {
			w.log.WithError(err).WithField("key", key).WithField("timeStart", rescheduleJob.Config.TimeStart).Warn("Failed to parse timeStart")
			continue
		}

		// Check if timeStart is at least threshold hours before current time
		// Only process jobs where config.timeStart <= (current_time - threshold_hours)
		if timeStart.After(thresholdTime) {
			w.log.WithFields(map[string]interface{}{
				"key":            key,
				"timeStart":      rescheduleJob.Config.TimeStart,
				"thresholdTime":  thresholdTime.Format("2006-01-02 15:04:05"),
				"thresholdHours": thresholdHours,
			}).Debug("Skipping job - timeStart is not old enough")
			skippedJobs++
			continue
		}

		// Parse timeEnd and add 2 hours
		timeEnd, err := time.Parse("2006-01-02 15:04:05", rescheduleJob.Config.TimeEnd)
		if err != nil {
			w.log.WithError(err).WithField("key", key).WithField("timeEnd", rescheduleJob.Config.TimeEnd).Warn("Failed to parse timeEnd")
			continue
		}

		// Add 2 hours
		newTimeEnd := timeEnd.Add(2 * time.Hour)
		rescheduleJob.Config.TimeEnd = newTimeEnd.Format("2006-01-02 15:04:05")

		// Increment RescheduleCount
		rescheduleJob.RescheduleCount++

		// Create a new task for api_pull_caveo
		newTask := &Task{
			ID:              fmt.Sprintf("reschedule-%d-%s", time.Now().Unix(), rescheduleJob.Config.Device.Ident),
			Type:            APICaveoWorker,
			CreatedAt:       time.Now().Unix(),
			RescheduleCount: rescheduleJob.RescheduleCount,
		}

		// Marshal the updated job data as payload
		updatedPayload, err := json.Marshal(rescheduleJob)
		if err != nil {
			w.log.WithError(err).WithField("key", key).Warn("Failed to marshal updated job data")
			continue
		}
		newTask.Payload = updatedPayload

		// Send task to dispatcher
		if dispatcher, ok := w.dispatcher.(interface {
			Dispatch(context.Context, *Task) error
		}); ok {
			if err := dispatcher.Dispatch(ctx, newTask); err != nil {
				w.log.WithError(err).WithField("key", key).Warn("Failed to dispatch rescheduled task")
				continue
			}
			sentJobs++

			// Delete the key from Redis after successful dispatch
			if w.customRedisClient != nil {
				if customClient, ok := w.customRedisClient.(interface {
					DeleteKey(context.Context, string) error
				}); ok {
					if err := customClient.DeleteKey(ctx, key); err != nil {
						w.log.WithError(err).WithField("key", key).Warn("Failed to delete re-schedule jobs key from Redis")
					} else {
						deletedKeys++
						w.log.WithField("key", key).Debug("Successfully deleted re-schedule jobs key from Redis")
					}
				} else {
					w.log.WithField("key", key).Warn("Custom redis client does not support DeleteKey method")
				}
			} else {
				if err := w.redisClient.Del(ctx, key).Err(); err != nil {
					w.log.WithError(err).WithField("key", key).Warn("Failed to delete re-schedule jobs key from Redis")
				} else {
					deletedKeys++
					w.log.WithField("key", key).Debug("Successfully deleted re-schedule jobs key from Redis")
				}
			}
		} else {
			w.log.WithField("key", key).Warn("Dispatcher does not support Dispatch method")
			continue
		}

		processedJobs++
	}

	w.log.WithFields(map[string]interface{}{
		"processed_jobs":  processedJobs,
		"sent_jobs":       sentJobs,
		"deleted_keys":    deletedKeys,
		"skipped_jobs":    skippedJobs,
		"threshold_hours": thresholdHours,
	}).Info("Completed processing re-schedule jobs")
	fmt.Printf("[%s] RescheduleCaveo - Completed processing %d re-schedule jobs, sent %d to queue, deleted %d keys, skipped %d jobs (threshold: %d hours)\n",
		time.Now().Format("2006-01-02 15:04:05"), processedJobs, sentJobs, deletedKeys, skippedJobs, thresholdHours)

	return nil
}

// extractIMEIFromKey extracts IMEI from the Redis key
// Key format: re-schedule-jobs:api_pull_caveo:YYYY-MM-DD:imei-hhmm
func (w *RescheduleCaveoWorkerImpl) extractIMEIFromKey(key string) string {
	parts := strings.Split(key, ":")
	if len(parts) >= 4 {
		// For new format with time suffix (imei-hhmm), extract just the IMEI part
		imeiWithTime := parts[3]
		// Check if it contains time suffix (format: imei-hhmm)
		if strings.Contains(imeiWithTime, "-") && len(imeiWithTime) > 4 {
			// Extract IMEI by removing the last 5 characters (-hhmm)
			lastDashIndex := strings.LastIndex(imeiWithTime, "-")
			if lastDashIndex > 0 && len(imeiWithTime)-lastDashIndex == 5 {
				return imeiWithTime[:lastDashIndex]
			}
		}
		// Return as-is for backward compatibility (old format without time)
		return imeiWithTime
	}
	return ""
}

// handleCorruptedData handles corrupted JSON data by deleting it from Redis and saving to Minio
func (w *RescheduleCaveoWorkerImpl) handleCorruptedData(ctx context.Context, key string, corruptedData string) error {
	// Extract task ID from the key for filename
	taskID := w.extractTaskIDFromKey(key)
	if taskID == "" {
		taskID = fmt.Sprintf("unknown-%d", time.Now().Unix())
	}

	// Get current date for folder structure
	dateStr := time.Now().Format("2006-01-02")

	// Delete corrupted data from Redis first
	if err := w.deleteRedisKey(ctx, key); err != nil {
		w.log.WithError(err).WithField("key", key).Warn("Failed to delete corrupted data from Redis")
	} else {
		w.log.WithField("key", key).Info("Successfully deleted corrupted data from Redis")
	}

	// Save corrupted data to Minio if client is available
	if w.minioClient != nil {
		if err := w.saveCorruptedDataToMinio(ctx, corruptedData, taskID, dateStr); err != nil {
			w.log.WithError(err).WithField("task_id", taskID).Error("Failed to save corrupted data to Minio")
			return fmt.Errorf("failed to save corrupted data to Minio: %w", err)
		} else {
			w.log.WithFields(map[string]interface{}{
				"task_id": taskID,
				"bucket":  "failed-jobs",
				"folder":  dateStr,
			}).Info("Successfully saved corrupted data to Minio")
		}
	} else {
		w.log.Warn("Minio client not available, skipping corrupted data storage")
	}

	return nil
}

// extractTaskIDFromKey extracts task ID from the Redis key
func (w *RescheduleCaveoWorkerImpl) extractTaskIDFromKey(key string) string {
	parts := strings.Split(key, ":")
	if len(parts) >= 4 {
		// For format: re-schedule-jobs:api_pull_caveo:YYYY-MM-DD:imei-hhmm
		// Extract the last part and use it as task ID
		return parts[len(parts)-1]
	}
	return ""
}

// deleteRedisKey deletes a key from Redis using the appropriate client
func (w *RescheduleCaveoWorkerImpl) deleteRedisKey(ctx context.Context, key string) error {
	if w.customRedisClient != nil {
		if customClient, ok := w.customRedisClient.(interface {
			DeleteKey(context.Context, string) error
		}); ok {
			return customClient.DeleteKey(ctx, key)
		} else {
			return fmt.Errorf("custom redis client does not support DeleteKey method")
		}
	} else if w.redisClient != nil {
		return w.redisClient.Del(ctx, key).Err()
	}
	return fmt.Errorf("no redis client available")
}

// saveCorruptedDataToMinio saves corrupted data to the "failed-jobs" bucket in Minio
func (w *RescheduleCaveoWorkerImpl) saveCorruptedDataToMinio(ctx context.Context, corruptedData string, taskID string, dateStr string) error {
	// Create object name: YYYY-MM-DD/task.ID
	objectName := filepath.Join(dateStr, taskID)

	// Create metadata for the corrupted data
	metadata := map[string]string{
		"x-amz-meta-worker-type": "reschedule_caveo",
		"x-amz-meta-task-id":     taskID,
		"x-amz-meta-error":       "corrupted_json_data",
		"x-amz-meta-timestamp":   time.Now().Format(time.RFC3339),
	}

	// Set content type and metadata
	putOptions := minio.PutObjectOptions{
		ContentType:  "application/json",
		UserMetadata: metadata,
	}

	// Check if "failed-jobs" bucket exists, create if not
	bucketName := "failed-jobs"
	exists, err := w.minioClient.BucketExists(ctx, bucketName)
	if err != nil {
		return fmt.Errorf("failed to check if bucket exists: %w", err)
	}
	if !exists {
		if err := w.minioClient.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{}); err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		w.log.WithField("bucket", bucketName).Info("Created failed-jobs bucket")
	}

	// Upload the corrupted data to Minio
	_, err = w.minioClient.PutObject(
		ctx,
		bucketName,
		objectName,
		bytes.NewReader([]byte(corruptedData)),
		int64(len(corruptedData)),
		putOptions,
	)
	if err != nil {
		return fmt.Errorf("failed to upload corrupted data to Minio: %w", err)
	}

	return nil
}
