package worker

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/user/workers/config"
	"github.com/user/workers/internal/token"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// WorkerConfigType is an alias for config.WorkerConfig to avoid naming conflicts
type WorkerConfigType = config.WorkerConfig

// CaveoAPIPullWorker is a worker that pulls data from the Caveo API
type CaveoAPIPullWorker struct {
	log          *logger.Logger
	client       *http.Client
	baseURL      string
	tokenMgr     *token.Manager
	minioClient  *minio.Client
	minioBucket  string
	logWriter    *logwriter.LogWriter
	workerConfig *WorkerConfigType
}

// CaveoAPIConfig holds configuration for the Caveo API pulling worker
type CaveoAPIConfig struct {
	BaseURL       string
	TokenUsername string
	TokenPassword string
	Timeout       int
	Bucket        string
	Minio         struct {
		Endpoint  string
		AccessKey string
		SecretKey string
		UseSSL    bool
	}
}

// CaveoAPIPayload is the payload for the Caveo API pulling worker
type CaveoAPIPayload struct {
	WorkerType string                `json:"worker_type"`
	Config     CaveoAPIPayloadConfig `json:"config"`
}

// DeviceConfig represents the device configuration with custom unmarshaling for ident field
type DeviceConfig struct {
	PSM      string `json:"psm"`
	Estate   string `json:"estate"`
	Division string `json:"division"`
	Ident    string `json:"ident"`
	NIK      string `json:"nik"`
	Name     string `json:"name"`
	Type     string `json:"type"`
}

// CaveoAPIPayloadConfig is the configuration for the Caveo API pulling worker
type CaveoAPIPayloadConfig struct {
	Device    DeviceConfig `json:"device"`
	TimeStart string       `json:"timeStart"`
	TimeEnd   string       `json:"timeEnd"`
}

// CaveoAPIRequest is the request body for the Caveo API
type CaveoAPIRequest struct {
	IMEI            string `json:"imei"`
	TimeStart       string `json:"time_start"`
	TimeEnd         string `json:"time_end"`
	LastDataRequest string `json:"lastData_requested"`
}

// CaveoAPIResponse is the response from the Caveo API
type CaveoAPIResponse struct {
	Status                bool    `json:"status"`
	IMEI                  string  `json:"imei"`
	Accuracy              float64 `json:"accuracy"`
	TotalTrue             int     `json:"totalTrue"`
	LastDataRequested     string  `json:"lastData_requested"`
	LastDataUpdated       string  `json:"lastData_updated"`
	TotalTracking         int     `json:"totalTracking"`
	LastDeviceDataUpdated string  `json:"lastDeviceData_updated"`
	Data                  []any   `json:"data"`
}

// NewCaveoAPIPullWorker creates a new Caveo API pulling worker
func NewCaveoAPIPullWorker(log *logger.Logger, config interface{}) (Worker, error) {
	var caveoConfig CaveoAPIConfig
	var workerConfig *WorkerConfigType

	// Try to extract from main config
	mainCfg, ok := config.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid config type for Caveo API pulling worker")
	}

	// Extract worker configuration
	if workerCfg, ok := mainCfg["WorkerConfig"]; ok {
		if cfg, ok := workerCfg.(WorkerConfigType); ok {
			workerConfig = &cfg
		}
	}

	// Extract Caveo API config
	if caveoAPI, ok := mainCfg["CaveoAPI"].(map[string]interface{}); ok {
		if baseURL, ok := caveoAPI["BaseURL"].(string); ok {
			caveoConfig.BaseURL = baseURL
		}
		if tokenUsername, ok := caveoAPI["TokenUsername"].(string); ok {
			caveoConfig.TokenUsername = tokenUsername
		}
		if tokenPassword, ok := caveoAPI["TokenPassword"].(string); ok {
			caveoConfig.TokenPassword = tokenPassword
		}
		if timeout, ok := caveoAPI["Timeout"].(int); ok {
			caveoConfig.Timeout = timeout
		}
		if bucket, ok := caveoAPI["Bucket"].(string); ok {
			caveoConfig.Bucket = bucket
		}
	}

	// Extract Minio config
	if minio, ok := mainCfg["Minio"].(map[string]interface{}); ok {
		if endpoint, ok := minio["Endpoint"].(string); ok {
			caveoConfig.Minio.Endpoint = endpoint
		}
		if accessKey, ok := minio["AccessKey"].(string); ok {
			caveoConfig.Minio.AccessKey = accessKey
		}
		if secretKey, ok := minio["SecretKey"].(string); ok {
			caveoConfig.Minio.SecretKey = secretKey
		}
		if useSSL, ok := minio["UseSSL"].(bool); ok {
			caveoConfig.Minio.UseSSL = useSSL
		}
	}

	// Try to get configuration from environment variables if not set
	if caveoConfig.BaseURL == "" {
		caveoConfig.BaseURL = os.Getenv("CAVEO_API_BASE_URL")
	}
	if caveoConfig.TokenUsername == "" {
		caveoConfig.TokenUsername = os.Getenv("CAVEO_API_TOKEN_USERNAME")
	}
	if caveoConfig.TokenPassword == "" {
		caveoConfig.TokenPassword = os.Getenv("CAVEO_API_TOKEN_PASSWORD")
	}
	if caveoConfig.Timeout == 0 {
		if timeoutStr := os.Getenv("CAVEO_API_TIMEOUT"); timeoutStr != "" {
			if timeout, err := time.ParseDuration(timeoutStr + "s"); err == nil {
				caveoConfig.Timeout = int(timeout.Seconds())
			}
		}
	}
	if caveoConfig.Minio.Endpoint == "" {
		caveoConfig.Minio.Endpoint = os.Getenv("MINIO_ENDPOINT")
	}
	if caveoConfig.Minio.AccessKey == "" {
		caveoConfig.Minio.AccessKey = os.Getenv("MINIO_ACCESS_KEY")
	}
	if caveoConfig.Minio.SecretKey == "" {
		caveoConfig.Minio.SecretKey = os.Getenv("MINIO_SECRET_KEY")
	}
	// Bucket is now configured in workers.json
	if !caveoConfig.Minio.UseSSL {
		if useSSLStr := os.Getenv("MINIO_USE_SSL"); useSSLStr == "true" {
			caveoConfig.Minio.UseSSL = true
		}
	}

	// Validate required configuration
	if caveoConfig.BaseURL == "" {
		return nil, fmt.Errorf("caveo API base URL is required")
	}
	if caveoConfig.TokenUsername == "" {
		return nil, fmt.Errorf("caveo API token username is required")
	}
	if caveoConfig.TokenPassword == "" {
		return nil, fmt.Errorf("caveo API token password is required")
	}

	// Minio is optional for testing, but required in production
	var minioRequired bool
	if os.Getenv("ENVIRONMENT") != "test" {
		minioRequired = true
	}

	if minioRequired {
		if caveoConfig.Minio.Endpoint == "" {
			return nil, fmt.Errorf("minio endpoint is required")
		}
	}

	// Initialize Minio client if endpoint is provided
	var minioClient *minio.Client
	if caveoConfig.Minio.Endpoint != "" {
		var err error
		minioClient, err = minio.New(caveoConfig.Minio.Endpoint, &minio.Options{
			Creds:  credentials.NewStaticV4(caveoConfig.Minio.AccessKey, caveoConfig.Minio.SecretKey, ""),
			Secure: caveoConfig.Minio.UseSSL,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to create Minio client: %w", err)
		}
	}

	// Set default timeout if not provided
	timeout := time.Duration(caveoConfig.Timeout) * time.Second
	if timeout == 0 {
		timeout = 120 * time.Second // Increased default timeout to 2 minutes
	}

	// Set timeout without logging

	// Log a warning if the bucket is not set
	if caveoConfig.Bucket == "" {
		log.Warn("Caveo API bucket is not set, using bucket from workers.json configuration")
	}

	// Create token manager
	tokenEndpoint := fmt.Sprintf("%s/token", caveoConfig.BaseURL)
	tokenMgr := token.NewManager(
		caveoConfig.TokenUsername,
		caveoConfig.TokenPassword,
		tokenEndpoint,
		timeout,
	)

	worker := &CaveoAPIPullWorker{
		log:          log,
		client:       &http.Client{Timeout: timeout},
		baseURL:      caveoConfig.BaseURL,
		tokenMgr:     tokenMgr,
		minioClient:  minioClient,
		minioBucket:  caveoConfig.Bucket,
		logWriter:    nil, // Will be set later via SetLogWriter
		workerConfig: workerConfig,
	}

	return worker, nil
}

// UnmarshalJSON implements custom unmarshaling for DeviceConfig to handle numeric ident values
func (d *DeviceConfig) UnmarshalJSON(data []byte) error {
	// Create a shadow type to avoid infinite recursion
	type DeviceConfigAlias DeviceConfig

	// First try to unmarshal directly
	aliasValue := &struct {
		*DeviceConfigAlias
	}{
		DeviceConfigAlias: (*DeviceConfigAlias)(d),
	}

	if err := json.Unmarshal(data, aliasValue); err != nil {
		// If that fails, try with a custom structure that accepts numeric ident
		var rawDevice struct {
			PSM      string      `json:"psm"`
			Estate   string      `json:"estate"`
			Division string      `json:"division"`
			Ident    interface{} `json:"ident"` // Accept any type
			NIK      string      `json:"nik"`
			Name     string      `json:"name"`
			Type     string      `json:"type"`
		}

		if err := json.Unmarshal(data, &rawDevice); err != nil {
			return err
		}

		// Copy the values
		d.PSM = rawDevice.PSM
		d.Estate = rawDevice.Estate
		d.Division = rawDevice.Division
		d.NIK = rawDevice.NIK
		d.Name = rawDevice.Name
		d.Type = rawDevice.Type

		// Convert ident to string regardless of its original type
		switch v := rawDevice.Ident.(type) {
		case string:
			d.Ident = v
		case float64:
			d.Ident = fmt.Sprintf("%.0f", v) // Convert float to string without decimal
		case int:
			d.Ident = fmt.Sprintf("%d", v)
		case int64:
			d.Ident = fmt.Sprintf("%d", v)
		case json.Number:
			d.Ident = string(v)
		default:
			if rawDevice.Ident != nil {
				d.Ident = fmt.Sprintf("%v", rawDevice.Ident)
			}
		}
	}

	return nil
}

// Process processes a task
func (w *CaveoAPIPullWorker) Process(ctx context.Context, task *Task) error {
	// Log the task information including reschedule count
	w.log.WithFields(map[string]interface{}{
		"task_id":          task.ID,
		"task_type":        task.Type,
		"reschedule_count": task.RescheduleCount,
		"created_at":       task.CreatedAt,
	}).Info("Processing api_pull_caveo task")

	// Try to unmarshal the payload as a CaveoAPIPayload
	var payload CaveoAPIPayload

	// Try to unmarshal as a JobRequest from the API server
	var jobRequest struct {
		WorkerType string `json:"worker_type"`
		Config     struct {
			Device struct {
				Division string      `json:"division"`
				Estate   string      `json:"estate"`
				Ident    interface{} `json:"ident"` // Accept any type for ident
				Name     string      `json:"name"`
				Nik      string      `json:"nik"`
				Psm      string      `json:"psm"`
				Type     string      `json:"type"`
			} `json:"device"`
			TimeEnd   string `json:"timeEnd"`
			TimeStart string `json:"timeStart"`
		} `json:"config"`
	}

	if err := json.Unmarshal(task.Payload, &jobRequest); err != nil {
		w.log.WithError(err).Error("Failed to unmarshal payload as JobRequest")
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	if err := json.Unmarshal(task.Payload, &payload); err != nil {
		w.log.WithError(err).Error("Failed to unmarshal payload as CaveoAPIPayload")

		// Convert the JobRequest to a CaveoAPIPayload
		w.log.Debug("Converting JobRequest to CaveoAPIPayload")

		// Create a new CaveoAPIPayload
		payload = CaveoAPIPayload{
			WorkerType: jobRequest.WorkerType,
			Config: CaveoAPIPayloadConfig{
				TimeStart: jobRequest.Config.TimeStart,
				TimeEnd:   jobRequest.Config.TimeEnd,
			},
		}

		// Set the device information
		payload.Config.TimeEnd = jobRequest.Config.TimeEnd
		payload.Config.TimeStart = jobRequest.Config.TimeStart
		payload.Config.Device.Division = jobRequest.Config.Device.Division
		payload.Config.Device.Estate = jobRequest.Config.Device.Estate
		payload.Config.Device.Ident = toString(jobRequest.Config.Device.Ident) // Convert to string using our helper function
		payload.Config.Device.Name = jobRequest.Config.Device.Name
		payload.Config.Device.NIK = jobRequest.Config.Device.Nik
		payload.Config.Device.PSM = jobRequest.Config.Device.Psm
		payload.Config.Device.Type = jobRequest.Config.Device.Type
	}

	// Parse time to get time start - end for the log (moved here to ensure it's always executed)
	start, err := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
	if err != nil {
		w.log.WithError(err).Warn("Failed to parse timeStart for log metadata")
		// Use current time as fallback
		start = time.Now()
	}
	end, err := time.Parse("2006-01-02 15:04:05", payload.Config.TimeEnd)
	if err != nil {
		w.log.WithError(err).Warn("Failed to parse timeEnd for log metadata")
		// Use current time as fallback
		end = time.Now()
	}

	// Format the times for the log (moved here to ensure it's always executed)
	endDOnly := end.Format("2006-01-02")
	startTOnly := start.Format("15:04:05")
	endTOnly := end.Format("15:04:05")
	// Hapus ":" dari startTOnly dan endTOnly
	startClean := strings.ReplaceAll(startTOnly, ":", "")
	endClean := strings.ReplaceAll(endTOnly, ":", "")
	timeRange := fmt.Sprintf("%s - %s", startClean, endClean)
	logID := fmt.Sprintf("%s-%s", payload.Config.Device.Ident, timeRange)

	// Prepare API request
	apiRequest := CaveoAPIRequest{
		IMEI:            payload.Config.Device.Ident,
		TimeStart:       payload.Config.TimeStart,
		TimeEnd:         payload.Config.TimeEnd,
		LastDataRequest: payload.Config.TimeStart,
	}

	// Convert to JSON
	requestBody, err := json.Marshal(apiRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	// Create request
	apiURL := fmt.Sprintf("%s/get-tracking", w.baseURL)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		apiURL,
		bytes.NewBuffer(requestBody),
	)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Add headers
	req.Header.Set("Content-Type", "application/json")

	// Get token from token manager
	token, err := w.tokenMgr.GetToken()
	if err != nil {
		return fmt.Errorf("failed to get token: %w", err)
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	// Send request with detailed error logging
	w.log.WithFields(map[string]interface{}{
		"url":     apiURL,
		"method":  http.MethodPost,
		"timeout": w.client.Timeout.String(),
	}).Debug("Sending request to Caveo API")

	resp, err := w.client.Do(req)
	if err != nil {
		// Check if it's a timeout error
		isTimeout := os.IsTimeout(err) ||
			strings.Contains(err.Error(), "timeout") ||
			strings.Contains(err.Error(), "deadline exceeded") ||
			strings.Contains(err.Error(), "Client.Timeout")

		if isTimeout {
			w.log.WithError(err).Error("Request to Caveo API timed out")

			// Store failed job and log failure to logwriter if available
			if w.logWriter != nil {
				// Parse timeStart to get the date for the failed job key
				start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
				var dateStr string
				if parseErr != nil {
					w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
					dateStr = time.Now().Format("2006-01-02")
				} else {
					dateStr = start.Format("2006-01-02")
				}

				// Store failed job in Redis
				failedJobMetadata := map[string]interface{}{
					"error":     "API request timed out: " + err.Error(),
					"task_id":   task.ID,
					"imei":      payload.Config.Device.Ident,
					"PSM":       payload.Config.Device.PSM,
					"Estate":    payload.Config.Device.Estate,
					"Division":  payload.Config.Device.Division,
					"NIK":       payload.Config.Device.NIK,
					"api_url":   apiURL,
					"timeout":   w.client.Timeout.String(),
					"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
					"reason":    "Timeout error",
				}
				if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
					w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
				}

				// Remove job from re-schedule queue if it exists
				if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
					w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
				} else {
					w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
				}

				// Log failure to logwriter
				metadata := map[string]interface{}{
					"error":     "API request timed out: " + err.Error(),
					"task_id":   task.ID,
					"imei":      payload.Config.Device.Ident,
					"PSM":       payload.Config.Device.PSM,
					"Estate":    payload.Config.Device.Estate,
					"Division":  payload.Config.Device.Division,
					"NIK":       payload.Config.Device.NIK,
					"api_url":   apiURL,
					"timeout":   w.client.Timeout.String(),
					"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
				}
				key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
				if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write to Redis")
				}
			}

			// Return a timeout error that will be recognized by the worker pool
			return fmt.Errorf("TIMEOUT_ERROR: request to Caveo API timed out (timeout: %s): %w", w.client.Timeout.String(), err)
		}

		// Other network errors - store as failed job
		w.log.WithError(err).Error("Failed to send request to Caveo API")

		// Store failed job and log failure to logwriter if available
		if w.logWriter != nil {
			// Parse timeStart to get the date for the failed job key
			start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
			var dateStr string
			if parseErr != nil {
				w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
				dateStr = time.Now().Format("2006-01-02")
			} else {
				dateStr = start.Format("2006-01-02")
			}

			// Store failed job in Redis
			failedJobMetadata := map[string]interface{}{
				"error":     "Failed to send request to Caveo API: " + err.Error(),
				"task_id":   task.ID,
				"api_url":   apiURL,
				"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
				"reason":    "Network error",
			}
			if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
				w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
			}

			// Remove job from re-schedule queue if it exists
			if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
				w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
			} else {
				w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
			}

			// Log failure to logwriter
			metadata := map[string]interface{}{
				"error":     "Failed to send request to Caveo API: " + err.Error(),
				"task_id":   task.ID,
				"imei":      payload.Config.Device.Ident,
				"PSM":       payload.Config.Device.PSM,
				"Estate":    payload.Config.Device.Estate,
				"Division":  payload.Config.Device.Division,
				"NIK":       payload.Config.Device.NIK,
				"api_url":   apiURL,
				"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
			}

			key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
			if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}

		return fmt.Errorf("failed to send request to Caveo API: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode >= 400 {
		// If unauthorized, try to refresh token and retry once
		if resp.StatusCode == http.StatusUnauthorized {
			w.log.Warn("Token expired, refreshing and retrying")

			// Refresh token
			token, err = w.tokenMgr.RefreshToken()
			if err != nil {
				// Store failed job for token refresh failure
				if w.logWriter != nil {
					// Parse timeStart to get the date for the failed job key
					start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
					var dateStr string
					if parseErr != nil {
						w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
						dateStr = time.Now().Format("2006-01-02")
					} else {
						dateStr = start.Format("2006-01-02")
					}

					// Store failed job in Redis
					failedJobMetadata := map[string]interface{}{
						"error":     "Failed to refresh token: " + err.Error(),
						"task_id":   task.ID,
						"api_url":   apiURL,
						"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
						"reason":    "Token refresh failure",
					}
					if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
						w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
					}

					// Remove job from re-schedule queue if it exists
					if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
						w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
					} else {
						w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
					}

					// Log failure to logwriter
					metadata := map[string]interface{}{
						"error":     "Failed to refresh token: " + err.Error(),
						"task_id":   task.ID,
						"api_url":   apiURL,
						"imei":      payload.Config.Device.Ident,
						"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
					}
					key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
					if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
						w.log.WithError(logErr).Error("Failed to write to Redis")
					}
				}
				return fmt.Errorf("failed to refresh token: %w", err)
			}

			// Create new request
			req, err = http.NewRequestWithContext(
				ctx,
				http.MethodPost,
				apiURL,
				bytes.NewBuffer(requestBody),
			)
			if err != nil {
				// Store failed job for request creation failure after token refresh
				if w.logWriter != nil {
					// Parse timeStart to get the date for the failed job key
					start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
					var dateStr string
					if parseErr != nil {
						w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
						dateStr = time.Now().Format("2006-01-02")
					} else {
						dateStr = start.Format("2006-01-02")
					}

					// Store failed job in Redis
					failedJobMetadata := map[string]interface{}{
						"error":     "Failed to create request after token refresh: " + err.Error(),
						"task_id":   task.ID,
						"api_url":   apiURL,
						"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
						"reason":    "Request creation failure after token refresh",
					}
					if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
						w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
					}

					// Remove job from re-schedule queue if it exists
					if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
						w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
					} else {
						w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
					}

					// Log failure to logwriter
					metadata := map[string]interface{}{
						"error":     "Failed to create request after token refresh: " + err.Error(),
						"task_id":   task.ID,
						"imei":      payload.Config.Device.Ident,
						"PSM":       payload.Config.Device.PSM,
						"Estate":    payload.Config.Device.Estate,
						"Division":  payload.Config.Device.Division,
						"NIK":       payload.Config.Device.NIK,
						"api_url":   apiURL,
						"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
					}
					key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
					if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
						w.log.WithError(logErr).Error("Failed to write to Redis")
					}
				}
				return fmt.Errorf("failed to create request after token refresh: %w", err)
			}

			// Add headers
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

			// Retry request
			resp, err = w.client.Do(req)
			if err != nil {
				// Store failed job for request send failure after token refresh
				if w.logWriter != nil {
					// Parse timeStart to get the date for the failed job key
					start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
					var dateStr string
					if parseErr != nil {
						w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
						dateStr = time.Now().Format("2006-01-02")
					} else {
						dateStr = start.Format("2006-01-02")
					}

					// Store failed job in Redis
					failedJobMetadata := map[string]interface{}{
						"error":     "Failed to send request after token refresh: " + err.Error(),
						"task_id":   task.ID,
						"api_url":   apiURL,
						"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
						"reason":    "Request send failure after token refresh",
					}
					if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
						w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
					}

					// Remove job from re-schedule queue if it exists
					if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
						w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
					} else {
						w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
					}

					// Log failure to logwriter
					metadata := map[string]interface{}{
						"error":     "Failed to send request after token refresh: " + err.Error(),
						"task_id":   task.ID,
						"imei":      payload.Config.Device.Ident,
						"PSM":       payload.Config.Device.PSM,
						"Estate":    payload.Config.Device.Estate,
						"Division":  payload.Config.Device.Division,
						"NIK":       payload.Config.Device.NIK,
						"api_url":   apiURL,
						"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
					}
					key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
					if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
						w.log.WithError(logErr).Error("Failed to write to Redis")
					}
				}
				return fmt.Errorf("failed to send request after token refresh: %w", err)
			}
			defer resp.Body.Close()

			// Check response status again
			if resp.StatusCode >= 400 {
				// Store failed job for status code error after token refresh
				if w.logWriter != nil {
					// Parse timeStart to get the date for the failed job key
					start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
					var dateStr string
					if parseErr != nil {
						w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
						dateStr = time.Now().Format("2006-01-02")
					} else {
						dateStr = start.Format("2006-01-02")
					}

					// Store failed job in Redis
					failedJobMetadata := map[string]interface{}{
						"error":       fmt.Sprintf("API request failed with status code: %d after token refresh", resp.StatusCode),
						"task_id":     task.ID,
						"api_url":     apiURL,
						"status_code": resp.StatusCode,
						"time_span":   payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
						"reason":      "HTTP status error after token refresh",
					}
					if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
						w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
					}

					// Remove job from re-schedule queue if it exists
					if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
						w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
					} else {
						w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
					}

					// Log failure to logwriter
					metadata := map[string]interface{}{
						"error":       fmt.Sprintf("API request failed with status code: %d after token refresh", resp.StatusCode),
						"task_id":     task.ID,
						"imei":        payload.Config.Device.Ident,
						"PSM":         payload.Config.Device.PSM,
						"Estate":      payload.Config.Device.Estate,
						"Division":    payload.Config.Device.Division,
						"NIK":         payload.Config.Device.NIK,
						"api_url":     apiURL,
						"status_code": resp.StatusCode,
						"time_span":   payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
					}
					key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
					if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
						w.log.WithError(logErr).Error("Failed to write to Redis")
					}
				}
				return fmt.Errorf("API request failed with status code: %d after token refresh", resp.StatusCode)
			}
		} else {
			// Store failed job for status code error
			if w.logWriter != nil {
				// Parse timeStart to get the date for the failed job key
				start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
				var dateStr string
				if parseErr != nil {
					w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
					dateStr = time.Now().Format("2006-01-02")
				} else {
					dateStr = start.Format("2006-01-02")
				}

				// Store failed job in Redis
				failedJobMetadata := map[string]interface{}{
					"error":       fmt.Sprintf("API request failed with status code: %d", resp.StatusCode),
					"task_id":     task.ID,
					"api_url":     apiURL,
					"status_code": resp.StatusCode,
					"time_span":   payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
					"reason":      "HTTP status error",
				}
				if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
					w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
				}

				// Remove job from re-schedule queue if it exists
				if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
					w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
				} else {
					w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
				}

				// Log failure to logwriter
				metadata := map[string]interface{}{
					"error":       fmt.Sprintf("API request failed with status code: %d", resp.StatusCode),
					"task_id":     task.ID,
					"imei":        payload.Config.Device.Ident,
					"PSM":         payload.Config.Device.PSM,
					"Estate":      payload.Config.Device.Estate,
					"Division":    payload.Config.Device.Division,
					"NIK":         payload.Config.Device.NIK,
					"api_url":     apiURL,
					"status_code": resp.StatusCode,
					"time_span":   payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
				}
				key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
				if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write to Redis")
				}
			}
			return fmt.Errorf("API request failed with status code: %d", resp.StatusCode)
		}
	}

	// Parse response with improved error handling
	var apiResponse CaveoAPIResponse

	// Read the response body into a buffer so we can retry if parsing fails
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		// Check if it's a timeout error
		isTimeout := os.IsTimeout(err) ||
			strings.Contains(err.Error(), "timeout") ||
			strings.Contains(err.Error(), "deadline exceeded") ||
			strings.Contains(err.Error(), "Client.Timeout")

		w.log.WithError(err).Error("Failed to read response body")

		if isTimeout {
			// Store failed job and log failure to logwriter if available
			if w.logWriter != nil {
				// Parse timeStart to get the date for the failed job key
				start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
				var dateStr string
				if parseErr != nil {
					w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
					dateStr = time.Now().Format("2006-01-02")
				} else {
					dateStr = start.Format("2006-01-02")
				}

				// Store failed job in Redis
				failedJobMetadata := map[string]interface{}{
					"error":     "API response read timed out: " + err.Error(),
					"task_id":   task.ID,
					"api_url":   apiURL,
					"timeout":   w.client.Timeout.String(),
					"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
					"reason":    "Response read timeout",
				}
				if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
					w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
				}

				// Remove job from re-schedule queue if it exists
				if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
					w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
				} else {
					w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
				}

				// Log failure to logwriter
				metadata := map[string]interface{}{
					"error":     "API response read timed out: " + err.Error(),
					"task_id":   task.ID,
					"imei":      payload.Config.Device.Ident,
					"PSM":       payload.Config.Device.PSM,
					"Estate":    payload.Config.Device.Estate,
					"Division":  payload.Config.Device.Division,
					"NIK":       payload.Config.Device.NIK,
					"api_url":   apiURL,
					"timeout":   w.client.Timeout.String(),
					"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
				}
				key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
				if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
					w.log.WithError(logErr).Error("Failed to write to Redis")
				}
			}

			// Return a timeout error that will be recognized by the worker pool
			return fmt.Errorf("TIMEOUT_ERROR: failed to read response body (timeout: %s): %w", w.client.Timeout.String(), err)
		}

		// Store failed job for non-timeout response body read error
		if w.logWriter != nil {
			// Parse timeStart to get the date for the failed job key
			start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
			var dateStr string
			if parseErr != nil {
				w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
				dateStr = time.Now().Format("2006-01-02")
			} else {
				dateStr = start.Format("2006-01-02")
			}

			// Store failed job in Redis
			failedJobMetadata := map[string]interface{}{
				"error":     "Failed to read response body: " + err.Error(),
				"task_id":   task.ID,
				"api_url":   apiURL,
				"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
				"reason":    "Response read error",
			}
			if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
				w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
			}

			// Remove job from re-schedule queue if it exists
			if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
				w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
			} else {
				w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
			}

			// Log failure to logwriter
			metadata := map[string]interface{}{
				"error":     "Failed to read response body: " + err.Error(),
				"task_id":   task.ID,
				"api_url":   apiURL,
				"imei":      payload.Config.Device.Ident,
				"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
			}
			key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
			if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}

		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Try to decode the response
	if err := json.Unmarshal(bodyBytes, &apiResponse); err != nil {
		w.log.WithError(err).Error("Failed to decode response")

		// Log the first part of the response for debugging
		responsePreview := string(bodyBytes)
		if len(responsePreview) > 200 {
			responsePreview = responsePreview[:200] + "..."
		}
		w.log.WithField("response_preview", responsePreview).Error("Response body preview")

		// Store failed HTTP response in Minio if client is available
		if w.minioClient != nil {
			// Parse timeStart to get the date for the folder
			start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
			var dateStr string
			if parseErr != nil {
				w.log.WithError(parseErr).Warn("Failed to parse timeStart for Minio folder, using current date")
				dateStr = time.Now().Format("2006-01-02")
			} else {
				dateStr = start.Format("2006-01-02")
			}

			// Store the failed response in Minio
			if minioErr := w.storeFailedResponseInMinio(ctx, bodyBytes, task.ID, dateStr, &payload); minioErr != nil {
				w.log.WithError(minioErr).Error("Failed to store failed response in Minio")
			} else {
				w.log.WithFields(map[string]interface{}{
					"task_id": task.ID,
					"bucket":  "failed-jobs",
					"folder":  dateStr,
				}).Info("Successfully stored failed HTTP response in Minio")
			}
		} else {
			w.log.Warn("Minio client not available, skipping failed response storage")
		}

		// Store failed job and log failure to logwriter if available
		if w.logWriter != nil {
			// Parse timeStart to get the date for the failed job key
			start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
			var dateStr string
			if parseErr != nil {
				w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
				dateStr = time.Now().Format("2006-01-02")
			} else {
				dateStr = start.Format("2006-01-02")
			}

			// Store failed job in Redis
			failedJobMetadata := map[string]interface{}{
				"error":            "Failed to decode API response: " + err.Error(),
				"task_id":          task.ID,
				"api_url":          apiURL,
				"time_span":        payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
				"response_preview": responsePreview,
				"reason":           "JSON decode error",
			}
			if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
				w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
			}

			// Remove job from re-schedule queue if it exists
			if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
				w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
			} else {
				w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
			}

			// Log failure to logwriter
			metadata := map[string]interface{}{
				"error":     "Failed to decode API response: " + err.Error(),
				"task_id":   task.ID,
				"api_url":   apiURL,
				"imei":      payload.Config.Device.Ident,
				"time_span": payload.Config.TimeStart + " to " + payload.Config.TimeEnd,
			}
			key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
			if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
				w.log.WithError(logErr).Error("Failed to write to Redis")
			}
		}

		return fmt.Errorf("failed to decode response: %w", err)
	}

	// Get accuracy threshold from worker configuration, default to 80.0 if not set
	accuracyThreshold := 80.0
	if w.workerConfig != nil && w.workerConfig.AccuracyThreshold > 0 {
		accuracyThreshold = w.workerConfig.AccuracyThreshold
	}

	// Check accuracy for validation
	if apiResponse.Accuracy >= accuracyThreshold {
		// Only try to store in Minio if the client is available
		if w.minioClient != nil {
			// Check if bucket is set
			if w.minioBucket == "" {
				w.log.Warn("Caveo API bucket is not set, skipping storage")
			} else {
				// Store response in Minio
				if err := w.storeInMinio(ctx, apiResponse, payload.Config.Device.Ident, payload.Config.TimeStart, payload.Config.TimeEnd); err != nil {
					// Store failed job and log failure to logwriter
					if w.logWriter != nil {
						// Parse timeStart to get the date for the failed job key
						start, parseErr := time.Parse("2006-01-02 15:04:05", payload.Config.TimeStart)
						var dateStr string
						if parseErr != nil {
							w.log.WithError(parseErr).Warn("Failed to parse timeStart for failed job, using current date")
							dateStr = time.Now().Format("2006-01-02")
						} else {
							dateStr = start.Format("2006-01-02")
						}

						// Store failed job in Redis
						failedJobMetadata := map[string]interface{}{
							"error":      "Failed to store response in Minio: " + err.Error(),
							"task_id":    task.ID,
							"time_start": payload.Config.TimeStart,
							"time_end":   payload.Config.TimeEnd,
							"accuracy":   apiResponse.Accuracy,
							"reason":     "Minio storage error",
						}
						if storeErr := w.logWriter.StoreFailedJob(dateStr, payload.Config.Device.Ident, task.Payload, failedJobMetadata); storeErr != nil {
							w.log.WithError(storeErr).Error("Failed to store failed job in Redis")
						}

						// Remove job from re-schedule queue if it exists
						if delErr := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); delErr != nil {
							w.log.WithError(delErr).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
						} else {
							w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed failed job from reschedule queue")
						}

						// Log failure to logwriter
						metadata := map[string]interface{}{
							"error":      err.Error(),
							"task_id":    task.ID,
							"time_start": payload.Config.TimeStart,
							"time_end":   payload.Config.TimeEnd,
							"accuracy":   apiResponse.Accuracy,
						}
						key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
						if logErr := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); logErr != nil {
							w.log.WithError(logErr).Error("Failed to write to Redis")
						}
					}
					return fmt.Errorf("failed to store response in Minio: %w", err)
				}
			}
		} else {
			w.log.Warn("Minio client not available, skipping storage")
		}

		// Log success to logwriter
		if w.logWriter != nil {
			metadata := map[string]interface{}{
				"accuracy":       apiResponse.Accuracy,
				"imei":           apiResponse.IMEI,
				"PSM":            payload.Config.Device.PSM,
				"Estate":         payload.Config.Device.Estate,
				"Division":       payload.Config.Device.Division,
				"NIK":            payload.Config.Device.NIK,
				"total_tracking": apiResponse.TotalTracking,
				"task_id":        task.ID,
				"date":           endDOnly,
				"timeRange":      timeRange,
			}

			key := fmt.Sprintf("logs:api_pull_caveo:%s:success:%s", endDOnly, logID)
			if err := w.logWriter.LogSuccess(key, string(APICaveoWorker), metadata); err != nil {
				w.log.WithError(err).Error("Failed to write to Redis")
			}
		}
	} else {
		// Get max reschedule count from worker configuration, default to 6 if not set
		maxRescheduleCount := 6
		if w.workerConfig != nil && w.workerConfig.MaxRescheduleCount > 0 {
			maxRescheduleCount = w.workerConfig.MaxRescheduleCount
		}

		// Check if we've reached the maximum reschedule count
		if task.RescheduleCount >= maxRescheduleCount {
			// Store as not-comply job instead of rescheduling
			if w.logWriter != nil {
				// Create not-comply job key: not-comply-job:api_pull_caveo:YYYY-MM-DD:task.id
				notComplyKey := fmt.Sprintf("not-comply-job:api_pull_caveo:%s:%s", endDOnly, task.ID)

				// Create not-comply job data
				notComplyData := map[string]interface{}{
					"original_payload": string(task.Payload),
					"accuracy":         apiResponse.Accuracy,
					"total_true":       apiResponse.TotalTrue,
					"total_tracking":   apiResponse.TotalTracking,
					"task_id":          task.ID,
					"reschedule_count": task.RescheduleCount,
					"max_reschedule":   maxRescheduleCount,
					"reason":           "Maximum reschedule count reached",
					"timestamp":        time.Now().UTC().Format(time.RFC3339),
				}

				// Store not-comply job in Redis
				if err := w.logWriter.StoreInRedis(notComplyKey, notComplyData); err != nil {
					w.log.WithError(err).Error("Failed to store not-comply job in Redis")
				} else {
					w.log.WithFields(map[string]interface{}{
						"task_id":          task.ID,
						"reschedule_count": task.RescheduleCount,
						"max_reschedule":   maxRescheduleCount,
						"accuracy":         apiResponse.Accuracy,
						"imei":             apiResponse.IMEI,
					}).Warn("Job reached maximum reschedule count, stored as not-comply job")
				}

				// Delete the job from re-schedule queue if it exists
				if err := w.logWriter.DeleteRescheduleJob(string(APICaveoWorker), payload.Config.Device.Ident, task.Payload); err != nil {
					w.log.WithError(err).WithField("imei", payload.Config.Device.Ident).Warn("Failed to delete reschedule job from Redis (job may not exist in reschedule queue)")
				} else {
					w.log.WithField("imei", payload.Config.Device.Ident).Info("Successfully removed job from reschedule queue")
				}

				// Log not-comply to logwriter
				metadata := map[string]interface{}{
					"accuracy":         apiResponse.Accuracy,
					"imei":             apiResponse.IMEI,
					"PSM":              payload.Config.Device.PSM,
					"Estate":           payload.Config.Device.Estate,
					"Division":         payload.Config.Device.Division,
					"NIK":              payload.Config.Device.NIK,
					"total_true":       apiResponse.TotalTrue,
					"total_tracking":   apiResponse.TotalTracking,
					"task_id":          task.ID,
					"date":             endDOnly,
					"timeRange":        timeRange,
					"reschedule_count": task.RescheduleCount,
					"max_reschedule":   maxRescheduleCount,
					"reason":           "Maximum reschedule count reached",
				}

				key := fmt.Sprintf("logs:api_pull_caveo:%s:failed:%s", endDOnly, logID)
				if err := w.logWriter.LogFailure(key, string(APICaveoWorker), metadata); err != nil {
					w.log.WithError(err).Error("Failed to write not-comply log to Redis")
				}
			}
		} else {
			// Store the original task payload for re-scheduling
			if w.logWriter != nil {
				// Store the job in Redis for re-scheduling using IMEI from payload
				if err := w.logWriter.StoreRescheduleJob(string(APICaveoWorker), task.Payload, payload.Config.Device.Ident); err != nil {
					w.log.WithError(err).Error("Failed to store re-schedule job in Redis")
				}

				// Log re-schedule to logwriter
				metadata := map[string]interface{}{
					"accuracy":         apiResponse.Accuracy,
					"imei":             apiResponse.IMEI,
					"PSM":              payload.Config.Device.PSM,
					"Estate":           payload.Config.Device.Estate,
					"Division":         payload.Config.Device.Division,
					"NIK":              payload.Config.Device.NIK,
					"total_true":       apiResponse.TotalTrue,
					"total_tracking":   apiResponse.TotalTracking,
					"task_id":          task.ID,
					"date":             endDOnly,
					"timeRange":        timeRange,
					"reschedule_count": task.RescheduleCount,
					"max_reschedule":   maxRescheduleCount,
					"reason":           "Insufficient accuracy",
				}
				key := fmt.Sprintf("logs:api_pull_caveo:%s:re-schedule:%s", endDOnly, logID)
				if err := w.logWriter.LogReschedule(key, string(APICaveoWorker), metadata); err != nil {
					w.log.WithError(err).Error("Failed to write re-schedule log to Redis")
				}
			}
		}
	}

	return nil
}

// storeInMinio stores the API response in Minio
func (w *CaveoAPIPullWorker) storeInMinio(ctx context.Context, response CaveoAPIResponse, imei string, timeStart string, timeEnd string) error {
	// Parse time to get time start - end
	start, err := time.Parse("2006-01-02 15:04:05", timeStart)
	if err != nil {
		return fmt.Errorf("failed to parse time: %w", err)
	}
	startTOnly := start.Format("15:04:05")
	end, err := time.Parse("2006-01-02 15:04:05", timeEnd)
	if err != nil {
		return fmt.Errorf("failed to parse time: %w", err)
	}
	endTOnly := end.Format("15:04:05")

	// Create folder name
	filename := fmt.Sprintf("%s %s-%s.json", imei, startTOnly, endTOnly)
	folderName := end.Format("2006-01-02")

	// Create object name
	objectName := filepath.Join(folderName, imei, filename)

	// Convert response to JSON
	responseJSON, err := json.Marshal(response)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	// Check if bucket is set
	if w.minioBucket == "" {
		return fmt.Errorf("caveo API bucket is not set")
	}

	// Check if bucket exists, create if not
	exists, err := w.minioClient.BucketExists(ctx, w.minioBucket)
	if err != nil {
		return fmt.Errorf("failed to check if bucket exists: %w", err)
	}
	if !exists {
		if err := w.minioClient.MakeBucket(ctx, w.minioBucket, minio.MakeBucketOptions{}); err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}

	}

	// Try to create metadata for the object
	// This is optional and should not block the process if it fails
	metadata := make(map[string]string)

	// Import the metadata package
	metadataObj := struct {
		URL        string `json:"url"`
		PSM        string `json:"psm"`
		Estate     string `json:"estate"`
		Division   string `json:"division"`
		NIK        string `json:"nik"`
		DataLength int    `json:"data_length"`
	}{
		URL:        w.baseURL + "/get-tracking",
		PSM:        "PSM 2",
		Estate:     "SMSE",
		Division:   "DIVISI 4",
		NIK:        "BACKUP",
		DataLength: len(response.Data),
	}

	// Convert metadata to JSON
	metadataJSON, err := json.Marshal(metadataObj)
	if err == nil {
		metadata["x-amz-meta-caveo-api-metadata"] = string(metadataJSON)
	} else {
		// Log error but continue with the upload
		w.log.WithError(err).Warn("Failed to create metadata for Minio object, continuing without metadata")
	}

	// Set content type and metadata
	putOptions := minio.PutObjectOptions{
		ContentType: "application/json",
	}

	// Add metadata if available
	if len(metadata) > 0 {
		putOptions.UserMetadata = metadata
	}

	// Upload to Minio
	_, err = w.minioClient.PutObject(
		ctx,
		w.minioBucket,
		objectName,
		bytes.NewReader(responseJSON),
		int64(len(responseJSON)),
		putOptions,
	)
	if err != nil {
		return fmt.Errorf("failed to upload to Minio: %w", err)
	}

	return nil
}

// storeFailedResponseInMinio stores a failed HTTP response in the "failed-jobs" bucket
func (w *CaveoAPIPullWorker) storeFailedResponseInMinio(ctx context.Context, responseBody []byte, taskID string, dateStr string, payload *CaveoAPIPayload) error {
	// Create object name: YYYY-MM-DD/task.ID
	objectName := filepath.Join(dateStr, taskID)

	// Create metadata for the failed response
	metadata := map[string]string{
		"x-amz-meta-task-id":    taskID,
		"x-amz-meta-imei":       payload.Config.Device.Ident,
		"x-amz-meta-time-start": payload.Config.TimeStart,
		"x-amz-meta-time-end":   payload.Config.TimeEnd,
		"x-amz-meta-api-url":    w.baseURL + "/get-tracking",
		"x-amz-meta-error":      "Failed to decode API response",
		"x-amz-meta-timestamp":  time.Now().UTC().Format(time.RFC3339),
	}

	// Set content type and metadata
	putOptions := minio.PutObjectOptions{
		ContentType:  "application/json",
		UserMetadata: metadata,
	}

	// Check if "failed-jobs" bucket exists, create if not
	bucketName := "failed-jobs"
	exists, err := w.minioClient.BucketExists(ctx, bucketName)
	if err != nil {
		return fmt.Errorf("failed to check if bucket exists: %w", err)
	}
	if !exists {
		if err := w.minioClient.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{}); err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		w.log.WithField("bucket", bucketName).Info("Created failed-jobs bucket")
	}

	// Upload the failed response to Minio
	_, err = w.minioClient.PutObject(
		ctx,
		bucketName,
		objectName,
		bytes.NewReader(responseBody),
		int64(len(responseBody)),
		putOptions,
	)
	if err != nil {
		return fmt.Errorf("failed to upload failed response to Minio: %w", err)
	}

	return nil
}

// Type returns the type of worker
func (w *CaveoAPIPullWorker) Type() WorkerType {
	return APICaveoWorker
}

// SetLogWriter sets the log writer for the worker
func (w *CaveoAPIPullWorker) SetLogWriter(lw *logwriter.LogWriter) {
	w.logWriter = lw
}

// SetDispatcher sets the dispatcher for the worker (not used by this worker)
func (w *CaveoAPIPullWorker) SetDispatcher(dispatcher interface{}) {
	// This worker doesn't need a dispatcher
}

// SetRedisClient sets the Redis client for the worker (not used by this worker)
func (w *CaveoAPIPullWorker) SetRedisClient(client interface{}) {
	// This worker doesn't need a Redis client
}

// toString is a helper function to convert any value to a string
func toString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case float64:
		return fmt.Sprintf("%.0f", v) // Convert float to string without decimal
	case int:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case json.Number:
		return string(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}
