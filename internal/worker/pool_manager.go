package worker

import (
	"context"
	"fmt"
	"sync"

	"github.com/user/workers/config"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

// PoolManager manages worker pools based on configuration
type PoolManager struct {
	ctx           context.Context
	cancel        context.CancelFunc
	log           *logger.Logger
	workersConfig *config.WorkersConfig
	pool          *Pool
	logWriter     *logwriter.LogWriter
	workersMu     sync.RWMutex
	workerCount   map[string]int // Track the number of instances for each worker type
	configMap     map[string]interface{}
}

// NewPoolManager creates a new pool manager
func NewPoolManager(ctx context.Context, log *logger.Logger, workersConfig *config.WorkersConfig, configMap map[string]interface{}, lw *logwriter.LogWriter) *PoolManager {
	ctx, cancel := context.WithCancel(ctx)

	// Calculate total worker count from configuration
	totalWorkers := 0
	for _, workerConfig := range workersConfig.GetEnabledWorkers() {
		totalWorkers += workerConfig.Count
	}

	return &PoolManager{
		ctx:           ctx,
		cancel:        cancel,
		log:           log,
		workersConfig: workersConfig,
		pool:          NewPool(ctx, totalWorkers, log), // Set worker count based on config
		logWriter:     lw,
		workersMu:     sync.RWMutex{},
		workerCount:   make(map[string]int),
		configMap:     configMap,
	}
}

// Start initializes and starts the worker pool based on configuration
func (pm *PoolManager) Start() error {
	pm.log.Info("Starting worker pool manager")

	// Get enabled workers from configuration
	enabledWorkers := pm.workersConfig.GetEnabledWorkers()

	// Initialize workers based on configuration
	for workerType, workerConfig := range enabledWorkers {

		// Convert string worker type to WorkerType
		var wType WorkerType
		switch workerType {
		case "api_pull":
			wType = APIWorker
		case "api_pull_caveo":
			wType = APICaveoWorker
		case "db_clone":
			wType = DBWorker
		case "caveo_payload_maker":
			wType = PayloadMakerWorker
		case "reschedule_caveo":
			wType = RescheduleCaveoWorker
		case "caveo_to_influx":
			wType = CaveoToInfluxWorkerType
		case "geof_division":
			wType = GeofDivisionWorkerType
		case "geof_estate":
			wType = GeofEstateWorkerType
		case "geof_block":
			wType = GeofBlockWorkerType
		default:
			pm.log.WithField("worker_type", workerType).Warn("Unknown worker type in configuration, skipping")
			continue
		}

		// Create and register worker instances
		for i := 0; i < workerConfig.Count; i++ {
			// Create worker
			worker, err := NewWorker(wType, pm.log, pm.configMap)
			if err != nil {
				return fmt.Errorf("failed to create worker of type %s: %w", workerType, err)
			}

			// Set log writer
			worker.SetLogWriter(pm.logWriter)

			// Register worker
			pm.pool.RegisterWorker(worker)

			// Update worker count
			pm.workersMu.Lock()
			pm.workerCount[workerType]++
			pm.workersMu.Unlock()

			pm.log.WithFields(map[string]interface{}{
				"worker_type": workerType,
				"instance":    i + 1,
				"total":       workerConfig.Count,
			}).Info("Worker instance created and registered")
		}
	}

	// Start the worker pool
	pm.pool.Start()

	return nil
}

// GetPool returns the worker pool
func (pm *PoolManager) GetPool() *Pool {
	return pm.pool
}

// UpdateWorkerCount updates the number of worker instances based on configuration
func (pm *PoolManager) UpdateWorkerCount() error {
	pm.log.Info("Updating worker count based on configuration")

	// Get enabled workers from configuration
	enabledWorkers := pm.workersConfig.GetEnabledWorkers()

	// Check if we need to add or remove workers
	for workerType, workerConfig := range enabledWorkers {
		// Get current worker count
		pm.workersMu.RLock()
		currentCount := pm.workerCount[workerType]
		pm.workersMu.RUnlock()

		// Convert string worker type to WorkerType
		var wType WorkerType
		switch workerType {
		case "api_pull":
			wType = APIWorker
		case "api_pull_caveo":
			wType = APICaveoWorker
		case "db_clone":
			wType = DBWorker
		case "reschedule_caveo":
			wType = RescheduleCaveoWorker
		default:
			pm.log.WithField("worker_type", workerType).Warn("Unknown worker type in configuration, skipping")
			continue
		}

		// Add workers if needed
		if currentCount < workerConfig.Count {
			for i := currentCount; i < workerConfig.Count; i++ {
				// Create worker
				worker, err := NewWorker(wType, pm.log, pm.configMap)
				if err != nil {
					return fmt.Errorf("failed to create worker of type %s: %w", workerType, err)
				}

				// Set log writer
				worker.SetLogWriter(pm.logWriter)

				// Register worker
				pm.pool.RegisterWorker(worker)

				// Update worker count
				pm.workersMu.Lock()
				pm.workerCount[workerType]++
				pm.workersMu.Unlock()

				pm.log.WithFields(map[string]interface{}{
					"worker_type": workerType,
					"instance":    i + 1,
					"total":       workerConfig.Count,
				}).Info("Worker instance created and registered")
			}
		}
		// Note: We can't easily remove workers once they're added to the pool
		// This would require a more complex implementation with worker IDs and tracking
	}

	return nil
}

// Shutdown gracefully shuts down the worker pool
func (pm *PoolManager) Shutdown() {
	pm.log.Info("Shutting down worker pool manager")

	// Signal all workers to stop
	pm.cancel()

	// Shutdown the worker pool
	pm.pool.Shutdown()

	pm.log.Info("Worker pool manager shutdown complete")
}
