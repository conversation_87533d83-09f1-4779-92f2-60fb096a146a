package token

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"
)

// TokenResponse represents the response from the token API
type TokenResponse struct {
	Status  bool   `json:"status"`
	Message string `json:"message"`
	Token   string `json:"token"`
}

// TokenRequest represents the request to the token API
type TokenRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// Manager is a thread-safe token manager
type Manager struct {
	mu            sync.RWMutex
	token         string
	expiresAt     time.Time
	username      string
	password      string
	tokenEndpoint string
	client        *http.Client
}

// NewManager creates a new token manager
func NewManager(username, password, tokenEndpoint string, timeout time.Duration) *Manager {
	return &Manager{
		username:      username,
		password:      password,
		tokenEndpoint: tokenEndpoint,
		client:        &http.Client{Timeout: timeout},
	}
}

// GetToken returns the current token, refreshing it if necessary
func (m *Manager) GetToken() (string, error) {
	m.mu.RLock()
	if m.token != "" && !m.IsExpired() {
		token := m.token
		m.mu.RUnlock()
		return token, nil
	}
	m.mu.RUnlock()

	// Token is expired or not set, refresh it
	return m.RefreshToken()
}

// SetToken sets the token and its expiry time
func (m *Manager) SetToken(token string, expiresIn time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.token = token
	m.expiresAt = time.Now().Add(expiresIn)
}

// IsExpired checks if the token is expired or about to expire
func (m *Manager) IsExpired() bool {
	// Consider token expired if it's within 5 minutes of expiry
	return m.expiresAt.Before(time.Now().Add(5 * time.Minute))
}

// RefreshToken refreshes the token by making an API call
func (m *Manager) RefreshToken() (string, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Prepare request body
	reqBody := TokenRequest{
		Username: m.username,
		Password: m.password,
	}

	// Convert to JSON
	reqJSON, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal token request: %w", err)
	}

	// Create request
	req, err := http.NewRequest(http.MethodPost, m.tokenEndpoint, bytes.NewBuffer(reqJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create token request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := m.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send token request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("token request failed with status code: %d", resp.StatusCode)
	}

	// Parse response
	var tokenResp TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return "", fmt.Errorf("failed to decode token response: %w", err)
	}

	// Check if token was returned
	if tokenResp.Token == "" {
		return "", fmt.Errorf("no token returned in response: %s", tokenResp.Message)
	}

	// Set token with default expiry of 24 hours
	// This can be adjusted based on the actual token expiry from the API
	m.token = tokenResp.Token
	m.expiresAt = time.Now().Add(24 * time.Hour)

	return m.token, nil
}
