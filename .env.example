# Application configuration
LOG_LEVEL=debug
PORT=8080
WORKER_POOL_SIZE=5
ENVIRONMENT=development

# RabbitMQ configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_PORT_MANAGEMENT=15672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=password
RABBITMQ_API_PULL_QUEUE=api_pull_tasks
RABBITMQ_API_PULL_CAVEO_QUEUE=api_pull_caveo_tasks
RABBITMQ_DB_CLONE_QUEUE=db_clone_tasks

# API configuration
API_BASE_URL=https://api.example.com
API_TIMEOUT=30

# Caveo API configuration
CAVEO_API_BASE_URL=https://trackvisionindo.ddns.net/api2
CAVEO_API_TOKEN=y2dbEauyOUlZmYdR
CAVEO_API_TIMEOUT=30
CAVEO_API_TOKEN_USERNAME=sinarmas
CAVEO_API_TOKEN_PASSWORD=sinar2024

# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_password
REDIS_DB=0
REDIS_MAX_MEMORY=2gb
REDIS_INSIGHT_PORT=8001

# Minio configuration
MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=password
MINIO_USE_SSL=false
MINIO_BUCKET=api-pull-files
DB_CLONE_BUCKET=db-clone-files

# Source database configuration
SOURCE_DB_HOST=localhost
SOURCE_DB_PORT=5432
SOURCE_DB_USER=postgres
SOURCE_DB_PASSWORD=postgres
SOURCE_DB_NAME=source_db

# Target database configuration
TARGET_DB_HOST=localhost
TARGET_DB_PORT=5432
TARGET_DB_USER=postgres
TARGET_DB_PASSWORD=postgres
TARGET_DB_NAME=target_db

# Timeseries database configuration
INFLUX_PORT=8086
INFLUX_ADMIN_USER=superadmin
INFLUX_ADMIN_PASSWORD=21.Influxdb
INFLUX_TOKEN=WgGjO9ZGqDD_Y_4A20JLUysVRxBvlGOEAEnOcY30XcyPA8LUVylhwG6r5vKIkJmpiioqKcAphUJ726oFBr8DZw==
INFLUX_ORG=smrtp_beta

# Reschedule worker configuration
RESCHEDULE_TIME_THRESHOLD_HOURS=4
