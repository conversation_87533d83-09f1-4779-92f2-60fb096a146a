# Worker Pool Project

This is a Golang project that implements a worker pool architecture with support for different types of workers. The project is designed to be modular, scalable, and follows Golang best practices.

## Features

- Worker pool with configurable number of workers
- Support for different types of workers:
  - API Pulling Worker: Pulls data from external APIs
  - DB Cloning Worker: Clones data from one database to another
  - Caveo API Pull Worker: Pulls data from the Caveo API and stores it in Minio
- RabbitMQ integration for task distribution
- REST API for submitting jobs to workers
- Cronjob functionality for scheduling recurring tasks
- Redis-based storage for cronjob definitions
- CORS support for all origins
- Graceful shutdown handling
- Configurable via environment variables and workers.json configuration file

## Project Structure

```
Workers/
├── api/
│   ├── server.go                # REST API server implementation
│   ├── models.go                # API request/response models
│   └── cronjob_handlers.go      # Cronjob API handlers
├── cmd/
│   └── worker/
│       └── main.go              # Main application entry point
├── config/
│   ├── config.go                # Configuration management
│   ├── worker_config.go         # Worker configuration management
│   └── workers.json             # Worker pool configuration
├── docs/
│   └── cronjob.md               # Cronjob feature documentation
├── internal/
│   ├── cronjob/
│   │   ├── types.go             # Cronjob type definitions
│   │   ├── manager.go           # Cronjob manager implementation
│   │   └── redis_storage.go     # Redis-based cronjob storage
│   ├── queue/
│   │   ├── rabbitmq.go          # RabbitMQ client
│   │   └── consumer.go          # Message consumer
│   ├── worker/
│   │   ├── pool.go              # Worker pool implementation
│   │   ├── worker.go            # Worker interface
│   │   ├── api_pulling_worker.go # API pulling worker implementation
│   │   ├── db_cloning_worker.go # DB cloning worker implementation
│   │   └── api_pull_caveo_worker.go # Caveo API pull worker implementation
│   └── dispatcher/
│       └── dispatcher.go        # Task dispatcher
├── pkg/
│   └── logger/
│       └── logger.go            # Logging utilities
├── script/
│   └── test_api.sh              # Script to test the REST API
├── go.mod
├── go.sum
├── .env.example                 # Example environment variables
├── Dockerfile                   # Docker configuration
└── README.md                    # Project documentation
```

## Prerequisites

- Go 1.18 or higher
- RabbitMQ server
- Docker (optional)

## Getting Started

### Local Development

1. Clone the repository:

```bash
git clone https://github.com/yourusername/workers.git
cd workers
```

2. Copy the example environment file:

```bash
cp .env.example .env
```

3. Modify the `.env` file to match your environment.

4. Review and update the `config/workers.json` file to configure the worker pool:

```json
{
  "workers": {
    "api_pull": {
      "enabled": true,
      "count": 2,
      "queue": "api_pull_tasks",
      "bucket": "api-pull-files",
      "timeout": 30,
      "retry_attempts": 3,
      "retry_delay": 5
    },
    "api_pull_caveo": {
      "enabled": true,
      "count": 2,
      "queue": "api_pull_caveo_tasks",
      "bucket": "api-pull-caveo-files",
      "timeout": 120,
      "retry_attempts": 3,
      "retry_delay": 10,
      "api_endpoint": "https://trackvisionindo.ddns.net/api2"
    },
    "db_clone": {
      "enabled": true,
      "count": 1,
      "queue": "db_clone_tasks",
      "bucket": "db-clone-files",
      "batch_size": 1000,
      "timeout": 60
    }
  },
  "global": {
    "log_level": "info",
    "max_concurrent_tasks": 10,
    "health_check_interval": 60
  }
}
```

Each worker type has the following configuration options:

**Common options for all workers:**
- `enabled`: Whether the worker is enabled
- `count`: Number of worker instances to create
- `queue`: RabbitMQ queue name for tasks
- `bucket`: Minio bucket name for storing files
- `timeout`: Request timeout in seconds
- `retry_attempts`: Number of retry attempts for failed tasks
- `retry_delay`: Delay between retry attempts in seconds

**Worker-specific options:**
- API Pull Worker:
  - No additional specific options

- Caveo API Pull Worker:
  - `api_endpoint`: The base URL for the Caveo API

- DB Clone Worker:
  - `batch_size`: Number of records to process in a single batch

**Global configuration options:**
- `log_level`: Global logging level (debug, info, warn, error)
- `max_concurrent_tasks`: Maximum number of concurrent tasks across all workers
- `health_check_interval`: Interval in seconds for health checks

5. Install dependencies:

```bash
go mod download
```

6. Build and run the application:

```bash
go build -o worker ./cmd/worker
./worker
```

### Using Docker

1. Build the Docker image:

```bash
docker build -t worker-pool .
```

2. Run the container:

```bash
docker run -p 8080:8080 --env-file .env worker-pool
```

## REST API

The application provides a REST API for submitting jobs to workers and managing cronjobs. The API is available at `http://localhost:8080/api`.

### Endpoints

#### Health Check

```
GET /api/health
```

Response:

```json
{
  "status": "ok",
  "time": "2023-05-12T15:04:05Z"
}
```

#### Submit Job

```
POST /api/jobs
```

Request body:

```json
{
  "worker_type": "api_pull_caveo",
  "config": {
    "device": {
      "psm": "PSM 2",
      "estate": "SMSE",
      "division": "DIVISI 1",
      "ident": "868738070028697",
      "nik": "SMSE-07002",
      "name": "HOLIDIN",
      "type": "Pemanen"
    },
    "timeStart": "2025-05-12 02:00:00",
    "timeEnd": "2025-05-13 00:00:00"
  }
}
```

Response:

```json
{
  "task_id": "task-123456789",
  "status": "accepted",
  "message": "Task dispatched successfully"
}
```

#### Get Job Status

```
GET /api/jobs/{id}
```

Response:

```json
{
  "task_id": "task-123456789",
  "status": "processing",
  "message": "Task is being processed"
}
```

#### Cronjob Endpoints

The application provides endpoints for managing cronjobs:

```
GET /api/cronjobs                  # List all cronjobs
POST /api/cronjobs                 # Create a new cronjob
GET /api/cronjobs/{name}           # Get a specific cronjob
PUT /api/cronjobs/{name}           # Update a cronjob
DELETE /api/cronjobs/{name}        # Delete a cronjob
POST /api/cronjobs/{name}/enable   # Enable a cronjob
POST /api/cronjobs/{name}/disable  # Disable a cronjob
POST /api/cronjobs/{name}/execute  # Execute a cronjob immediately
```

Example request to create a cronjob:

```
POST /api/cronjobs
```

Request body:

```json
{
  "name": "daily_api_pull",
  "description": "Pull data from API daily at midnight",
  "enabled": true,
  "schedule": "0 0 0 * * *",
  "worker_type": "api_pull",
  "payload": {
    "endpoint": "https://api.example.com/data",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer token123"
    },
    "params": {
      "date": "${YESTERDAY_DATE}"
    }
  },
  "variables": {
    "YESTERDAY_DATE": "function() { return moment().subtract(1, 'day').format('YYYY-MM-DD'); }"
  },
  "retry_policy": {
    "attempts": 3,
    "delay": 300
  },
  "timeout": 600
}
```

Response:

```json
{
  "status": "ok",
  "message": "Cronjob created successfully",
  "cronjob": {
    "name": "daily_api_pull",
    "description": "Pull data from API daily at midnight",
    "enabled": true,
    "schedule": "0 0 * * *",
    "worker_type": "api_pull",
    "payload": {
      "endpoint": "https://api.example.com/data",
      "method": "GET",
      "headers": {
        "Authorization": "Bearer token123"
      },
      "params": {
        "date": "${YESTERDAY_DATE}"
      }
    },
    "variables": {
      "YESTERDAY_DATE": "function() { return moment().subtract(1, 'day').format('YYYY-MM-DD'); }"
    },
    "retry_policy": {
      "attempts": 3,
      "delay": 300
    },
    "timeout": 600,
    "created_at": "2023-05-12T15:04:05Z",
    "updated_at": "2023-05-12T15:04:05Z",
    "next_run": "2023-05-13T00:00:00Z"
  }
}
```

For more information about the cronjob feature, see the [cronjob documentation](docs/cronjob.md).

### Testing the API

You can use the provided test script to test the API:

```bash
./script/test_api.sh
```

## RabbitMQ Message Format

The application expects messages in the following JSON format:

```json
{
  "id": "task-123",
  "type": "api_pulling_worker",
  "payload": {
    "endpoint": "/api/data",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer token123"
    }
  },
  "created_at": 1633046400
}
```

For DB cloning worker:

```json
{
  "id": "task-456",
  "type": "db_cloning_worker",
  "payload": {
    "table": "users",
    "columns": ["id", "name", "email"],
    "conditions": "created_at > '2023-01-01'",
    "batch_size": 1000
  },
  "created_at": 1633046400
}
```

For Caveo API worker:

```json
{
  "id": "task-789",
  "type": "api_pull_caveo",
  "payload": {
    "worker_type": "api_pull_caveo",
    "config": {
      "device": {
        "psm": "PSM 2",
        "estate": "SMSE",
        "division": "DIVISI 1",
        "ident": "868738070028697",
        "nik": "SMSE-07002",
        "name": "HOLIDIN",
        "type": "Pemanen"
      },
      "timeStart": "2025-05-12 02:00:00",
      "timeEnd": "2025-05-13 00:00:00"
    }
  },
  "created_at": 1633046400
}
```

## Configuration

The application can be configured using both environment variables and the workers.json configuration file.

### Environment Variables

Environment variables are used for general application configuration, such as:
- API endpoints and credentials
- Database connection details
- RabbitMQ connection details
- Minio connection details
- Redis connection details

See the `.env.example` file for a complete list of available environment variables.

### Cronjob Configuration

Cronjobs are stored in Redis and can be managed through the REST API. Each cronjob has the following configuration options:

- `name`: A unique name for the cronjob
- `description`: A description of the cronjob
- `enabled`: Whether the cronjob is enabled
- `schedule`: The cron expression for the schedule (supports 6-field format with seconds)
- `worker_type`: The type of worker to execute the task
- `payload`: The payload to send to the worker
- `variables`: Variables to be processed in the payload
- `retry_policy`: The retry policy for failed tasks
- `timeout`: The timeout for the task in seconds

The cron expression uses a 6-field format that includes seconds:
```
┌───────────── second (0 - 59)
│ ┌───────────── minute (0 - 59)
│ │ ┌───────────── hour (0 - 23)
│ │ │ ┌───────────── day of the month (1 - 31)
│ │ │ │ ┌───────────── month (1 - 12)
│ │ │ │ │ ┌───────────── day of the week (0 - 6) (Sunday to Saturday)
│ │ │ │ │ │
│ │ │ │ │ │
* * * * * *
```

For example, to run a job every 15 seconds, use `*/15 * * * * *`.

Example cronjob configuration:

```json
{
  "name": "daily_api_pull",
  "description": "Pull data from API daily at midnight",
  "enabled": true,
  "schedule": "0 0 * * *",
  "worker_type": "api_pull",
  "payload": {
    "endpoint": "https://api.example.com/data",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer ${AUTH_TOKEN}"
    },
    "params": {
      "date": "${YESTERDAY_DATE}"
    }
  },
  "variables": {
    "AUTH_TOKEN": "function() { return 'token123'; }",
    "YESTERDAY_DATE": "function() { return moment().subtract(1, 'day').format('YYYY-MM-DD'); }"
  },
  "retry_policy": {
    "attempts": 3,
    "delay": 300
  },
  "timeout": 600
}
```

For more information about the cronjob feature, see the [cronjob documentation](docs/cronjob.md).

### Workers Configuration (workers.json)

The `config/workers.json` file is used to configure the worker pool, including:
- Which worker types are enabled
- How many instances of each worker type to create
- Which RabbitMQ queue each worker type should consume from
- Which Minio bucket each worker type should use for storage
- Worker-specific settings like timeouts, retry attempts, and API endpoints
- Global settings that apply to all workers

This centralized configuration makes it easier to manage worker settings without changing environment variables or recompiling the application.

#### Worker-Specific Configuration

Each worker type can have its own specific configuration options:

```json
"api_pull_caveo": {
  "enabled": true,
  "count": 2,
  "queue": "api_pull_caveo_tasks",
  "bucket": "api-pull-caveo-files",
  "timeout": 120,
  "retry_attempts": 3,
  "retry_delay": 10,
  "api_endpoint": "https://trackvisionindo.ddns.net/api2"
}
```

This allows you to fine-tune each worker type's behavior without modifying code.

#### Global Configuration

Global settings apply to all workers and the application as a whole:

```json
"global": {
  "log_level": "info",
  "max_concurrent_tasks": 10,
  "health_check_interval": 60
}
```

These settings provide application-wide controls that can be adjusted without recompiling the application.

#### Migrating from Environment Variables to workers.json

To migrate worker-specific configuration from environment variables to workers.json:

1. Identify worker-specific environment variables in your `.env` file
2. Add corresponding settings to the appropriate worker in `workers.json`
3. Update your code to read these settings from the workers.json configuration

For example, migrating the Caveo API bucket setting:

**Before (in .env):**
```
CAVEO_API_BUCKET=api-pull-caveo
```

**After (in workers.json):**
```json
"api_pull_caveo": {
  "bucket": "api-pull-caveo-files"
}
```

**Code changes:**
```go
// Before: Reading from environment variable
bucket := os.Getenv("CAVEO_API_BUCKET")

// After: Reading from workers.json configuration
workerConfig, exists := workersConfig.GetWorkerConfig("api_pull_caveo")
if exists {
    bucket := workerConfig.Bucket
}
```

This approach centralizes configuration and makes it easier to manage worker-specific settings.

## Graceful Shutdown

The application handles graceful shutdown by:

1. Capturing termination signals (SIGINT, SIGTERM)
2. Stopping the consumer to prevent new tasks from being accepted
3. Waiting for all workers to finish their current tasks
4. Closing all connections and releasing resources

## Adding New Worker Types

To add a new worker type:

1. Define a new worker type constant in `internal/worker/worker.go`
2. Create a new worker implementation file (e.g., `internal/worker/new_worker.go`)
3. Implement the `Worker` interface
4. Update the `config/workers.json` file to include the new worker type with appropriate configuration:
   ```json
   "new_worker": {
     "enabled": true,
     "count": 1,
     "queue": "new_worker_tasks",
     "bucket": "new-worker-files",
     "timeout": 60,
     "retry_attempts": 3,
     "retry_delay": 5,
     "custom_option_1": "value1",
     "custom_option_2": "value2"
   }
   ```

   Make sure to add any worker-specific configuration options that your new worker type needs.
5. Update the worker registration logic in `cmd/worker/main.go` to handle the new worker type
6. Create cronjobs for the new worker type if needed:
   ```json
   {
     "name": "new_worker_job",
     "description": "Execute new worker job daily",
     "enabled": true,
     "schedule": "0 0 0 * * *",
     "worker_type": "new_worker",
     "payload": {
       "custom_option_1": "value1",
       "custom_option_2": "value2"
     },
     "retry_policy": {
       "attempts": 3,
       "delay": 300
     },
     "timeout": 600
   }
   ```

## Testing and Updating Configuration

### Testing Configuration Changes

When making changes to the workers.json configuration, it's important to test them in a controlled environment before deploying to production:

1. Make a backup of your current configuration:
   ```bash
   cp config/workers.json config/workers.json.bak
   ```

2. Update the configuration with your changes:
   ```bash
   # Edit config/workers.json with your changes
   ```

3. Start the application in a test environment:
   ```bash
   go run ./cmd/worker/main.go
   ```

4. Monitor the logs for any configuration-related issues:
   ```bash
   tail -f logs/application.log
   ```

5. If everything works as expected, deploy the changes to production.

### Hot Reloading Configuration

The application supports hot reloading of the workers.json configuration. This means you can update the configuration without restarting the application:

1. Edit the workers.json file with your changes
2. The application will automatically detect the changes and reload the configuration
3. Check the logs to confirm the configuration was reloaded successfully

This feature is particularly useful for adjusting worker counts, timeouts, and other settings in response to changing workloads.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
