package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"time"
)

// SystemMetrics represents system-level metrics
type SystemMetrics struct {
	Timestamp    string            `json:"timestamp"`
	CPU          CPUMetrics        `json:"cpu"`
	Memory       MemoryMetrics     `json:"memory"`
	Goroutines   int               `json:"goroutines"`
	GC           GCMetrics         `json:"gc"`
	Runtime      RuntimeMetrics    `json:"runtime"`
	Application  ApplicationMetrics `json:"application"`
}

// CPUMetrics represents CPU-related metrics
type CPUMetrics struct {
	NumCPU int `json:"num_cpu"`
}

// MemoryMetrics represents memory-related metrics
type MemoryMetrics struct {
	Alloc        uint64 `json:"alloc"`
	TotalAlloc   uint64 `json:"total_alloc"`
	Sys          uint64 `json:"sys"`
	Lookups      uint64 `json:"lookups"`
	Mallocs      uint64 `json:"mallocs"`
	Frees        uint64 `json:"frees"`
	HeapAlloc    uint64 `json:"heap_alloc"`
	HeapSys      uint64 `json:"heap_sys"`
	HeapIdle     uint64 `json:"heap_idle"`
	HeapInuse    uint64 `json:"heap_inuse"`
	HeapReleased uint64 `json:"heap_released"`
	HeapObjects  uint64 `json:"heap_objects"`
	StackInuse   uint64 `json:"stack_inuse"`
	StackSys     uint64 `json:"stack_sys"`
}

// GCMetrics represents garbage collection metrics
type GCMetrics struct {
	NumGC        uint32  `json:"num_gc"`
	PauseTotal   uint64  `json:"pause_total_ns"`
	LastPause    uint64  `json:"last_pause_ns"`
	NextGC       uint64  `json:"next_gc"`
	EnableGC     bool    `json:"enable_gc"`
	DebugGC      bool    `json:"debug_gc"`
	GCCPUFraction float64 `json:"gc_cpu_fraction"`
}

// RuntimeMetrics represents Go runtime metrics
type RuntimeMetrics struct {
	Version   string `json:"version"`
	GOOS      string `json:"goos"`
	GOARCH    string `json:"goarch"`
	Compiler  string `json:"compiler"`
	NumCgoCall int64  `json:"num_cgo_call"`
}

// ApplicationMetrics represents application-specific metrics
type ApplicationMetrics struct {
	Uptime   string `json:"uptime"`
	Version  string `json:"version"`
	BuildTime string `json:"build_time,omitempty"`
}

// RabbitMQMetrics represents RabbitMQ-specific metrics
type RabbitMQMetrics struct {
	Timestamp   string                 `json:"timestamp"`
	Overview    map[string]interface{} `json:"overview,omitempty"`
	Queues      QueueMetrics           `json:"queues"`
	Connections ConnectionMetrics      `json:"connections"`
	Channels    ChannelMetrics         `json:"channels"`
	Messages    MessageMetrics         `json:"messages"`
}

// QueueMetrics represents queue-related metrics
type QueueMetrics struct {
	Total     int                      `json:"total"`
	Details   []map[string]interface{} `json:"details,omitempty"`
	Summary   QueueSummary             `json:"summary"`
}

// QueueSummary represents a summary of queue metrics
type QueueSummary struct {
	TotalMessages int64 `json:"total_messages"`
	ReadyMessages int64 `json:"ready_messages"`
	UnackedMessages int64 `json:"unacked_messages"`
}

// ConnectionMetrics represents connection-related metrics
type ConnectionMetrics struct {
	Total   int                      `json:"total"`
	Details []map[string]interface{} `json:"details,omitempty"`
}

// ChannelMetrics represents channel-related metrics
type ChannelMetrics struct {
	Total   int                      `json:"total"`
	Details []map[string]interface{} `json:"details,omitempty"`
}

// MessageMetrics represents message-related metrics
type MessageMetrics struct {
	PublishRate   float64 `json:"publish_rate"`
	DeliverRate   float64 `json:"deliver_rate"`
	AckRate       float64 `json:"ack_rate"`
	TotalMessages int64   `json:"total_messages"`
}

var startTime = time.Now()

// handleMetrics handles the general metrics endpoint (Prometheus format)
func (s *Server) handleMetrics() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get system metrics
		systemMetrics := s.getSystemMetrics()
		
		// Convert to Prometheus format
		prometheusFormat := s.convertToPrometheusFormat(systemMetrics)
		
		w.Header().Set("Content-Type", "text/plain; version=0.0.4; charset=utf-8")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(prometheusFormat))
	}
}

// handleMetricsRabbitMQ handles the RabbitMQ metrics endpoint
func (s *Server) handleMetricsRabbitMQ() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		metrics := RabbitMQMetrics{
			Timestamp: time.Now().Format(time.RFC3339),
		}

		if s.rabbitMQClient != nil {
			// Get overview
			if overview, err := s.rabbitMQClient.GetOverview(ctx); err == nil {
				metrics.Overview = overview
			}

			// Get queues
			if queues, err := s.rabbitMQClient.GetQueues(ctx); err == nil {
				metrics.Queues.Total = len(queues)
				metrics.Queues.Details = queues
				
				// Calculate queue summary
				var totalMessages, readyMessages, unackedMessages int64
				for _, queue := range queues {
					if messages, ok := queue["messages"].(float64); ok {
						totalMessages += int64(messages)
					}
					if ready, ok := queue["messages_ready"].(float64); ok {
						readyMessages += int64(ready)
					}
					if unacked, ok := queue["messages_unacknowledged"].(float64); ok {
						unackedMessages += int64(unacked)
					}
				}
				metrics.Queues.Summary = QueueSummary{
					TotalMessages:   totalMessages,
					ReadyMessages:   readyMessages,
					UnackedMessages: unackedMessages,
				}
			}

			// Get connections
			if connections, err := s.rabbitMQClient.GetConnections(ctx); err == nil {
				metrics.Connections.Total = len(connections)
				metrics.Connections.Details = connections
			}

			// Get channels
			if channels, err := s.rabbitMQClient.GetChannels(ctx); err == nil {
				metrics.Channels.Total = len(channels)
				metrics.Channels.Details = channels
			}

			// Calculate message metrics from overview
			if metrics.Overview != nil {
				if messageStats, ok := metrics.Overview["message_stats"].(map[string]interface{}); ok {
					if publishDetails, ok := messageStats["publish_details"].(map[string]interface{}); ok {
						if rate, ok := publishDetails["rate"].(float64); ok {
							metrics.Messages.PublishRate = rate
						}
					}
					if deliverDetails, ok := messageStats["deliver_get_details"].(map[string]interface{}); ok {
						if rate, ok := deliverDetails["rate"].(float64); ok {
							metrics.Messages.DeliverRate = rate
						}
					}
					if ackDetails, ok := messageStats["ack_details"].(map[string]interface{}); ok {
						if rate, ok := ackDetails["rate"].(float64); ok {
							metrics.Messages.AckRate = rate
						}
					}
				}
				metrics.Messages.TotalMessages = metrics.Queues.Summary.TotalMessages
			}
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(metrics)
	}
}

// handleMetricsSystem handles the system metrics endpoint
func (s *Server) handleMetricsSystem() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		metrics := s.getSystemMetrics()
		
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(metrics)
	}
}

// handleMetricsDB handles the database metrics endpoint
func (s *Server) handleMetricsDB() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
		defer cancel()

		metrics := map[string]interface{}{
			"timestamp": time.Now().Format(time.RFC3339),
			"redis": map[string]interface{}{
				"status": "unknown",
			},
		}

		if s.redisClient != nil {
			redisMetrics := map[string]interface{}{
				"status": "ok",
			}
			
			if err := s.redisClient.Ping(ctx); err != nil {
				redisMetrics["status"] = "error"
				redisMetrics["error"] = err.Error()
			}
			
			metrics["redis"] = redisMetrics
		} else {
			metrics["redis"] = map[string]interface{}{
				"status": "unavailable",
				"error":  "Redis client not initialized",
			}
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(metrics)
	}
}

// getSystemMetrics collects system metrics
func (s *Server) getSystemMetrics() SystemMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return SystemMetrics{
		Timestamp: time.Now().Format(time.RFC3339),
		CPU: CPUMetrics{
			NumCPU: runtime.NumCPU(),
		},
		Memory: MemoryMetrics{
			Alloc:        m.Alloc,
			TotalAlloc:   m.TotalAlloc,
			Sys:          m.Sys,
			Lookups:      m.Lookups,
			Mallocs:      m.Mallocs,
			Frees:        m.Frees,
			HeapAlloc:    m.HeapAlloc,
			HeapSys:      m.HeapSys,
			HeapIdle:     m.HeapIdle,
			HeapInuse:    m.HeapInuse,
			HeapReleased: m.HeapReleased,
			HeapObjects:  m.HeapObjects,
			StackInuse:   m.StackInuse,
			StackSys:     m.StackSys,
		},
		Goroutines: runtime.NumGoroutine(),
		GC: GCMetrics{
			NumGC:         m.NumGC,
			PauseTotal:    m.PauseTotalNs,
			LastPause:     m.PauseNs[(m.NumGC+255)%256],
			NextGC:        m.NextGC,
			EnableGC:      m.EnableGC,
			DebugGC:       m.DebugGC,
			GCCPUFraction: m.GCCPUFraction,
		},
		Runtime: RuntimeMetrics{
			Version:    runtime.Version(),
			GOOS:       runtime.GOOS,
			GOARCH:     runtime.GOARCH,
			Compiler:   runtime.Compiler,
			NumCgoCall: runtime.NumCgoCall(),
		},
		Application: ApplicationMetrics{
			Uptime:  time.Since(startTime).String(),
			Version: "1.0.0",
		},
	}
}

// convertToPrometheusFormat converts metrics to Prometheus format
func (s *Server) convertToPrometheusFormat(metrics SystemMetrics) string {
	return fmt.Sprintf(`# HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.
# TYPE go_memstats_alloc_bytes gauge
go_memstats_alloc_bytes %d

# HELP go_memstats_total_alloc_bytes Total number of bytes allocated, even if freed.
# TYPE go_memstats_total_alloc_bytes counter
go_memstats_total_alloc_bytes %d

# HELP go_memstats_sys_bytes Number of bytes obtained from system.
# TYPE go_memstats_sys_bytes gauge
go_memstats_sys_bytes %d

# HELP go_goroutines Number of goroutines that currently exist.
# TYPE go_goroutines gauge
go_goroutines %d

# HELP go_memstats_gc_total Number of GC runs completed.
# TYPE go_memstats_gc_total counter
go_memstats_gc_total %d

# HELP process_cpu_seconds_total Total user and system CPU time spent in seconds.
# TYPE process_cpu_seconds_total counter
process_cpu_seconds_total %d
`,
		metrics.Memory.Alloc,
		metrics.Memory.TotalAlloc,
		metrics.Memory.Sys,
		metrics.Goroutines,
		metrics.GC.NumGC,
		runtime.NumCPU(),
	)
}
