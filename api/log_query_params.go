package api

import (
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/go-playground/validator/v10"
)

// LogQueryParams represents query parameters for log retrieval
type LogQueryParams struct {
	WorkerType string
	Status     string
	TaskID     string
	IMEI       string
	Error      string
	Timestamp  string
	Date       string // Added for date-based queries
	FromTime   time.Time
	ToTime     time.Time
	Page       int
	PerPage    int
}

// NewLogQueryParams creates a new LogQueryParams with default values
func NewLogQueryParams() LogQueryParams {
	return LogQueryParams{
		Page:    1,
		PerPage: 100,
	}
}

// NewLogQueryParamsFromURL creates a new LogQueryParams from URL query parameters
func NewLogQueryParamsFromURL(query url.Values) (LogQueryParams, error) {
	params := NewLogQueryParams()

	// Parse worker type
	if workerType := query.Get("worker_type"); workerType != "" {
		params.WorkerType = workerType
	}

	// Parse status
	if status := query.Get("status"); status != "" {
		params.Status = status
	}

	// Parse task ID
	if taskID := query.Get("task_id"); taskID != "" {
		params.TaskID = taskID
	}

	// Parse IMEI
	if imei := query.Get("imei"); imei != "" {
		params.IMEI = imei
	}

	// Parse error
	if errorMsg := query.Get("error"); errorMsg != "" {
		params.Error = errorMsg
	}

	// Parse timestamp
	if timestamp := query.Get("timestamp"); timestamp != "" {
		params.Timestamp = timestamp
	}

	// Parse date
	if date := query.Get("date"); date != "" {
		params.Date = date
		// Validate date format
		_, err := time.Parse("2006-01-02", date)
		if err != nil {
			return params, fmt.Errorf("invalid date format: %w", err)
		}
	}

	// Parse from time
	if fromTime := query.Get("from_time"); fromTime != "" {
		t, err := time.Parse(time.RFC3339, fromTime)
		if err != nil {
			return params, fmt.Errorf("invalid from_time format: %w", err)
		}
		params.FromTime = t
	}

	// Parse to time
	if toTime := query.Get("to_time"); toTime != "" {
		t, err := time.Parse(time.RFC3339, toTime)
		if err != nil {
			return params, fmt.Errorf("invalid to_time format: %w", err)
		}
		params.ToTime = t
	}

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return params, fmt.Errorf("invalid page format: %w", err)
		}
		if page < 1 {
			return params, fmt.Errorf("page must be greater than 0")
		}
		params.Page = page
	}

	// Parse per page
	if perPageStr := query.Get("per_page"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return params, fmt.Errorf("invalid per_page format: %w", err)
		}
		if perPage < 1 {
			return params, fmt.Errorf("per_page must be greater than 0")
		}
		if perPage > 1000 {
			return params, fmt.Errorf("per_page must be less than or equal to 1000")
		}
		params.PerPage = perPage
	}

	return params, nil
}

// ValidateLogQueryParams validates the log query parameters using the validator
func ValidateLogQueryParams(v *validator.Validate, params LogQueryParams) error {
	// Create validation struct
	validation := LogQueryParamsValidation{
		WorkerType: params.WorkerType,
		Status:     params.Status,
		TaskID:     params.TaskID,
		IMEI:       params.IMEI,
		Error:      params.Error,
		Timestamp:  params.Timestamp,
		Date:       params.Date,
		FromTime:   params.FromTime.Format(time.RFC3339),
		ToTime:     params.ToTime.Format(time.RFC3339),
		Page:       params.Page,
		PerPage:    params.PerPage,
	}

	// Skip validation for zero time values
	if params.FromTime.IsZero() {
		validation.FromTime = ""
	}
	if params.ToTime.IsZero() {
		validation.ToTime = ""
	}

	return v.Struct(validation)
}
