package api

import (
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
)

// Validator is a struct that holds the validator instance
type Validator struct {
	validate *validator.Validate
}

// NewValidator creates a new validator instance
func NewValidator() *Validator {
	validate := validator.New()

	// Register custom validation for cron expressions
	validate.RegisterValidation("cron6fields", validateCron6Fields)

	return &Validator{
		validate: validate,
	}
}

// validateCron6Fields validates that a cron expression has exactly 6 fields
func validateCron6Fields(fl validator.FieldLevel) bool {
	schedule := fl.Field().String()
	fields := strings.Fields(schedule)
	return len(fields) == 6
}

// JobRequestValidation represents the validation struct for job requests
type JobRequestValidation struct {
	WorkerType  string      `json:"worker_type" validate:"required,oneof=api_pull db_clone api_pull_caveo caveo_payload_maker caveo_to_influx geof_division geof_estate geof_block"`
	Config      interface{} `json:"config" validate:"required"`
	Payload     interface{} `json:"payload,omitempty"`
	Table       string      `json:"table,omitempty"`
	Source      string      `json:"source,omitempty"`
	Destination string      `json:"destination,omitempty"`
	Metadata    interface{} `json:"metadata,omitempty"`
}

// CaveoJobRequestValidation represents the validation struct for Caveo job requests
type CaveoJobRequestValidation struct {
	WorkerType string                   `json:"worker_type" validate:"required,eq=api_pull_caveo"`
	Config     CaveoJobConfigValidation `json:"config" validate:"required"`
}

// CaveoJobConfigValidation represents the validation struct for Caveo job config
type CaveoJobConfigValidation struct {
	Device    CaveoDeviceValidation `json:"device" validate:"required"`
	TimeStart string                `json:"timeStart" validate:"required"`
	TimeEnd   string                `json:"timeEnd" validate:"required"`
}

// CaveoDeviceValidation represents the validation struct for Caveo device
type CaveoDeviceValidation struct {
	PSM      string `json:"psm"`
	Estate   string `json:"estate"`
	Division string `json:"division"`
	Ident    string `json:"ident" validate:"required"`
	NIK      string `json:"nik"`
	Name     string `json:"name"`
	Type     string `json:"type"`
}

// LogEntryValidation represents the validation struct for log entries
type LogEntryValidation struct {
	Timestamp  string                 `json:"timestamp"`
	WorkerType string                 `json:"worker_type" validate:"required"`
	Status     string                 `json:"status" validate:"required"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// LogQueryParamsValidation represents the validation struct for log query parameters
type LogQueryParamsValidation struct {
	WorkerType string `form:"worker_type" validate:"omitempty,oneof=api_pull db_clone api_pull_caveo caveo_payload_maker caveo_to_influx geof_division geof_estate geof_block"`
	Status     string `form:"status" validate:"omitempty"`
	TaskID     string `form:"task_id" validate:"omitempty"`
	IMEI       string `form:"imei" validate:"omitempty"`
	Error      string `form:"error" validate:"omitempty"`
	Timestamp  string `form:"timestamp" validate:"omitempty"`
	Date       string `form:"date" validate:"omitempty,datetime=2006-01-02"`
	FromTime   string `form:"from_time" validate:"omitempty,datetime=2006-01-02T15:04:05Z07:00"`
	ToTime     string `form:"to_time" validate:"omitempty,datetime=2006-01-02T15:04:05Z07:00"`
	Page       int    `form:"page" validate:"omitempty,gt=0"`
	PerPage    int    `form:"per_page" validate:"omitempty,gt=0,lte=1000"`
}

// RescheduleJobRequestValidation represents the validation struct for reschedule job requests
type RescheduleJobRequestValidation struct {
	WorkerType string `json:"worker_type" validate:"required,oneof=api_pull db_clone api_pull_caveo caveo_payload_maker caveo_to_influx geof_division geof_estate geof_block"`
}

// CronjobValidation represents the validation struct for cronjobs
type CronjobValidation struct {
	Name        string                 `json:"name" validate:"required"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Schedule    string                 `json:"schedule" validate:"required,cron6fields"`
	WorkerType  string                 `json:"worker_type" validate:"required,oneof=api_pull db_clone api_pull_caveo caveo_payload_maker caveo_to_influx geof_division geof_estate geof_block"`
	Payload     map[string]interface{} `json:"payload" validate:"required"`
	Variables   map[string]string      `json:"variables"`
	RetryPolicy RetryPolicyValidation  `json:"retry_policy"`
	Timeout     int                    `json:"timeout" validate:"omitempty,gt=0"`
}

// RetryPolicyValidation represents the validation struct for retry policy
type RetryPolicyValidation struct {
	Attempts int `json:"attempts" validate:"omitempty,gte=0"`
	Delay    int `json:"delay" validate:"omitempty,gte=0"` // in seconds
}

// Validate validates a struct using the validator
func (v *Validator) Validate(i interface{}) error {
	return v.validate.Struct(i)
}

// ValidateCronExpression validates a cron expression to ensure it has exactly 6 fields
func ValidateCronExpression(schedule string) error {
	fields := strings.Fields(schedule)
	if len(fields) != 6 {
		return fmt.Errorf("expected exactly 6 fields, found %d: [%s]", len(fields), schedule)
	}
	return nil
}
