package api

import (
	"github.com/user/workers/internal/cronjob"
)

// convertCronjobToResponse converts a cronjob to a response
func convertCronjobToResponse(c *cronjob.Cronjob) CronjobResponse {
	response := CronjobResponse{
		Name:        c.Name,
		Description: c.Description,
		Enabled:     c.Enabled,
		Schedule:    c.Schedule,
		WorkerType:  c.WorkerType,
		Payload:     c.Payload,
		Variables:   c.Variables,
		Timeout:     c.Timeout,
		CreatedAt:   c.CreatedAt,
		UpdatedAt:   c.UpdatedAt,
		LastRun:     c.LastRun,
		NextRun:     c.NextRun,
	}

	// Set retry policy
	response.RetryPolicy.Attempts = c.RetryPolicy.Attempts
	response.RetryPolicy.Delay = c.RetryPolicy.Delay

	return response
}
