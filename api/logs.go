package api

import (
	"time"
)

// Metadata represents the metadata structure in log entries
// Using a map to allow for dynamic key-value pairs
type Metadata map[string]interface{}

// GetMetadata returns a copy of the metadata map
func (l *LogEntry) GetMetadata() Metadata {
	// Create a new map to avoid modifying the original
	metadata := make(Metadata)

	// Copy all key-value pairs from the original metadata
	for k, v := range l.Metadata {
		metadata[k] = v
	}

	return metadata
}

// parseTime parses a time string in various formats
func parseTime(timeStr string) (time.Time, error) {
	// Try different time formats
	formats := []string{
		time.RFC3339,
		"2006-01-02T15:04:05Z",
		"2006-01-02 15:04:05",
		"2006-01-02",
	}

	var parseErr error
	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		} else {
			parseErr = err
		}
	}

	return time.Time{}, parseErr
}
