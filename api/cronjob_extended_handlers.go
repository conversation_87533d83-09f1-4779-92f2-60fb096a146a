package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/mux"
)

// CronjobRequest represents a request to create or update a cronjob
type CronjobRequest struct {
	Name        string            `json:"name"`
	Description string            `json:"description,omitempty"`
	Enabled     bool              `json:"enabled"`
	Schedule    string            `json:"schedule"`
	WorkerType  string            `json:"worker_type"`
	Payload     interface{}       `json:"payload"`
	Variables   map[string]string `json:"variables,omitempty"`
	RetryPolicy struct {
		Attempts int `json:"attempts"`
		Delay    int `json:"delay"`
	} `json:"retry_policy,omitempty"`
	Timeout int `json:"timeout,omitempty"`
}

// CronjobResponseExt is used internally in this file

// isValidCronExpression validates a cron expression
func isValidCronExpression(expr string) bool {
	// Simple validation for demonstration purposes
	// In a real implementation, you would use a proper cron parser
	parts := strings.Fields(expr)
	return len(parts) == 5
}

// CronjobHistoryEntry represents a history entry for a cronjob execution
type CronjobHistoryEntry struct {
	TaskID     string                 `json:"task_id"`
	Timestamp  string                 `json:"timestamp"`
	Status     string                 `json:"status"`
	WorkerType string                 `json:"worker_type"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// CronjobStats represents statistics for cronjobs
type CronjobStats struct {
	TotalCronjobs     int                        `json:"total_cronjobs"`
	EnabledCronjobs   int                        `json:"enabled_cronjobs"`
	DisabledCronjobs  int                        `json:"disabled_cronjobs"`
	ExecutionsByDay   map[string]int             `json:"executions_by_day"`
	SuccessRate       float64                    `json:"success_rate"`
	AverageExecutions float64                    `json:"average_executions"`
	WorkerTypeStats   map[string]WorkerTypeStats `json:"worker_type_stats"`
}

// WorkerTypeStats represents statistics for a specific worker type
type WorkerTypeStats struct {
	TotalCronjobs   int     `json:"total_cronjobs"`
	TotalExecutions int     `json:"total_executions"`
	SuccessRate     float64 `json:"success_rate"`
}

// CronjobTemplate represents a template for creating cronjobs
type CronjobTemplate struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	WorkerType  string                 `json:"worker_type"`
	Schedule    string                 `json:"schedule"`
	Payload     map[string]interface{} `json:"payload"`
	Variables   map[string]string      `json:"variables,omitempty"`
	RetryPolicy struct {
		Attempts int `json:"attempts"`
		Delay    int `json:"delay"`
	} `json:"retry_policy,omitempty"`
	Timeout int `json:"timeout,omitempty"`
}

// handleGetCronjobHistory handles the endpoint to get the execution history of a cronjob
func (s *Server) handleGetCronjobHistory() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Get cronjob
		cronjob, err := s.cronjobManager.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to get cronjob")
			s.sendErrorResponse(w, http.StatusNotFound, "Cronjob not found")
			return
		}

		// Parse query parameters
		startDateStr := r.URL.Query().Get("start_date")
		endDateStr := r.URL.Query().Get("end_date")
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")

		// Set default values
		limit := 10
		offset := 0
		var startDate, endDate time.Time

		// Parse start date
		if startDateStr != "" {
			parsedDate, err := time.Parse("2006-01-02", startDateStr)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid start_date format. Expected YYYY-MM-DD")
				return
			}
			startDate = parsedDate
		} else {
			// Default to 7 days ago
			startDate = time.Now().AddDate(0, 0, -7)
		}

		// Parse end date
		if endDateStr != "" {
			parsedDate, err := time.Parse("2006-01-02", endDateStr)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid end_date format. Expected YYYY-MM-DD")
				return
			}
			endDate = parsedDate
		} else {
			// Default to today
			endDate = time.Now()
		}

		// Parse limit
		if limitStr != "" {
			parsedLimit, err := strconv.Atoi(limitStr)
			if err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		// Parse offset
		if offsetStr != "" {
			parsedOffset, err := strconv.Atoi(offsetStr)
			if err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// Create pattern for Redis keys
		// Format: logs:{worker_type}:*:cronjob-{name}-*
		pattern := fmt.Sprintf("logs:%s:*:cronjob-%s-*", cronjob.WorkerType, name)

		// Get logs from Redis
		logsResponse, err := s.logService.GetLogsByPrefix(pattern)
		logs := logsResponse
		if err != nil {
			s.log.WithError(err).WithField("pattern", pattern).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get cronjob history")
			return
		}

		// Filter logs by date
		var filteredLogs []LogEntry
		for _, log := range logs.Logs {
			// Parse timestamp
			logTime, err := time.Parse(time.RFC3339, log.Timestamp)
			if err != nil {
				continue
			}

			// Check if log is within date range
			if (logTime.After(startDate) || logTime.Equal(startDate)) && (logTime.Before(endDate) || logTime.Equal(endDate)) {
				filteredLogs = append(filteredLogs, log)
			}
		}

		// Apply pagination
		totalLogs := len(filteredLogs)
		var paginatedLogs []LogEntry
		if offset < totalLogs {
			end := offset + limit
			if end > totalLogs {
				end = totalLogs
			}
			paginatedLogs = filteredLogs[offset:end]
		}

		// Convert logs to history entries
		history := make([]CronjobHistoryEntry, 0, len(paginatedLogs))
		for _, log := range paginatedLogs {
			entry := CronjobHistoryEntry{
				Timestamp:  log.Timestamp,
				Status:     log.Status,
				WorkerType: log.WorkerType,
				Metadata:   log.Metadata,
			}

			// Extract task ID from metadata
			if log.Metadata != nil {
				if taskID, ok := log.Metadata["task_id"].(string); ok {
					entry.TaskID = taskID
				}
			}

			history = append(history, entry)
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":     "ok",
			"cronjob":    cronjob.Name,
			"history":    history,
			"total":      totalLogs,
			"limit":      limit,
			"offset":     offset,
			"start_date": startDate.Format("2006-01-02"),
			"end_date":   endDate.Format("2006-01-02"),
		})
	}
}

// handleGetCronjobLogs handles the endpoint to get logs for a specific cronjob
func (s *Server) handleGetCronjobLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Get cronjob
		cronjob, err := s.cronjobManager.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to get cronjob")
			s.sendErrorResponse(w, http.StatusNotFound, "Cronjob not found")
			return
		}

		// Parse query parameters
		statusParam := r.URL.Query().Get("status")
		dateParam := r.URL.Query().Get("date")
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")

		// Set default values
		limit := 10
		offset := 0

		// Parse limit
		if limitStr != "" {
			parsedLimit, err := strconv.Atoi(limitStr)
			if err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		// Parse offset
		if offsetStr != "" {
			parsedOffset, err := strconv.Atoi(offsetStr)
			if err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// Create pattern for Redis keys
		pattern := fmt.Sprintf("logs:%s:*:cronjob-%s-*", cronjob.WorkerType, name)
		if dateParam != "" {
			// Validate date format
			_, err := time.Parse("2006-01-02", dateParam)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid date format. Expected YYYY-MM-DD")
				return
			}
			pattern = fmt.Sprintf("logs:%s:%s:*:cronjob-%s-*", cronjob.WorkerType, dateParam, name)
		}

		// Get logs from Redis
		logsResponse, err := s.logService.GetLogsByPrefix(pattern)
		logs := logsResponse
		if err != nil {
			s.log.WithError(err).WithField("pattern", pattern).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get cronjob logs")
			return
		}

		// Filter logs by status if provided
		var filteredLogs []LogEntry
		if statusParam != "" {
			for _, log := range logs.Logs {
				if log.Status == statusParam {
					filteredLogs = append(filteredLogs, log)
				}
			}
		} else {
			filteredLogs = logs.Logs
		}

		// Apply pagination
		totalLogs := len(filteredLogs)
		var paginatedLogs []LogEntry
		if offset < totalLogs {
			end := offset + limit
			if end > totalLogs {
				end = totalLogs
			}
			paginatedLogs = filteredLogs[offset:end]
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"cronjob": cronjob.Name,
			"logs":    paginatedLogs,
			"total":   totalLogs,
			"limit":   limit,
			"offset":  offset,
		})
	}
}

// handleGetAllCronjobLogs handles the endpoint to get logs for all cronjobs
func (s *Server) handleGetAllCronjobLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Parse query parameters
		workerTypeParam := r.URL.Query().Get("worker_type")
		statusParam := r.URL.Query().Get("status")
		dateParam := r.URL.Query().Get("date")
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")

		// Set default values
		limit := 10
		offset := 0

		// Parse limit
		if limitStr != "" {
			parsedLimit, err := strconv.Atoi(limitStr)
			if err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		// Parse offset
		if offsetStr != "" {
			parsedOffset, err := strconv.Atoi(offsetStr)
			if err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// Create pattern for Redis keys
		pattern := "logs:*:*:cronjob-*"
		if workerTypeParam != "" {
			pattern = fmt.Sprintf("logs:%s:*:cronjob-*", workerTypeParam)
		}
		if dateParam != "" {
			// Validate date format
			_, err := time.Parse("2006-01-02", dateParam)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid date format. Expected YYYY-MM-DD")
				return
			}
			if workerTypeParam != "" {
				pattern = fmt.Sprintf("logs:%s:%s:*:cronjob-*", workerTypeParam, dateParam)
			} else {
				pattern = fmt.Sprintf("logs:*:%s:*:cronjob-*", dateParam)
			}
		}

		// Get logs from Redis
		logsResponse, err := s.logService.GetLogsByPrefix(pattern)
		logs := logsResponse
		if err != nil {
			s.log.WithError(err).WithField("pattern", pattern).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get cronjob logs")
			return
		}

		// Filter logs by status if provided
		var filteredLogs []LogEntry
		if statusParam != "" {
			for _, log := range logs.Logs {
				if log.Status == statusParam {
					filteredLogs = append(filteredLogs, log)
				}
			}
		} else {
			filteredLogs = logs.Logs
		}

		// Apply pagination
		totalLogs := len(filteredLogs)
		var paginatedLogs []LogEntry
		if offset < totalLogs {
			end := offset + limit
			if end > totalLogs {
				end = totalLogs
			}
			paginatedLogs = filteredLogs[offset:end]
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"logs":   paginatedLogs,
			"total":  totalLogs,
			"limit":  limit,
			"offset": offset,
		})
	}
}

// handleGetCronjobStats handles the endpoint to get statistics for all cronjobs
func (s *Server) handleGetCronjobStats() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Parse query parameters
		periodParam := r.URL.Query().Get("period")
		startDateStr := r.URL.Query().Get("start_date")
		endDateStr := r.URL.Query().Get("end_date")

		// Set default values
		period := "daily"
		var startDate, endDate time.Time

		// Validate period
		if periodParam != "" {
			switch periodParam {
			case "daily", "weekly", "monthly":
				period = periodParam
			default:
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid period. Expected daily, weekly, or monthly")
				return
			}
		}

		// Parse start date
		if startDateStr != "" {
			parsedDate, err := time.Parse("2006-01-02", startDateStr)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid start_date format. Expected YYYY-MM-DD")
				return
			}
			startDate = parsedDate
		} else {
			// Default to 30 days ago
			startDate = time.Now().AddDate(0, 0, -30)
		}

		// Parse end date
		if endDateStr != "" {
			parsedDate, err := time.Parse("2006-01-02", endDateStr)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid end_date format. Expected YYYY-MM-DD")
				return
			}
			endDate = parsedDate
		} else {
			// Default to today
			endDate = time.Now()
		}

		// Get all cronjobs
		cronjobs, err := s.cronjobManager.ListCronjobs()
		if err != nil {
			s.log.WithError(err).Error("Failed to list cronjobs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get cronjob statistics")
			return
		}

		// Initialize statistics
		stats := CronjobStats{
			TotalCronjobs:    len(cronjobs),
			EnabledCronjobs:  0,
			DisabledCronjobs: 0,
			ExecutionsByDay:  make(map[string]int),
			WorkerTypeStats:  make(map[string]WorkerTypeStats),
		}

		// Count enabled and disabled cronjobs
		for _, c := range cronjobs {
			if c.Enabled {
				stats.EnabledCronjobs++
			} else {
				stats.DisabledCronjobs++
			}

			// Initialize worker type stats if not exists
			if _, exists := stats.WorkerTypeStats[c.WorkerType]; !exists {
				stats.WorkerTypeStats[c.WorkerType] = WorkerTypeStats{
					TotalCronjobs:   0,
					TotalExecutions: 0,
					SuccessRate:     0,
				}
			}

			// Update worker type stats
			workerStats := stats.WorkerTypeStats[c.WorkerType]
			workerStats.TotalCronjobs++
			stats.WorkerTypeStats[c.WorkerType] = workerStats
		}

		// Get logs for all cronjobs
		pattern := "logs:*:*:cronjob-*"
		logsResponse, err := s.logService.GetLogsByPrefix(pattern)
		logs := logsResponse
		if err != nil {
			s.log.WithError(err).WithField("pattern", pattern).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get cronjob statistics")
			return
		}

		// Process logs
		totalExecutions := 0
		successfulExecutions := 0
		workerTypeExecutions := make(map[string]int)
		workerTypeSuccessful := make(map[string]int)

		for _, log := range logs.Logs {
			// Parse timestamp
			logTime, err := time.Parse(time.RFC3339, log.Timestamp)
			if err != nil {
				continue
			}

			// Check if log is within date range
			if (logTime.After(startDate) || logTime.Equal(startDate)) && (logTime.Before(endDate) || logTime.Equal(endDate)) {
				// Format date based on period
				var dateKey string
				switch period {
				case "daily":
					dateKey = logTime.Format("2006-01-02")
				case "weekly":
					year, week := logTime.ISOWeek()
					dateKey = fmt.Sprintf("%d-W%02d", year, week)
				case "monthly":
					dateKey = logTime.Format("2006-01")
				}

				// Increment executions by day
				stats.ExecutionsByDay[dateKey]++
				totalExecutions++

				// Count successful executions
				if log.Status == "success" {
					successfulExecutions++
				}

				// Update worker type stats
				if _, exists := workerTypeExecutions[log.WorkerType]; !exists {
					workerTypeExecutions[log.WorkerType] = 0
					workerTypeSuccessful[log.WorkerType] = 0
				}
				workerTypeExecutions[log.WorkerType]++
				if log.Status == "success" {
					workerTypeSuccessful[log.WorkerType]++
				}
			}
		}

		// Calculate success rate
		if totalExecutions > 0 {
			stats.SuccessRate = float64(successfulExecutions) / float64(totalExecutions) * 100
		}

		// Calculate average executions per day
		dayCount := endDate.Sub(startDate).Hours() / 24
		if dayCount > 0 {
			stats.AverageExecutions = float64(totalExecutions) / dayCount
		}

		// Update worker type stats
		for workerType, executions := range workerTypeExecutions {
			if workerStats, exists := stats.WorkerTypeStats[workerType]; exists {
				workerStats.TotalExecutions = executions
				if executions > 0 {
					workerStats.SuccessRate = float64(workerTypeSuccessful[workerType]) / float64(executions) * 100
				}
				stats.WorkerTypeStats[workerType] = workerStats
			}
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":     "ok",
			"stats":      stats,
			"period":     period,
			"start_date": startDate.Format("2006-01-02"),
			"end_date":   endDate.Format("2006-01-02"),
		})
	}
}

// handleGetCronjobStatsByName handles the endpoint to get statistics for a specific cronjob
func (s *Server) handleGetCronjobStatsByName() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Get cronjob
		cronjob, err := s.cronjobManager.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to get cronjob")
			s.sendErrorResponse(w, http.StatusNotFound, "Cronjob not found")
			return
		}

		// Parse query parameters
		periodParam := r.URL.Query().Get("period")
		startDateStr := r.URL.Query().Get("start_date")
		endDateStr := r.URL.Query().Get("end_date")

		// Set default values
		period := "daily"
		var startDate, endDate time.Time

		// Validate period
		if periodParam != "" {
			switch periodParam {
			case "daily", "weekly", "monthly":
				period = periodParam
			default:
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid period. Expected daily, weekly, or monthly")
				return
			}
		}

		// Parse start date
		if startDateStr != "" {
			parsedDate, err := time.Parse("2006-01-02", startDateStr)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid start_date format. Expected YYYY-MM-DD")
				return
			}
			startDate = parsedDate
		} else {
			// Default to 30 days ago
			startDate = time.Now().AddDate(0, 0, -30)
		}

		// Parse end date
		if endDateStr != "" {
			parsedDate, err := time.Parse("2006-01-02", endDateStr)
			if err != nil {
				s.sendErrorResponse(w, http.StatusBadRequest, "Invalid end_date format. Expected YYYY-MM-DD")
				return
			}
			endDate = parsedDate
		} else {
			// Default to today
			endDate = time.Now()
		}

		// Create pattern for Redis keys
		pattern := fmt.Sprintf("logs:%s:*:cronjob-%s-*", cronjob.WorkerType, name)
		logsResponse, err := s.logService.GetLogsByPrefix(pattern)
		logs := logsResponse
		if err != nil {
			s.log.WithError(err).WithField("pattern", pattern).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get cronjob statistics")
			return
		}

		// Initialize statistics
		stats := struct {
			TotalExecutions      int            `json:"total_executions"`
			SuccessfulExecutions int            `json:"successful_executions"`
			FailedExecutions     int            `json:"failed_executions"`
			SuccessRate          float64        `json:"success_rate"`
			AverageExecutions    float64        `json:"average_executions"`
			ExecutionsByDay      map[string]int `json:"executions_by_day"`
			LastExecution        string         `json:"last_execution"`
		}{
			ExecutionsByDay: make(map[string]int),
		}

		// Process logs
		totalExecutions := 0
		successfulExecutions := 0
		var lastExecutionTime time.Time

		for _, log := range logs.Logs {
			// Parse timestamp
			logTime, err := time.Parse(time.RFC3339, log.Timestamp)
			if err != nil {
				continue
			}

			// Check if log is within date range
			if (logTime.After(startDate) || logTime.Equal(startDate)) && (logTime.Before(endDate) || logTime.Equal(endDate)) {
				// Format date based on period
				var dateKey string
				switch period {
				case "daily":
					dateKey = logTime.Format("2006-01-02")
				case "weekly":
					year, week := logTime.ISOWeek()
					dateKey = fmt.Sprintf("%d-W%02d", year, week)
				case "monthly":
					dateKey = logTime.Format("2006-01")
				}

				// Increment executions by day
				stats.ExecutionsByDay[dateKey]++
				totalExecutions++

				// Count successful executions
				if log.Status == "success" {
					successfulExecutions++
				}

				// Update last execution time
				if lastExecutionTime.IsZero() || logTime.After(lastExecutionTime) {
					lastExecutionTime = logTime
				}
			}
		}

		// Update statistics
		stats.TotalExecutions = totalExecutions
		stats.SuccessfulExecutions = successfulExecutions
		stats.FailedExecutions = totalExecutions - successfulExecutions
		if totalExecutions > 0 {
			stats.SuccessRate = float64(successfulExecutions) / float64(totalExecutions) * 100
		}

		// Calculate average executions per day
		dayCount := endDate.Sub(startDate).Hours() / 24
		if dayCount > 0 {
			stats.AverageExecutions = float64(totalExecutions) / dayCount
		}

		// Set last execution time
		if !lastExecutionTime.IsZero() {
			stats.LastExecution = lastExecutionTime.Format(time.RFC3339)
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":     "ok",
			"cronjob":    convertCronjobToResponse(cronjob),
			"stats":      stats,
			"period":     period,
			"start_date": startDate.Format("2006-01-02"),
			"end_date":   endDate.Format("2006-01-02"),
		})
	}
}

// handleGetCronjobRescheduleJobs handles the endpoint to get rescheduled jobs
func (s *Server) handleGetCronjobRescheduleJobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Parse query parameters
		// workerTypeParam would be used in a real implementation
		_ = r.URL.Query().Get("worker_type")
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")

		// Set default values
		limit := 10
		offset := 0

		// Parse limit
		if limitStr != "" {
			parsedLimit, err := strconv.Atoi(limitStr)
			if err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		// Parse offset
		if offsetStr != "" {
			parsedOffset, err := strconv.Atoi(offsetStr)
			if err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// In a real implementation, we would use this pattern to query Redis
		// pattern := "re-schedule-jobs:*"
		// if workerTypeParam != "" {
		//     pattern = fmt.Sprintf("re-schedule-jobs:%s:*", workerTypeParam)
		// }

		// For demonstration purposes, we'll return mock data
		// In a real implementation, you would use the Redis client to get the keys
		// and then get the job data for each key

		// Mock data
		mockJobs := []map[string]interface{}{
			{
				"id":          "job-1",
				"worker_type": "api_pull",
				"payload": map[string]interface{}{
					"method": "GET",
					"url":    "https://api.example.com/data",
				},
				"created_at": time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
			},
			{
				"id":          "job-2",
				"worker_type": "api_pull_caveo",
				"payload": map[string]interface{}{
					"method": "POST",
					"url":    "https://api.caveo.com/data",
					"body":   "{\"key\":\"value\"}",
				},
				"created_at": time.Now().Add(-12 * time.Hour).Format(time.RFC3339),
			},
		}

		// Apply pagination
		totalJobs := len(mockJobs)
		var paginatedJobs []map[string]interface{}
		if offset < totalJobs {
			end := offset + limit
			if end > totalJobs {
				end = totalJobs
			}
			paginatedJobs = mockJobs[offset:end]
		}

		// Use the mock jobs
		jobs := paginatedJobs

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"jobs":   jobs,
			"total":  totalJobs,
			"limit":  limit,
			"offset": offset,
		})
	}
}

// handleExecuteCronjobRescheduleJob handles the endpoint to execute a rescheduled job
func (s *Server) handleExecuteCronjobRescheduleJob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client and dispatcher are available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}
		if s.dispatcher == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Dispatcher not available")
			return
		}

		// Get job ID from URL
		vars := mux.Vars(r)
		jobID := vars["id"]

		// For demonstration purposes, we'll use mock data
		// In a real implementation, you would use the Redis client to get the job data
		// and then dispatch the task

		// Check if job exists (mock check)
		if jobID != "job-1" && jobID != "job-2" {
			s.sendErrorResponse(w, http.StatusNotFound, "Rescheduled job not found")
			return
		}

		// Mock successful dispatch
		taskID := "task-" + time.Now().Format("20060102150405")

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Rescheduled job executed successfully",
			"task_id": taskID,
		})
	}
}

// handleDeleteCronjobRescheduleJob handles the endpoint to delete a rescheduled job
func (s *Server) handleDeleteCronjobRescheduleJob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Get job ID from URL
		vars := mux.Vars(r)
		jobID := vars["id"]

		// For demonstration purposes, we'll use mock data
		// In a real implementation, you would use the Redis client to find and delete the job

		// Check if job exists (mock check)
		if jobID != "job-1" && jobID != "job-2" {
			s.sendErrorResponse(w, http.StatusNotFound, "Rescheduled job not found")
			return
		}

		// Mock successful deletion

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Rescheduled job deleted successfully",
		})
	}
}

// handleGetCronjobTemplates handles the endpoint to get all cronjob templates
func (s *Server) handleGetCronjobTemplates() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Define available templates
		templates := []CronjobTemplate{
			{
				Name:        "api_pull_daily",
				Description: "Daily API pull template",
				WorkerType:  "api_pull",
				Schedule:    "0 0 * * *", // Every day at midnight
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.example.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			{
				Name:        "api_pull_hourly",
				Description: "Hourly API pull template",
				WorkerType:  "api_pull",
				Schedule:    "0 * * * *", // Every hour
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.example.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			{
				Name:        "api_pull_caveo_daily",
				Description: "Daily Caveo API pull template",
				WorkerType:  "api_pull_caveo",
				Schedule:    "0 0 * * *", // Every day at midnight
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.caveo.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			{
				Name:        "db_clone_weekly",
				Description: "Weekly database clone template",
				WorkerType:  "db_clone",
				Schedule:    "0 0 * * 0", // Every Sunday at midnight
				Payload: map[string]interface{}{
					"source_db":      "production",
					"destination_db": "backup_${DATE}",
					"tables":         []string{"users", "orders", "products"},
				},
				Variables: map[string]string{
					"DATE": "Current date in YYYY-MM-DD format",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 2,
					Delay:    300,
				},
				Timeout: 3600,
			},
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":    "ok",
			"templates": templates,
		})
	}
}

// handleGetCronjobTemplate handles the endpoint to get a specific cronjob template
func (s *Server) handleGetCronjobTemplate() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get template name from URL
		vars := mux.Vars(r)
		templateName := vars["template_name"]

		// Define available templates
		templates := map[string]CronjobTemplate{
			"api_pull_daily": {
				Name:        "api_pull_daily",
				Description: "Daily API pull template",
				WorkerType:  "api_pull",
				Schedule:    "0 0 * * *", // Every day at midnight
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.example.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			"api_pull_hourly": {
				Name:        "api_pull_hourly",
				Description: "Hourly API pull template",
				WorkerType:  "api_pull",
				Schedule:    "0 * * * *", // Every hour
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.example.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			"api_pull_caveo_daily": {
				Name:        "api_pull_caveo_daily",
				Description: "Daily Caveo API pull template",
				WorkerType:  "api_pull_caveo",
				Schedule:    "0 0 * * *", // Every day at midnight
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.caveo.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			"db_clone_weekly": {
				Name:        "db_clone_weekly",
				Description: "Weekly database clone template",
				WorkerType:  "db_clone",
				Schedule:    "0 0 * * 0", // Every Sunday at midnight
				Payload: map[string]interface{}{
					"source_db":      "production",
					"destination_db": "backup_${DATE}",
					"tables":         []string{"users", "orders", "products"},
				},
				Variables: map[string]string{
					"DATE": "Current date in YYYY-MM-DD format",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 2,
					Delay:    300,
				},
				Timeout: 3600,
			},
		}

		// Check if template exists
		template, exists := templates[templateName]
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, "Template not found")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":   "ok",
			"template": template,
		})
	}
}

// handleCreateCronjobFromTemplate handles the endpoint to create a cronjob from a template
func (s *Server) handleCreateCronjobFromTemplate() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Parse request body
		var request struct {
			TemplateName string                 `json:"template_name"`
			Name         string                 `json:"name"`
			Description  string                 `json:"description,omitempty"`
			Schedule     string                 `json:"schedule,omitempty"`
			Variables    map[string]string      `json:"variables,omitempty"`
			Payload      map[string]interface{} `json:"payload,omitempty"`
		}
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Validate request
		if request.TemplateName == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Template name is required")
			return
		}
		if request.Name == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Cronjob name is required")
			return
		}

		// Define available templates
		templates := map[string]CronjobTemplate{
			"api_pull_daily": {
				Name:        "api_pull_daily",
				Description: "Daily API pull template",
				WorkerType:  "api_pull",
				Schedule:    "0 0 * * *", // Every day at midnight
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.example.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			"api_pull_hourly": {
				Name:        "api_pull_hourly",
				Description: "Hourly API pull template",
				WorkerType:  "api_pull",
				Schedule:    "0 * * * *", // Every hour
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.example.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			"api_pull_caveo_daily": {
				Name:        "api_pull_caveo_daily",
				Description: "Daily Caveo API pull template",
				WorkerType:  "api_pull_caveo",
				Schedule:    "0 0 * * *", // Every day at midnight
				Payload: map[string]interface{}{
					"method":  "GET",
					"url":     "https://api.caveo.com/data",
					"headers": map[string]string{"Authorization": "Bearer ${API_TOKEN}"},
				},
				Variables: map[string]string{
					"API_TOKEN": "Your API token here",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 3,
					Delay:    60,
				},
				Timeout: 300,
			},
			"db_clone_weekly": {
				Name:        "db_clone_weekly",
				Description: "Weekly database clone template",
				WorkerType:  "db_clone",
				Schedule:    "0 0 * * 0", // Every Sunday at midnight
				Payload: map[string]interface{}{
					"source_db":      "production",
					"destination_db": "backup_${DATE}",
					"tables":         []string{"users", "orders", "products"},
				},
				Variables: map[string]string{
					"DATE": "Current date in YYYY-MM-DD format",
				},
				RetryPolicy: struct {
					Attempts int `json:"attempts"`
					Delay    int `json:"delay"`
				}{
					Attempts: 2,
					Delay:    300,
				},
				Timeout: 3600,
			},
		}

		// Check if template exists
		template, exists := templates[request.TemplateName]
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, "Template not found")
			return
		}

		// For demonstration purposes, we'll use mock data
		// In a real implementation, you would create a cronjob.Cronjob struct and save it

		// Create a mock cronjob response
		mockCronjob := map[string]interface{}{
			"name":        request.Name,
			"description": request.Description,
			"enabled":     false, // Disabled by default
			"schedule":    template.Schedule,
			"worker_type": template.WorkerType,
			"payload":     template.Payload,
			"variables":   template.Variables,
			"retry_policy": map[string]interface{}{
				"attempts": template.RetryPolicy.Attempts,
				"delay":    template.RetryPolicy.Delay,
			},
			"timeout":    template.Timeout,
			"created_at": time.Now().Format(time.RFC3339),
			"updated_at": time.Now().Format(time.RFC3339),
			"last_run":   "",
			"next_run":   "",
		}

		// Override template values with request values
		if request.Description != "" {
			mockCronjob["description"] = request.Description
		}
		if request.Schedule != "" {
			mockCronjob["schedule"] = request.Schedule
		}
		if request.Variables != nil {
			// Merge variables
			variables, ok := mockCronjob["variables"].(map[string]string)
			if ok {
				for k, v := range request.Variables {
					variables[k] = v
				}
				mockCronjob["variables"] = variables
			}
		}
		if request.Payload != nil {
			// Merge payload
			payload, ok := mockCronjob["payload"].(map[string]interface{})
			if ok {
				for k, v := range request.Payload {
					payload[k] = v
				}
				mockCronjob["payload"] = payload
			}
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob created successfully",
			"cronjob": mockCronjob,
		})
	}
}

// handleValidateCronjob handles the endpoint to validate a cronjob configuration
func (s *Server) handleValidateCronjob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Parse request body
		var cronjobRequest CronjobRequest
		if err := json.NewDecoder(r.Body).Decode(&cronjobRequest); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Validate request using validator
		payload, ok := cronjobRequest.Payload.(map[string]interface{})
		if !ok {
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid payload format")
			return
		}

		cronjobValidation := CronjobValidation{
			Name:        cronjobRequest.Name,
			Description: cronjobRequest.Description,
			Enabled:     cronjobRequest.Enabled,
			Schedule:    cronjobRequest.Schedule,
			WorkerType:  cronjobRequest.WorkerType,
			Payload:     payload,
			Variables:   cronjobRequest.Variables,
			RetryPolicy: RetryPolicyValidation{
				Attempts: cronjobRequest.RetryPolicy.Attempts,
				Delay:    cronjobRequest.RetryPolicy.Delay,
			},
			Timeout: cronjobRequest.Timeout,
		}

		if err := s.validator.Validate(cronjobValidation); err != nil {
			s.log.WithError(err).WithField("name", cronjobRequest.Name).Error("Invalid cronjob request")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Validate cron expression to ensure it has exactly 6 fields
		if err := ValidateCronExpression(cronjobRequest.Schedule); err != nil {
			s.log.WithError(err).WithField("name", cronjobRequest.Name).Error("Invalid cron expression")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Invalid cron expression: %s", err.Error()))
			return
		}

		// Validate schedule format (simple validation for demonstration)
		if !isValidCronExpression(cronjobRequest.Schedule) {
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid schedule format")
			return
		}

		// Validate payload based on worker type
		var payloadValidationError string
		switch cronjobRequest.WorkerType {
		case "api_pull", "api_pull_caveo":
			// Validate API pull payload
			payload, ok := cronjobRequest.Payload.(map[string]interface{})
			if !ok {
				payloadValidationError = "Invalid payload format for API pull worker"
				break
			}

			// Check required fields
			if _, exists := payload["method"]; !exists {
				payloadValidationError = "Missing required field 'method' in payload"
				break
			}
			if _, exists := payload["url"]; !exists {
				payloadValidationError = "Missing required field 'url' in payload"
				break
			}

			// Validate method
			method, ok := payload["method"].(string)
			if !ok {
				payloadValidationError = "Field 'method' must be a string"
				break
			}
			if method != "GET" && method != "POST" && method != "PUT" && method != "DELETE" {
				payloadValidationError = fmt.Sprintf("Invalid HTTP method: %s", method)
				break
			}

			// Validate URL
			url, ok := payload["url"].(string)
			if !ok {
				payloadValidationError = "Field 'url' must be a string"
				break
			}
			if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
				payloadValidationError = "URL must start with http:// or https://"
				break
			}

		case "db_clone":
			// Validate DB clone payload
			payload, ok := cronjobRequest.Payload.(map[string]interface{})
			if !ok {
				payloadValidationError = "Invalid payload format for DB clone worker"
				break
			}

			// Check required fields
			if _, exists := payload["source_db"]; !exists {
				payloadValidationError = "Missing required field 'source_db' in payload"
				break
			}
			if _, exists := payload["destination_db"]; !exists {
				payloadValidationError = "Missing required field 'destination_db' in payload"
				break
			}
			if _, exists := payload["tables"]; !exists {
				payloadValidationError = "Missing required field 'tables' in payload"
				break
			}

			// Validate tables
			tables, ok := payload["tables"].([]interface{})
			if !ok {
				payloadValidationError = "Field 'tables' must be an array"
				break
			}
			if len(tables) == 0 {
				payloadValidationError = "Field 'tables' cannot be empty"
				break
			}
		}

		if payloadValidationError != "" {
			s.sendErrorResponse(w, http.StatusBadRequest, payloadValidationError)
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob configuration is valid",
			"cronjob": cronjobRequest,
		})
	}
}
