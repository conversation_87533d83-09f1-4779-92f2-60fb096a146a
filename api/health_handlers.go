package api

import (
	"context"
	"encoding/json"
	"net/http"
	"time"
)

// HealthStatus represents the status of a service
type HealthStatus struct {
	Status string `json:"status"`
	Error  string `json:"error,omitempty"`
}

// HealthResponse represents the overall health response
type HealthResponse struct {
	Status   string                  `json:"status"`
	Time     string                  `json:"time"`
	Services map[string]HealthStatus `json:"services"`
	Version  string                  `json:"version"`
}

// handleHealthRabbitMQ handles the RabbitMQ health check endpoint
func (s *Server) handleHealthRabbitMQ() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
		defer cancel()

		status := "ok"
		errorMsg := ""

		// Test RabbitMQ Management API connection
		if s.rabbitMQClient != nil {
			if err := s.rabbitMQClient.TestConnection(ctx); err != nil {
				status = "error"
				errorMsg = err.Error()
			}
		} else {
			status = "unavailable"
			errorMsg = "RabbitMQ Management client not initialized"
		}

		response := HealthStatus{
			Status: status,
			Error:  errorMsg,
		}

		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		if status == "ok" {
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusServiceUnavailable)
		}
		json.NewEncoder(w).Encode(response)
	}
}

// handleHealthDatabase handles the database health check endpoint
func (s *Server) handleHealthDatabase() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
		defer cancel()

		status := "ok"
		errorMsg := ""

		// Check Redis connection
		if s.redisClient == nil {
			status = "unavailable"
			errorMsg = "Redis client not initialized"
		} else {
			if err := s.redisClient.Ping(ctx); err != nil {
				status = "error"
				errorMsg = err.Error()
			}
		}

		response := HealthStatus{
			Status: status,
			Error:  errorMsg,
		}

		w.Header().Set("Content-Type", "application/json")
		if status == "ok" {
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusServiceUnavailable)
		}
		json.NewEncoder(w).Encode(response)
	}
}

// handleHealthDependencies handles the dependencies health check endpoint
func (s *Server) handleHealthDependencies() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		services := make(map[string]HealthStatus)
		overallStatus := "ok"

		// Check Redis
		redisStatus := "ok"
		redisError := ""
		if s.redisClient == nil {
			redisStatus = "unavailable"
			redisError = "Redis client not initialized"
			overallStatus = "degraded"
		} else {
			if err := s.redisClient.Ping(ctx); err != nil {
				redisStatus = "error"
				redisError = err.Error()
				overallStatus = "degraded"
			}
		}
		services["redis"] = HealthStatus{Status: redisStatus, Error: redisError}

		// Check RabbitMQ
		rabbitMQStatus := "ok"
		rabbitMQError := ""
		if s.rabbitMQClient != nil {
			if err := s.rabbitMQClient.TestConnection(ctx); err != nil {
				rabbitMQStatus = "error"
				rabbitMQError = err.Error()
				overallStatus = "degraded"
			}
		} else {
			rabbitMQStatus = "unavailable"
			rabbitMQError = "RabbitMQ Management client not initialized"
			overallStatus = "degraded"
		}
		services["rabbitmq"] = HealthStatus{Status: rabbitMQStatus, Error: rabbitMQError}

		// Check Minio
		minioStatus := "not_configured"
		minioError := ""
		if s.minioClient != nil {
			// Simple check - try to list buckets
			_, err := s.minioClient.ListBuckets(ctx)
			if err != nil {
				minioStatus = "error"
				minioError = err.Error()
				overallStatus = "degraded"
			} else {
				minioStatus = "ok"
			}
		}
		services["minio"] = HealthStatus{Status: minioStatus, Error: minioError}

		// Check Cronjob Manager
		cronjobStatus := "not_configured"
		cronjobError := ""
		if s.cronjobManager != nil {
			cronjobStatus = "ok"
		}
		services["cronjob"] = HealthStatus{Status: cronjobStatus, Error: cronjobError}

		response := HealthResponse{
			Status:   overallStatus,
			Time:     time.Now().Format(time.RFC3339),
			Services: services,
			Version:  "1.0.0",
		}

		w.Header().Set("Content-Type", "application/json")
		if overallStatus == "ok" {
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusServiceUnavailable)
		}
		json.NewEncoder(w).Encode(response)
	}
}
