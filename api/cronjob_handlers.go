package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/user/workers/internal/cronjob"
)

// CronjobResponse represents a response for a cronjob
type CronjobResponse struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Schedule    string                 `json:"schedule"`
	WorkerType  string                 `json:"worker_type"`
	Payload     map[string]interface{} `json:"payload"`
	Variables   map[string]string      `json:"variables,omitempty"`
	RetryPolicy struct {
		Attempts int `json:"attempts"`
		Delay    int `json:"delay"`
	} `json:"retry_policy,omitempty"`
	Timeout   int       `json:"timeout,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	LastRun   time.Time `json:"last_run,omitempty"`
	NextRun   time.Time `json:"next_run,omitempty"`
}

// handleListCronjobs handles the endpoint to list all cronjobs
func (s *Server) handleListCronjobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get all cronjobs
		cronjobs, err := s.cronjobManager.ListCronjobs()
		if err != nil {
			s.log.WithError(err).Error("Failed to list cronjobs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to list cronjobs")
			return
		}

		// Convert to response format
		response := make([]CronjobResponse, 0, len(cronjobs))
		for _, c := range cronjobs {
			response = append(response, convertCronjobToResponse(c))
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":   "ok",
			"cronjobs": response,
			"count":    len(response),
		})
	}
}

// handleGetCronjob handles the endpoint to get a specific cronjob
func (s *Server) handleGetCronjob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Get cronjob
		cronjob, err := s.cronjobManager.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to get cronjob")
			s.sendErrorResponse(w, http.StatusNotFound, "Cronjob not found")
			return
		}

		// Convert to response format
		response := convertCronjobToResponse(cronjob)

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"cronjob": response,
		})
	}
}

// handleCreateCronjob handles the endpoint to create a new cronjob
func (s *Server) handleCreateCronjob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Parse request body
		var request cronjob.Cronjob
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Validate cron expression to ensure it has exactly 6 fields
		if err := ValidateCronExpression(request.Schedule); err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Invalid cron expression")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Invalid cron expression: %s", err.Error()))
			return
		}

		// Validate request using validator
		cronjobValidation := CronjobValidation{
			Name:        request.Name,
			Description: request.Description,
			Enabled:     request.Enabled,
			Schedule:    request.Schedule,
			WorkerType:  request.WorkerType,
			Payload:     request.Payload,
			Variables:   request.Variables,
			RetryPolicy: RetryPolicyValidation{
				Attempts: request.RetryPolicy.Attempts,
				Delay:    request.RetryPolicy.Delay,
			},
			Timeout: request.Timeout,
		}

		if err := s.validator.Validate(cronjobValidation); err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Invalid cronjob request")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Create cronjob
		if err := s.cronjobManager.CreateCronjob(&request); err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Failed to create cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to create cronjob")
			return
		}

		// Get created cronjob
		createdCronjob, err := s.cronjobManager.GetCronjob(request.Name)
		if err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Failed to get created cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get created cronjob")
			return
		}

		// Convert to response format
		response := convertCronjobToResponse(createdCronjob)

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob created successfully",
			"cronjob": response,
		})
	}
}

// handleUpdateCronjob handles the endpoint to update a cronjob
func (s *Server) handleUpdateCronjob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Parse request body
		var request cronjob.Cronjob
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Ensure name in URL matches name in request
		request.Name = name

		// Validate cron expression to ensure it has exactly 6 fields
		if err := ValidateCronExpression(request.Schedule); err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Invalid cron expression")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Invalid cron expression: %s", err.Error()))
			return
		}

		// Validate request using validator
		cronjobValidation := CronjobValidation{
			Name:        request.Name,
			Description: request.Description,
			Enabled:     request.Enabled,
			Schedule:    request.Schedule,
			WorkerType:  request.WorkerType,
			Payload:     request.Payload,
			Variables:   request.Variables,
			RetryPolicy: RetryPolicyValidation{
				Attempts: request.RetryPolicy.Attempts,
				Delay:    request.RetryPolicy.Delay,
			},
			Timeout: request.Timeout,
		}

		if err := s.validator.Validate(cronjobValidation); err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Invalid cronjob request")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Update cronjob
		if err := s.cronjobManager.UpdateCronjob(&request); err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to update cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to update cronjob")
			return
		}

		// Get updated cronjob
		updatedCronjob, err := s.cronjobManager.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to get updated cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get updated cronjob")
			return
		}

		// Convert to response format
		response := convertCronjobToResponse(updatedCronjob)

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob updated successfully",
			"cronjob": response,
		})
	}
}

// handleDeleteCronjob handles the endpoint to delete a cronjob
func (s *Server) handleDeleteCronjob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Delete cronjob
		if err := s.cronjobManager.DeleteCronjob(name); err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to delete cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete cronjob")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob deleted successfully",
		})
	}
}

// handleEnableCronjob handles the endpoint to enable a cronjob
func (s *Server) handleEnableCronjob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Enable cronjob
		if err := s.cronjobManager.EnableCronjob(name); err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to enable cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to enable cronjob")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob enabled successfully",
		})
	}
}

// handleDisableCronjob handles the endpoint to disable a cronjob
func (s *Server) handleDisableCronjob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Disable cronjob
		if err := s.cronjobManager.DisableCronjob(name); err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to disable cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to disable cronjob")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob disabled successfully",
		})
	}
}

// handleExecuteCronjobNow handles the endpoint to execute a cronjob immediately
func (s *Server) handleExecuteCronjobNow() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Execute cronjob
		if err := s.cronjobManager.ExecuteCronjobNow(name); err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to execute cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to execute cronjob")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "Cronjob execution triggered successfully",
		})
	}
}
