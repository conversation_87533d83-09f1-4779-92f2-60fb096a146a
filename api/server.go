package api

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/invopop/jsonschema"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/redis/go-redis/v9"
	"github.com/rs/cors"
	"github.com/user/workers/config"
	"github.com/user/workers/internal/cronjob"
	"github.com/user/workers/internal/dispatcher"
	"github.com/user/workers/internal/worker"
	"github.com/user/workers/pkg/influxdb"
	"github.com/user/workers/pkg/logger"
	customRedis "github.com/user/workers/pkg/redis"
)

// Server represents the API server
type Server struct {
	router      *mux.Router
	server      *http.Server
	dispatcher  *dispatcher.Dispatcher
	log         *logger.Logger
	logService  *LogService
	redisClient *customRedis.Client
	minioClient *minio.Client
	minioBucket string
	// Map of available buckets for different worker types
	minioBuckets   map[string]string
	cronjobManager *cronjob.Manager
	validator      *Validator
	rabbitMQClient *RabbitMQManagementClient
	influxClient   *influxdb.Client
}

// NewServer creates a new API server
func NewServer(dispatcher *dispatcher.Dispatcher, log *logger.Logger, cfg *config.Config) *Server {
	router := mux.NewRouter()

	// Create Redis client
	redisClient, err := customRedis.New(customRedis.Config{
		Host:     cfg.Redis.Host,
		Port:     cfg.Redis.Port,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})
	if err != nil {
		log.WithError(err).Warn("Failed to connect to Redis, some features may not work")
	}

	// Create log service with Redis
	logService := NewLogService(customRedis.Config{
		Host:     cfg.Redis.Host,
		Port:     cfg.Redis.Port,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	}, log)

	// Initialize Minio client
	var minioClient *minio.Client
	var minioBucket string
	minioBuckets := make(map[string]string)

	if cfg.Minio.Endpoint != "" {
		var err error
		minioClient, err = minio.New(cfg.Minio.Endpoint, &minio.Options{
			Creds:  credentials.NewStaticV4(cfg.Minio.AccessKey, cfg.Minio.SecretKey, ""),
			Secure: cfg.Minio.UseSSL,
		})
		if err != nil {
			log.WithError(err).Warn("Failed to create Minio client, file operations will not be available")
		} else {
			// Set default bucket
			minioBucket = cfg.Minio.Bucket
			if minioBucket == "" {
				minioBucket = "api-pull-files" // Default bucket
			}

			// Initialize bucket map with known worker buckets
			minioBuckets["api_pull"] = "api-pull-files"
			minioBuckets["api_pull_caveo"] = "api-pull-caveo-files"
			minioBuckets["db_clone"] = "db-clone-files"
			minioBuckets["caveo_payload_maker"] = "caveo-payload-maker-files"

			// Override with config if provided
			if cfg.Minio.Bucket != "" {
				minioBuckets["default"] = cfg.Minio.Bucket
			}

			// Check and create buckets if they don't exist
			for workerType, bucketName := range minioBuckets {
				exists, err := minioClient.BucketExists(context.Background(), bucketName)
				if err != nil {
					log.WithError(err).WithField("bucket", bucketName).Warn("Failed to check if bucket exists, file operations may not work")
				} else if !exists {
					// Create bucket if it doesn't exist
					err = minioClient.MakeBucket(context.Background(), bucketName, minio.MakeBucketOptions{})
					if err != nil {
						log.WithError(err).WithField("bucket", bucketName).Warn("Failed to create bucket, file operations may not work")
					} else {
						log.WithFields(map[string]interface{}{
							"bucket":      bucketName,
							"worker_type": workerType,
						}).Info("Created Minio bucket")
					}
				}
			}
		}
	}

	// Create validator
	validator := NewValidator()

	// Create RabbitMQ Management client
	var rabbitMQClient *RabbitMQManagementClient
	if cfg.RabbitMQ.ManagementPort != "" {
		rabbitMQClient = NewRabbitMQManagementClient(cfg, log)
		log.Info("RabbitMQ Management client initialized")
	} else {
		log.Warn("RabbitMQ Management port not configured, monitoring features will be limited")
	}

	// Create InfluxDB client
	var influxClient *influxdb.Client
	if cfg.InfluxDB.URL != "" && cfg.InfluxDB.Token != "" && cfg.InfluxDB.Org != "" {
		var err error
		influxClient, err = influxdb.NewInfluxDBClient(cfg.InfluxDB.URL, cfg.InfluxDB.Token, cfg.InfluxDB.Org)
		if err != nil {
			log.WithError(err).Warn("Failed to create InfluxDB client, InfluxDB features will not be available")
		} else {
			log.Info("InfluxDB client initialized")
		}
	} else {
		log.Warn("InfluxDB configuration not complete, InfluxDB features will not be available")
	}

	// Create server instance
	server := &Server{
		router:         router,
		dispatcher:     dispatcher,
		log:            log,
		logService:     logService,
		redisClient:    redisClient,
		minioClient:    minioClient,
		minioBucket:    minioBucket,
		minioBuckets:   minioBuckets,
		validator:      validator,
		rabbitMQClient: rabbitMQClient,
		influxClient:   influxClient,
	}

	// Initialize cronjob manager if Redis is available
	if redisClient != nil {
		// Create Redis client for cronjob storage
		redisOptions := &redis.Options{
			Addr:     fmt.Sprintf("%s:%s", cfg.Redis.Host, cfg.Redis.Port),
			Password: cfg.Redis.Password,
			DB:       cfg.Redis.DB,
		}
		redisClientForCronjob := redis.NewClient(redisOptions)

		// Create cronjob storage
		cronjobStorage := cronjob.NewRedisStorage(redisClientForCronjob, log)

		// Create cronjob manager
		server.cronjobManager = cronjob.NewManager(cronjobStorage, dispatcher, log)

		// Start cronjob manager
		if err := server.cronjobManager.Start(); err != nil {
			log.WithError(err).Warn("Failed to start cronjob manager, cronjob functionality will not be available")
		} else {
			log.Info("Cronjob manager started successfully")
		}
	} else {
		log.Warn("Redis is not available, cronjob functionality will not be available")
	}

	// Register routes
	server.registerRoutes()

	return server
}

// registerRoutes registers all API routes
func (s *Server) registerRoutes() {
	// API routes
	s.router.HandleFunc("/api/health", s.handleHealth()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/jobs", s.handleSubmitJob()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/jobs", s.handleGetJobs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/jobs/{id}", s.handleGetJob()).Methods(http.MethodGet, http.MethodOptions)

	// Standard Log CRUD Operations
	s.router.HandleFunc("/api/logs", s.handleGetAllLogs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs", s.handleCreateLog()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/logs/{date}/{status}/{task_id}", s.handleGetLogByDateStatusTaskID()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/{date}/{status}/{task_id}", s.handleUpdateLog()).Methods(http.MethodPut, http.MethodOptions)
	s.router.HandleFunc("/api/logs/{date}/{status}/{task_id}", s.handleDeleteLog()).Methods(http.MethodDelete, http.MethodOptions)

	// Log Query & Search
	s.router.HandleFunc("/api/logs/search", s.handleSearchLogs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/recent", s.handleGetRecentLogs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/errors", s.handleGetLogsByStatusCode()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/by-endpoint", s.handleGetLogsByURL()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/worker_type/{worker_type}", s.handleGetLogsByWorkerType()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/imei/{imei}", s.handleGetLogsByIMEI()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/error/{error}", s.handleGetLogsByError()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/timestamp/{timestamp}", s.handleGetLogsByTimestamp()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/key/{key:.*}", s.handleGetLogByKey()).Methods(http.MethodGet, http.MethodOptions)

	// Additional Log Routes (as requested)
	s.router.HandleFunc("/api/logs/date/{date}", s.handleGetLogsByDate()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/status/{status}", s.handleGetLogsByStatus()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/task/{task_id}", s.handleGetLogsByTaskID()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/{date}", s.handleGetLogsByDate()).Methods(http.MethodGet, http.MethodOptions)

	// Bulk & Wildcard Operations
	s.router.HandleFunc("/api/logs/bulk", s.handleBulkDeleteLogs()).Methods(http.MethodDelete, http.MethodOptions)
	s.router.HandleFunc("/api/logs/prefix", s.handleGetLogsByPrefix()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/batch", s.handleBatchCreateLogs()).Methods(http.MethodPost, http.MethodOptions)

	// Log Analytics & Grouping
	s.router.HandleFunc("/api/logs/stats", s.handleGetLogStats()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/group-by", s.handleGroupLogsByField()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/count-by-status", s.handleCountLogsByStatus()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/logs/status-code-distribution", s.handleGetStatusCodeDistribution()).Methods(http.MethodGet, http.MethodOptions)

	// Minio File Operations
	s.router.HandleFunc("/api/files", s.handleListFiles()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/files/{bucket}", s.handleListFilesByBucket()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/files/{bucket}/{folder}", s.handleListFilesByFolder()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/files/{bucket}/{folder}/{subfolder}", s.handleListFilesBySubfolder()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/files/download/{bucket}/{path:.*}", s.handleDownloadFile()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/files/bulk/{bucket}/{folder-path:.*}", s.handleDeleteMultipleFiles()).Methods(http.MethodDelete, http.MethodOptions)
	s.router.HandleFunc("/api/files/{bucket}/{path:.*}", s.handleDeleteFile()).Methods(http.MethodDelete, http.MethodOptions)

	// API Pull Caveo Routes
	s.router.HandleFunc("/api/caveo/jobs", s.handleSubmitCaveoJob()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/caveo/jobs/{id}", s.handleGetCaveoJob()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/caveo/logs", s.handleGetCaveoLogs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/caveo/logs/{imei}", s.handleGetCaveoLogsByIMEI()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/caveo/logs/{date}/{imei}", s.handleGetCaveoLogsByDateAndIMEI()).Methods(http.MethodGet, http.MethodOptions)

	// Re-schedule Jobs Routes
	s.router.HandleFunc("/api/jobs/reschedule", s.handleGetRescheduleJobs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/jobs/reschedule/{worker_type}", s.handleGetRescheduleJobsByWorkerType()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/jobs/reschedule", s.handleTriggerRescheduleJobs()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/jobs/reschedule/{worker_type}", s.handleTriggerRescheduleJobsByWorkerType()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/jobs/reschedule", s.handleDeleteRescheduleJobs()).Methods(http.MethodDelete, http.MethodOptions)
	s.router.HandleFunc("/api/jobs/reschedule/{worker_type}", s.handleDeleteRescheduleJobsByWorkerType()).Methods(http.MethodDelete, http.MethodOptions)

	// Reschedule Caveo Worker Routes
	s.router.HandleFunc("/api/reschedule-caveo/trigger", s.handleTriggerRescheduleCaveoWorker()).Methods(http.MethodPost, http.MethodOptions)

	// Not-Comply Jobs Routes
	s.router.HandleFunc("/api/not-comply-jobs", s.handleGetNotComplyJobs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/not-comply-jobs/{worker_type}", s.handleGetNotComplyJobsByWorkerType()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/not-comply-jobs/{worker_type}/{date}", s.handleGetNotComplyJobsByWorkerTypeAndDate()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/not-comply-jobs/{worker_type}/{date}/{imei}", s.handleGetNotComplyJobByWorkerTypeDateAndIMEI()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/not-comply-jobs", s.handleDeleteNotComplyJobs()).Methods(http.MethodDelete, http.MethodOptions)
	s.router.HandleFunc("/api/not-comply-jobs/{worker_type}", s.handleDeleteNotComplyJobsByWorkerType()).Methods(http.MethodDelete, http.MethodOptions)
	s.router.HandleFunc("/api/not-comply-jobs/{worker_type}/{date}/{imei}", s.handleDeleteNotComplyJobByWorkerTypeDateAndIMEI()).Methods(http.MethodDelete, http.MethodOptions)

	// Legacy routes (for backward compatibility)
	s.router.HandleFunc("/api/logs/reload", s.handleReloadLogs()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/redis/delete", s.handleDeleteRedisData()).Methods(http.MethodDelete, http.MethodOptions)

	// Healthcheck / Utility
	s.router.HandleFunc("/api/stats", s.handleGetSystemStats()).Methods(http.MethodGet, http.MethodOptions)

	// Worker Type Routes
	s.router.HandleFunc("/api/worker-types", s.handleListWorkerTypes()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/worker-types/{worker_type}/schema", s.handleGetWorkerTypeSchema()).Methods(http.MethodGet, http.MethodOptions)

	// Cronjob Routes - Basic Management
	s.router.HandleFunc("/api/cronjobs", s.handleListCronjobs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs", s.handleCreateCronjob()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}", s.handleGetCronjob()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}", s.handleUpdateCronjob()).Methods(http.MethodPut, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}", s.handleDeleteCronjob()).Methods(http.MethodDelete, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}/enable", s.handleEnableCronjob()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}/disable", s.handleDisableCronjob()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}/execute", s.handleExecuteCronjobNow()).Methods(http.MethodPost, http.MethodOptions)

	// Cronjob Routes - History and Monitoring
	s.router.HandleFunc("/api/cronjobs/{name}/history", s.handleGetCronjobHistory()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}/logs", s.handleGetCronjobLogs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/logs", s.handleGetAllCronjobLogs()).Methods(http.MethodGet, http.MethodOptions)

	// Cronjob Routes - Statistics and Analytics
	s.router.HandleFunc("/api/cronjobs/stats", s.handleGetCronjobStats()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}/stats", s.handleGetCronjobStatsByName()).Methods(http.MethodGet, http.MethodOptions)

	// Cronjob Routes - Reschedule Management
	s.router.HandleFunc("/api/cronjobs/reschedule", s.handleGetCronjobRescheduleJobs()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/reschedule/{id}/execute", s.handleExecuteCronjobRescheduleJob()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/reschedule/{id}", s.handleDeleteCronjobRescheduleJob()).Methods(http.MethodDelete, http.MethodOptions)

	// Cronjob Routes - Template Management
	s.router.HandleFunc("/api/cronjobs/templates", s.handleGetCronjobTemplates()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/templates/{template_name}", s.handleGetCronjobTemplate()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/from-template", s.handleCreateCronjobFromTemplate()).Methods(http.MethodPost, http.MethodOptions)

	// Cronjob Routes - Validation
	s.router.HandleFunc("/api/cronjobs/validate", s.handleValidateCronjob()).Methods(http.MethodPost, http.MethodOptions)

	// Cronjob Routes - Worker Management
	s.router.HandleFunc("/api/cronjobs/refresh", s.handleRefreshCronjobWorker()).Methods(http.MethodPost, http.MethodOptions)
	s.router.HandleFunc("/api/cronjobs/{name}/update-and-refresh", s.handleUpdateCronjobAndRefreshWorker()).Methods(http.MethodPut, http.MethodOptions)

	// Health Check Routes
	s.router.HandleFunc("/health/rabbitmq", s.handleHealthRabbitMQ()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/health/database", s.handleHealthDatabase()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/health/dependencies", s.handleHealthDependencies()).Methods(http.MethodGet, http.MethodOptions)

	// Metrics Routes
	s.router.HandleFunc("/metrics", s.handleMetrics()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/metrics/rabbitmq", s.handleMetricsRabbitMQ()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/metrics/system", s.handleMetricsSystem()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/metrics/db", s.handleMetricsDB()).Methods(http.MethodGet, http.MethodOptions)

	// RabbitMQ Monitoring Routes
	s.router.HandleFunc("/rabbitmq/overview", s.handleRabbitMQOverview()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/queues", s.handleRabbitMQQueues()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/queues/{name}", s.handleRabbitMQQueue()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/exchanges", s.handleRabbitMQExchanges()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/exchanges/{name}", s.handleRabbitMQExchange()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/bindings", s.handleRabbitMQBindings()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/connections", s.handleRabbitMQConnections()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/channels", s.handleRabbitMQChannels()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/consumers", s.handleRabbitMQConsumers()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/rabbitmq/messages/stats", s.handleRabbitMQMessageStats()).Methods(http.MethodGet, http.MethodOptions)

	// InfluxDB Routes
	s.router.HandleFunc("/api/influxdb/count/{imei}", s.handleGetInfluxDBCountByIMEIAndDateRange()).Methods(http.MethodGet, http.MethodOptions)
	s.router.HandleFunc("/api/influxdb/tracking/{imei}/{date}", s.handleGetInfluxDBTrackingByIMEIAndDate()).Methods(http.MethodGet, http.MethodOptions)
}

// Start starts the API server
func (s *Server) Start(port int) error {
	// Create CORS handler
	corsHandler := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"}, // Allow all origins
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	// Create HTTP server
	s.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		Handler:      corsHandler.Handler(s.router),
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server
	s.log.WithField("port", port).Info("Starting API server")
	return s.server.ListenAndServe()
}

// Shutdown gracefully shuts down the API server
func (s *Server) Shutdown(ctx context.Context) error {
	s.log.Info("Shutting down API server")

	// Stop cronjob manager if it exists
	if s.cronjobManager != nil {
		s.log.Info("Stopping cronjob manager")
		s.cronjobManager.Stop()
	}

	// Close Redis client if it exists
	if s.redisClient != nil {
		if err := s.redisClient.Close(); err != nil {
			s.log.WithError(err).Warn("Failed to close Redis client")
		}
	}

	return s.server.Shutdown(ctx)
}

// handleHealth handles the health check endpoint
func (s *Server) handleHealth() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Create context with timeout for all health checks
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		// Check Redis connection
		redisStatus := "ok"
		redisError := ""
		if s.redisClient == nil {
			redisStatus = "unavailable"
			redisError = "Redis client not initialized"
		} else {
			if err := s.redisClient.Ping(ctx); err != nil {
				redisStatus = "error"
				redisError = err.Error()
			}
		}

		// Check RabbitMQ connection
		rabbitMQStatus := "not_configured"
		rabbitMQError := ""
		if s.rabbitMQClient != nil {
			if err := s.rabbitMQClient.TestConnection(ctx); err != nil {
				rabbitMQStatus = "error"
				rabbitMQError = err.Error()
			} else {
				rabbitMQStatus = "ok"
			}
		}

		// Check Minio connection (if available)
		minioStatus := "not_configured"
		minioError := ""
		if s.minioClient != nil {
			// Simple check - try to list buckets
			_, err := s.minioClient.ListBuckets(ctx)
			if err != nil {
				minioStatus = "error"
				minioError = err.Error()
			} else {
				minioStatus = "ok"
			}
		}

		// Check cronjob manager status
		cronjobStatus := "not_configured"
		cronjobError := ""
		if s.cronjobManager != nil {
			cronjobStatus = "ok"
		}

		// Create response
		response := map[string]interface{}{
			"status": "ok",
			"time":   time.Now().Format(time.RFC3339),
			"services": map[string]interface{}{
				"redis": map[string]string{
					"status": redisStatus,
					"error":  redisError,
				},
				"rabbitmq": map[string]string{
					"status": rabbitMQStatus,
					"error":  rabbitMQError,
				},
				"minio": map[string]string{
					"status": minioStatus,
					"error":  minioError,
				},
				"cronjob": map[string]string{
					"status": cronjobStatus,
					"error":  cronjobError,
				},
			},
			"version": "1.0.0", // Add version information
		}

		// Set overall status based on service statuses
		if redisStatus != "ok" || rabbitMQStatus == "error" || minioStatus == "error" || (cronjobStatus != "ok" && cronjobStatus != "not_configured") {
			response["status"] = "degraded"
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleSubmitJob handles the job submission endpoint
func (s *Server) handleSubmitJob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Parse request body
		var jobRequest JobRequest
		if err := json.NewDecoder(r.Body).Decode(&jobRequest); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Validate request using validator
		jobRequestValidation := JobRequestValidation{
			WorkerType:  jobRequest.WorkerType,
			Config:      jobRequest.Config,
			Table:       jobRequest.Table,
			Source:      jobRequest.Source,
			Destination: jobRequest.Destination,
			Metadata:    jobRequest.Metadata,
		}

		if err := s.validator.Validate(jobRequestValidation); err != nil {
			s.log.WithError(err).WithField("worker_type", jobRequest.WorkerType).
				Error("Invalid job request")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Create task
		task := &worker.Task{
			ID:              generateTaskID(),
			Type:            mapWorkerType(jobRequest.WorkerType),
			CreatedAt:       time.Now().Unix(),
			RescheduleCount: 0,
		}

		// For caveo_payload_maker and geofencing workers, use the config field
		if jobRequest.WorkerType == "caveo_payload_maker" || jobRequest.WorkerType == "geof_division" || jobRequest.WorkerType == "geof_estate" || jobRequest.WorkerType == "geof_block" {
			payload, err := json.Marshal(jobRequest.Config)
			if err != nil {
				s.log.WithError(err).Error("Failed to marshal config")
				s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to marshal config")
				return
			}
			task.Payload = payload
		} else {
			// Marshal payload
			payload, err := json.Marshal(jobRequest)
			if err != nil {
				s.log.WithError(err).Error("Failed to marshal payload")
				s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to marshal payload")
				return
			}
			task.Payload = payload
		}

		// Dispatch task
		if err := s.dispatcher.Dispatch(r.Context(), task); err != nil {
			s.log.WithError(err).Error("Failed to dispatch task")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to dispatch task")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusAccepted)
		json.NewEncoder(w).Encode(map[string]string{
			"task_id": task.ID,
			"status":  "accepted",
			"message": "Task dispatched successfully",
		})
	}
}

// handleGetJobs handles the endpoint to get all jobs or filter by worker type and date
func (s *Server) handleGetJobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		// Get query parameters
		workerType := r.URL.Query().Get("worker_type")
		date := r.URL.Query().Get("date")
		limit := r.URL.Query().Get("limit")
		offset := r.URL.Query().Get("offset")

		// Set default values
		limitInt := 100
		offsetInt := 0

		// Parse limit
		if limit != "" {
			parsedLimit, err := strconv.Atoi(limit)
			if err == nil && parsedLimit > 0 {
				limitInt = parsedLimit
			}
		}

		// Parse offset
		if offset != "" {
			parsedOffset, err := strconv.Atoi(offset)
			if err == nil && parsedOffset >= 0 {
				offsetInt = parsedOffset
			}
		}

		// Create pattern for key
		var pattern string
		if workerType != "" && date != "" {
			pattern = fmt.Sprintf("jobs:%s:%s:*", workerType, date)
		} else if workerType != "" {
			pattern = fmt.Sprintf("jobs:%s:*", workerType)
		} else if date != "" {
			pattern = fmt.Sprintf("jobs:*:%s:*", date)
		} else {
			pattern = "jobs:*"
		}

		// Get keys matching the pattern
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get jobs from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get jobs")
			return
		}

		// Sort keys in descending order (newest first)
		sort.Sort(sort.Reverse(sort.StringSlice(keys)))

		// Apply pagination
		totalCount := len(keys)
		if offsetInt >= totalCount {
			// Return empty result if offset is beyond total count
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"status":      "ok",
				"total_count": totalCount,
				"count":       0,
				"offset":      offsetInt,
				"limit":       limitInt,
				"jobs":        []interface{}{},
			})
			return
		}

		// Calculate end index for pagination
		endIndex := offsetInt + limitInt
		if endIndex > totalCount {
			endIndex = totalCount
		}

		// Get the paginated keys
		paginatedKeys := keys[offsetInt:endIndex]

		// Get job data for each key
		jobs := make([]map[string]interface{}, 0, len(paginatedKeys))
		for _, key := range paginatedKeys {
			// Get job data from Redis
			result, err := s.redisClient.Get(ctx, key)
			if err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to get job data")
				continue
			}

			// Parse job data
			var jobData map[string]interface{}
			if err := json.Unmarshal([]byte(result), &jobData); err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to parse job data")
				continue
			}

			// Extract key parts
			parts := strings.Split(key, ":")
			if len(parts) >= 4 {
				jobData["key"] = key
				jobData["worker_type"] = parts[1]
				jobData["date"] = parts[2]
				jobData["task_id"] = parts[3]
			}

			jobs = append(jobs, jobData)
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"total_count": totalCount,
			"count":       len(jobs),
			"offset":      offsetInt,
			"limit":       limitInt,
			"jobs":        jobs,
		})
	}
}

// handleGetJob handles the job status endpoint
func (s *Server) handleGetJob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get job ID from URL
		vars := mux.Vars(r)
		jobID := vars["id"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
		defer cancel()

		// Create pattern for key
		pattern := fmt.Sprintf("jobs:*:*:%s", jobID)

		// Get keys matching the pattern
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get job from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get job")
			return
		}

		if len(keys) == 0 {
			// Job not found
			s.sendErrorResponse(w, http.StatusNotFound, "Job not found")
			return
		}

		// Get job data from Redis (use the first key found)
		result, err := s.redisClient.Get(ctx, keys[0])
		if err != nil {
			s.log.WithError(err).Error("Failed to get job data from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get job data")
			return
		}

		// Parse job data
		var jobData map[string]interface{}
		if err := json.Unmarshal([]byte(result), &jobData); err != nil {
			s.log.WithError(err).Error("Failed to parse job data")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to parse job data")
			return
		}

		// Extract key parts
		parts := strings.Split(keys[0], ":")
		if len(parts) >= 4 {
			jobData["key"] = keys[0]
			jobData["worker_type"] = parts[1]
			jobData["date"] = parts[2]
			jobData["task_id"] = parts[3]
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(jobData)
	}
}

// sendErrorResponse sends an error response
func (s *Server) sendErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(map[string]string{
		"error": message,
	})
}

// isValidWorkerType checks if the worker type is valid
func isValidWorkerType(workerType string) bool {
	switch workerType {
	case "api_pull", "db_clone", "api_pull_caveo", "caveo_payload_maker", "caveo_to_influx", "geof_division", "geof_estate", "geof_block":
		return true
	default:
		return false
	}
}

// mapWorkerType maps the API worker type to the internal worker type
func mapWorkerType(apiWorkerType string) worker.WorkerType {
	switch apiWorkerType {
	case "api_pull":
		return "api_pull"
	case "db_clone":
		return worker.DBWorker
	case "api_pull_caveo":
		return worker.APICaveoWorker
	case "caveo_payload_maker":
		return "caveo_payload_maker"
	case "caveo_to_influx":
		return "caveo_to_influx"
	case "geof_division":
		return worker.GeofDivisionWorkerType
	case "geof_estate":
		return worker.GeofEstateWorkerType
	case "geof_block":
		return worker.GeofBlockWorkerType
	default:
		return ""
	}
}

// GetWorkerPayloadSchema returns the JSON schema for a specific worker type
func GetWorkerPayloadSchema(workerType string) (interface{}, error) {
	var schema interface{}

	switch workerType {
	case "api_pull":
		schema = jsonschema.Reflect(&APIPullPayload{})
	case "api_pull_caveo":
		schema = jsonschema.Reflect(&CaveoAPIPayload{})
	case "db_clone":
		schema = jsonschema.Reflect(&DBCloningPayload{})
	case "caveo_payload_maker":
		schema = jsonschema.Reflect(&CaveoPayloadMakerPayload{})
	case "caveo_to_influx":
		schema = jsonschema.Reflect(&CaveoToInfluxPayload{})
	case "geof_division":
		schema = jsonschema.Reflect(&GeofDivisionPayload{})
	case "geof_estate":
		schema = jsonschema.Reflect(&GeofEstatePayload{})
	case "geof_block":
		schema = jsonschema.Reflect(&GeofBlockPayload{})
	default:
		return nil, fmt.Errorf("unknown worker type: %s", workerType)
	}

	return schema, nil
}

// generateTaskID generates a unique task ID using UUID
func generateTaskID() string {
	return fmt.Sprintf("task-%s", uuid.New().String())
}

// handleGetAllLogs handles the endpoint to get all logs with optional filtering
func (s *Server) handleGetAllLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Parse query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// Validate query parameters
		if err := ValidateLogQueryParams(s.validator.validate, params); err != nil {
			s.log.WithError(err).Error("Invalid query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByWorkerType handles the endpoint to get logs by worker type
func (s *Server) handleGetLogsByWorkerType() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		params.WorkerType = workerType

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByIMEI handles the endpoint to get logs by IMEI
func (s *Server) handleGetLogsByIMEI() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get IMEI from URL
		vars := mux.Vars(r)
		imei := vars["imei"]

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		params.IMEI = imei

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByStatus handles the endpoint to get logs by status
func (s *Server) handleGetLogsByStatus() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get status from URL
		vars := mux.Vars(r)
		status := vars["status"]

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		params.Status = status

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByTaskID handles the endpoint to get logs by task ID
func (s *Server) handleGetLogsByTaskID() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get task ID from URL
		vars := mux.Vars(r)
		taskID := vars["task_id"]

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		params.TaskID = taskID

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByError handles the endpoint to get logs by error message
func (s *Server) handleGetLogsByError() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get error from URL
		vars := mux.Vars(r)
		errorMsg := vars["error"]

		// URL decode the error message
		decodedError, err := url.QueryUnescape(errorMsg)
		if err != nil {
			s.log.WithError(err).Error("Failed to decode error message")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid error message encoding")
			return
		}

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		params.Error = decodedError

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByTimestamp handles the endpoint to get logs by timestamp
func (s *Server) handleGetLogsByTimestamp() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get timestamp from URL
		vars := mux.Vars(r)
		timestamp := vars["timestamp"]

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}
		params.Timestamp = timestamp

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleReloadLogs handles the endpoint to reload logs
func (s *Server) handleReloadLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Reload logs
		if err := s.logService.ReloadLogs(); err != nil {
			s.log.WithError(err).Error("Failed to reload logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to reload logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "ok",
			"message": "Logs reloaded successfully",
		})
	}
}

// handleCreateLog handles the endpoint to create a new log
func (s *Server) handleCreateLog() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Parse request body
		var logEntry LogEntry
		if err := json.NewDecoder(r.Body).Decode(&logEntry); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Set default timestamp if not provided
		if logEntry.Timestamp == "" {
			logEntry.Timestamp = time.Now().Format(time.RFC3339)
		}

		// Validate log entry using validator
		logEntryValidation := LogEntryValidation{
			Timestamp:  logEntry.Timestamp,
			WorkerType: logEntry.WorkerType,
			Status:     logEntry.Status,
			Metadata:   logEntry.Metadata,
		}

		if err := s.validator.Validate(logEntryValidation); err != nil {
			s.log.WithError(err).Error("Invalid log entry")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Save log entry
		if err := s.logService.SaveLog(logEntry); err != nil {
			s.log.WithError(err).Error("Failed to save log entry")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to save log entry")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "ok",
			"message": "Log entry created successfully",
		})
	}
}

// handleGetLogByDateStatusTaskID handles the endpoint to get a specific log by date, status, and task ID
func (s *Server) handleGetLogByDateStatusTaskID() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get parameters from URL
		vars := mux.Vars(r)
		date := vars["date"]
		status := vars["status"]
		taskID := vars["task_id"]

		// Get log entry
		logEntry, err := s.logService.GetLogByDateStatusTaskID(date, status, taskID)
		if err != nil {
			s.log.WithError(err).Error("Failed to get log entry")
			s.sendErrorResponse(w, http.StatusNotFound, "Log entry not found")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(logEntry)
	}
}

// handleUpdateLog handles the endpoint to update a log entry
func (s *Server) handleUpdateLog() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get parameters from URL
		vars := mux.Vars(r)
		date := vars["date"]
		status := vars["status"]
		taskID := vars["task_id"]

		// Create key
		key := fmt.Sprintf("logs:%s:%s:%s", date, status, taskID)

		// Parse request body
		var logEntry LogEntry
		if err := json.NewDecoder(r.Body).Decode(&logEntry); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Update log entry
		if err := s.logService.UpdateLog(key, logEntry); err != nil {
			s.log.WithError(err).Error("Failed to update log entry")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to update log entry")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "ok",
			"message": "Log entry updated successfully",
		})
	}
}

// handleDeleteLog handles the endpoint to delete a log entry
func (s *Server) handleDeleteLog() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get parameters from URL
		vars := mux.Vars(r)
		date := vars["date"]
		status := vars["status"]
		taskID := vars["task_id"]

		// Create key
		key := fmt.Sprintf("logs:%s:%s:%s", date, status, taskID)

		// Delete log entry
		if err := s.logService.DeleteLog(key); err != nil {
			s.log.WithError(err).Error("Failed to delete log entry")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete log entry")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "ok",
			"message": "Log entry deleted successfully",
		})
	}
}

// handleBulkDeleteLogs handles the endpoint to delete multiple log entries
func (s *Server) handleBulkDeleteLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get key pattern from query parameters
		prefix := r.URL.Query().Get("prefix")
		if prefix == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Missing prefix parameter")
			return
		}

		// Delete log entries
		deleted, err := s.logService.DeleteLogsByPattern(prefix)
		if err != nil {
			s.log.WithError(err).Error("Failed to delete log entries")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete log entries")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": fmt.Sprintf("Successfully deleted %d log entries", deleted),
			"deleted": deleted,
		})
	}
}

// handleGetLogsByPrefix handles the endpoint to get logs by prefix
func (s *Server) handleGetLogsByPrefix() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get key pattern from query parameters
		prefix := r.URL.Query().Get("key")
		if prefix == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Missing key parameter")
			return
		}

		// Get logs
		response, err := s.logService.GetLogsByPrefix(prefix)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleBatchCreateLogs handles the endpoint to create multiple log entries
func (s *Server) handleBatchCreateLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Parse request body
		var logEntries []LogEntry
		if err := json.NewDecoder(r.Body).Decode(&logEntries); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Validate log entries
		for i, entry := range logEntries {
			// Set default timestamp if not provided
			if entry.Timestamp == "" {
				logEntries[i].Timestamp = time.Now().Format(time.RFC3339)
				entry.Timestamp = logEntries[i].Timestamp
			}

			// Validate log entry using validator
			logEntryValidation := LogEntryValidation{
				Timestamp:  entry.Timestamp,
				WorkerType: entry.WorkerType,
				Status:     entry.Status,
				Metadata:   entry.Metadata,
			}

			if err := s.validator.Validate(logEntryValidation); err != nil {
				s.log.WithError(err).WithField("entry_index", i).Error("Invalid log entry in batch")
				s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error in entry %d: %s", i, err.Error()))
				return
			}
		}

		// Save log entries
		if err := s.logService.SaveLogsBatch(logEntries); err != nil {
			s.log.WithError(err).Error("Failed to save log entries")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to save log entries")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": fmt.Sprintf("Successfully created %d log entries", len(logEntries)),
			"count":   len(logEntries),
		})
	}
}

// handleSearchLogs handles the endpoint to search logs
func (s *Server) handleSearchLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get search parameters
		imei := r.URL.Query().Get("imei")
		if imei == "" {
			imei = r.URL.Query().Get("worker_id") // Alternative parameter name
		}

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// Set IMEI parameter
		params.IMEI = imei

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetRecentLogs handles the endpoint to get recent logs
func (s *Server) handleGetRecentLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get limit parameter
		limitStr := r.URL.Query().Get("limit")
		limit := 100 // Default limit
		if limitStr != "" {
			if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
				limit = l
			}
		}

		// Get logs
		response, err := s.logService.GetRecentLogs(limit)
		if err != nil {
			s.log.WithError(err).Error("Failed to get recent logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get recent logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByStatusCode handles the endpoint to get logs by status code
func (s *Server) handleGetLogsByStatusCode() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get status code parameter
		code := r.URL.Query().Get("code")
		if code == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Missing code parameter")
			return
		}

		// Get logs
		response, err := s.logService.GetLogsByStatusCode(code)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs by status code")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs by status code")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogsByURL handles the endpoint to get logs by URL
func (s *Server) handleGetLogsByURL() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get URL parameter
		url := r.URL.Query().Get("url")
		if url == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Missing url parameter")
			return
		}

		// Get logs
		response, err := s.logService.GetLogsByURL(url)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs by URL")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs by URL")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetLogStats handles the endpoint to get log statistics
func (s *Server) handleGetLogStats() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get log statistics
		stats, err := s.logService.GetLogStats()
		if err != nil {
			s.log.WithError(err).Error("Failed to get log statistics")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get log statistics")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(stats)
	}
}

// handleGroupLogsByField handles the endpoint to group logs by field
func (s *Server) handleGroupLogsByField() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get field parameter
		field := r.URL.Query().Get("field")
		if field == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Missing field parameter")
			return
		}

		// Group logs by field
		grouped, err := s.logService.GroupLogsByField(field)
		if err != nil {
			s.log.WithError(err).Error("Failed to group logs by field")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to group logs by field")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(grouped)
	}
}

// handleCountLogsByStatus handles the endpoint to count logs by status
func (s *Server) handleCountLogsByStatus() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get date parameter
		date := r.URL.Query().Get("date")
		if date == "" {
			date = time.Now().Format("2006-01-02")
		}

		// Count logs by status
		counts, err := s.logService.CountLogsByStatus(date)
		if err != nil {
			s.log.WithError(err).Error("Failed to count logs by status")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to count logs by status")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(counts)
	}
}

// handleGetStatusCodeDistribution handles the endpoint to get status code distribution
func (s *Server) handleGetStatusCodeDistribution() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get date parameter
		date := r.URL.Query().Get("date")
		if date == "" {
			date = time.Now().Format("2006-01-02")
		}

		// Get status code distribution
		distribution, err := s.logService.GetStatusCodeDistribution(date)
		if err != nil {
			s.log.WithError(err).Error("Failed to get status code distribution")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get status code distribution")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(distribution)
	}
}

// handleGetSystemStats handles the endpoint to get system statistics
func (s *Server) handleGetSystemStats() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get system statistics
		stats, err := s.logService.GetSystemStats()
		if err != nil {
			s.log.WithError(err).Error("Failed to get system statistics")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get system statistics")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(stats)
	}
}

// handleGetLogByKey handles the endpoint to get logs by Redis key or key pattern
func (s *Server) handleGetLogByKey() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get key from URL
		vars := mux.Vars(r)
		key := vars["key"]

		// URL decode the key
		decodedKey, err := url.QueryUnescape(key)
		if err != nil {
			s.log.WithError(err).Error("Failed to decode key")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid key encoding")
			return
		}

		// Check if exact match is requested
		exactMatch := r.URL.Query().Get("exact") == "true"

		if exactMatch {
			// Get log by exact key match
			logEntry, err := s.logService.GetLogByKey(decodedKey)
			if err != nil {
				s.log.WithError(err).WithField("key", decodedKey).Error("Failed to get log by key")
				s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("Log with key '%s' not found", decodedKey))
				return
			}

			// Send response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(logEntry)
		} else {
			// If key doesn't end with wildcard, add it
			if !strings.HasSuffix(decodedKey, "*") {
				decodedKey = decodedKey + "*"
			}

			// Get logs by key pattern
			response, err := s.logService.GetLogsByKeyPattern(decodedKey)
			if err != nil {
				s.log.WithError(err).WithField("key_pattern", decodedKey).Error("Failed to get logs by key pattern")
				s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs by key pattern")
				return
			}

			// Check if any logs were found
			if response.Total == 0 {
				s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("No logs found matching pattern '%s'", decodedKey))
				return
			}

			// Send response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)
		}
	}
}

// handleGetLogsByDate handles the endpoint to get logs by date
func (s *Server) handleGetLogsByDate() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get date from URL
		vars := mux.Vars(r)
		date := vars["date"]

		// Validate date format
		_, err := time.Parse("2006-01-02", date)
		if err != nil {
			s.log.WithError(err).Error("Invalid date format")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid date format. Expected YYYY-MM-DD")
			return
		}

		// Get logs by date
		response, err := s.logService.GetLogsByDate(date)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs by date")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs by date")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleListFiles handles the endpoint to list all buckets and their files in Minio
func (s *Server) handleListFiles() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Minio client is available
		if s.minioClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Minio client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Get list of all buckets
		buckets, err := s.minioClient.ListBuckets(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to list buckets")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to list buckets")
			return
		}

		// Create response with bucket information
		bucketsInfo := make(map[string]interface{})
		for _, bucket := range buckets {
			// Count objects in bucket
			objectCount := 0
			objectCh := s.minioClient.ListObjects(ctx, bucket.Name, minio.ListObjectsOptions{
				Recursive: false,
			})

			// Get top-level folders
			folders := make(map[string]int)
			for object := range objectCh {
				if object.Err != nil {
					s.log.WithError(object.Err).WithField("bucket", bucket.Name).Error("Failed to list objects")
					continue
				}

				objectCount++

				// Extract folder from key
				key := object.Key
				parts := strings.Split(key, "/")
				if len(parts) > 0 {
					folderName := parts[0]
					if folderName != "" {
						folders[folderName]++
					}
				}
			}

			// Add bucket info to response
			bucketsInfo[bucket.Name] = map[string]interface{}{
				"name":          bucket.Name,
				"creation_date": bucket.CreationDate,
				"object_count":  objectCount,
				"top_folders":   folders,
			}
		}

		// Map worker types to bucket names for easier reference
		workerBuckets := make(map[string]string)
		for workerType, bucketName := range s.minioBuckets {
			workerBuckets[workerType] = bucketName
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"buckets":        bucketsInfo,
			"worker_buckets": workerBuckets,
			"total_buckets":  len(buckets),
		})
	}
}

// handleListFilesByBucket handles the endpoint to list files in a specific bucket
func (s *Server) handleListFilesByBucket() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Minio client is available
		if s.minioClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Minio client not available")
			return
		}

		// Get bucket from URL
		vars := mux.Vars(r)
		bucketName := vars["bucket"]

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Check if bucket exists
		exists, err := s.minioClient.BucketExists(ctx, bucketName)
		if err != nil {
			s.log.WithError(err).WithField("bucket", bucketName).Error("Failed to check if bucket exists")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to check if bucket exists")
			return
		}
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("Bucket '%s' not found", bucketName))
			return
		}

		// List objects in bucket
		objectCh := s.minioClient.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
			Recursive: false,
		})

		// Collect objects and folders
		var objects []map[string]interface{}
		folderMap := make(map[string]int)

		for object := range objectCh {
			if object.Err != nil {
				s.log.WithError(object.Err).Error("Failed to list objects")
				s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to list objects")
				return
			}

			// Check if object is a folder
			if strings.HasSuffix(object.Key, "/") {
				folderName := strings.TrimSuffix(object.Key, "/")
				folderMap[folderName]++
				continue
			}

			// Add object to list
			objects = append(objects, map[string]interface{}{
				"key":           object.Key,
				"size":          object.Size,
				"last_modified": object.LastModified,
				"etag":          object.ETag,
			})

			// Extract folder from key
			parts := strings.Split(object.Key, "/")
			if len(parts) > 1 {
				folderName := parts[0]
				if folderName != "" {
					folderMap[folderName]++
				}
			}
		}

		// Convert folder map to array for response
		folders := make([]map[string]interface{}, 0, len(folderMap))
		for folder, count := range folderMap {
			folders = append(folders, map[string]interface{}{
				"name":  folder,
				"count": count,
			})
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"bucket":        bucketName,
			"folders":       folders,
			"objects":       objects,
			"total_folders": len(folders),
			"total_objects": len(objects),
		})
	}
}

// handleListFilesByFolder handles the endpoint to list files in a specific folder
func (s *Server) handleListFilesByFolder() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Minio client is available
		if s.minioClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Minio client not available")
			return
		}

		// Get bucket and folder from URL
		vars := mux.Vars(r)
		bucketName := vars["bucket"]
		folder := vars["folder"]

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Check if bucket exists
		exists, err := s.minioClient.BucketExists(ctx, bucketName)
		if err != nil {
			s.log.WithError(err).WithField("bucket", bucketName).Error("Failed to check if bucket exists")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to check if bucket exists")
			return
		}
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("Bucket '%s' not found", bucketName))
			return
		}

		// List objects in folder
		prefix := folder + "/"
		objectCh := s.minioClient.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
			Prefix:    prefix,
			Recursive: true,
		})

		// Collect objects
		var objects []map[string]interface{}
		for object := range objectCh {
			if object.Err != nil {
				s.log.WithError(object.Err).Error("Failed to list objects")
				s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to list objects")
				return
			}

			// Add object to list
			objects = append(objects, map[string]interface{}{
				"key":           object.Key,
				"size":          object.Size,
				"last_modified": object.LastModified,
				"etag":          object.ETag,
				"url":           fmt.Sprintf("/api/files/download/%s/%s", bucketName, object.Key),
			})
		}

		// Group objects by subfolder and separate files
		subfolderMap := make(map[string][]map[string]interface{})
		filesList := make([]map[string]interface{}, 0)

		for _, object := range objects {
			key := object["key"].(string)
			// Remove prefix from key
			relativeKey := strings.TrimPrefix(key, prefix)
			parts := strings.Split(relativeKey, "/")

			if len(parts) > 1 && parts[1] != "" {
				// This is a subfolder
				subfolder := parts[0]
				if subfolder != "" {
					if _, ok := subfolderMap[subfolder]; !ok {
						subfolderMap[subfolder] = []map[string]interface{}{}
					}
					subfolderMap[subfolder] = append(subfolderMap[subfolder], object)
				}
			} else {
				// This is a file directly in the folder
				filesList = append(filesList, object)
			}
		}

		// Convert subfolder map to array for response
		subfolders := make([]map[string]interface{}, 0, len(subfolderMap))
		for subfolder, subfolderObjects := range subfolderMap {
			subfolders = append(subfolders, map[string]interface{}{
				"name":   subfolder,
				"count":  len(subfolderObjects),
				"path":   fmt.Sprintf("%s/%s", folder, subfolder),
				"url":    fmt.Sprintf("/api/files/%s/%s/%s", bucketName, folder, subfolder),
				"is_dir": true,
			})
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"bucket":        bucketName,
			"folder":        folder,
			"subfolders":    subfolders,
			"files":         filesList,
			"total_objects": len(objects),
			"total_files":   len(filesList),
		})
	}
}

// handleListFilesBySubfolder handles the endpoint to list files in a specific subfolder
func (s *Server) handleListFilesBySubfolder() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Minio client is available
		if s.minioClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Minio client not available")
			return
		}

		// Get bucket, folder and subfolder from URL
		vars := mux.Vars(r)
		bucketName := vars["bucket"]
		folder := vars["folder"]
		subfolder := vars["subfolder"]

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Check if bucket exists
		exists, err := s.minioClient.BucketExists(ctx, bucketName)
		if err != nil {
			s.log.WithError(err).WithField("bucket", bucketName).Error("Failed to check if bucket exists")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to check if bucket exists")
			return
		}
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("Bucket '%s' not found", bucketName))
			return
		}

		// List objects in subfolder
		prefix := filepath.Join(folder, subfolder) + "/"
		objectCh := s.minioClient.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
			Prefix:    prefix,
			Recursive: true,
		})

		// Collect objects
		var objects []map[string]interface{}
		for object := range objectCh {
			if object.Err != nil {
				s.log.WithError(object.Err).Error("Failed to list objects")
				s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to list objects")
				return
			}

			// Add object to list
			objects = append(objects, map[string]interface{}{
				"key":           object.Key,
				"size":          object.Size,
				"last_modified": object.LastModified,
				"etag":          object.ETag,
				"url":           fmt.Sprintf("/api/files/download/%s/%s", bucketName, object.Key),
			})
		}

		// Group objects by sub-subfolder if any
		subSubfolderMap := make(map[string][]map[string]interface{})
		filesList := make([]map[string]interface{}, 0)

		for _, object := range objects {
			key := object["key"].(string)
			// Remove prefix from key
			relativeKey := strings.TrimPrefix(key, prefix)
			parts := strings.Split(relativeKey, "/")

			if len(parts) > 1 && parts[0] != "" {
				// This is a file in a sub-subfolder
				subSubfolder := parts[0]
				if _, ok := subSubfolderMap[subSubfolder]; !ok {
					subSubfolderMap[subSubfolder] = []map[string]interface{}{}
				}
				subSubfolderMap[subSubfolder] = append(subSubfolderMap[subSubfolder], object)
			} else if relativeKey != "" {
				// This is a file directly in the subfolder
				filesList = append(filesList, object)
			}
		}

		// Convert sub-subfolder map to array for response
		subSubfolders := make([]map[string]interface{}, 0, len(subSubfolderMap))
		for subSubfolder, subSubfolderObjects := range subSubfolderMap {
			subSubfolders = append(subSubfolders, map[string]interface{}{
				"name":  subSubfolder,
				"count": len(subSubfolderObjects),
				"path":  fmt.Sprintf("%s/%s/%s", folder, subfolder, subSubfolder),
			})
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"bucket":         bucketName,
			"folder":         folder,
			"subfolder":      subfolder,
			"sub_subfolders": subSubfolders,
			"files":          filesList,
			"total_objects":  len(objects),
			"total_files":    len(filesList),
		})
	}
}

// CaveoJobRequest represents a request to submit a Caveo API pull job
type CaveoJobRequest struct {
	WorkerType string `json:"worker_type"`
	Config     struct {
		Device struct {
			PSM      string `json:"psm"`
			Estate   string `json:"estate"`
			Division string `json:"division"`
			Ident    string `json:"ident"`
			NIK      string `json:"nik"`
			Name     string `json:"name"`
			Type     string `json:"type"`
		} `json:"device"`
		TimeStart string `json:"timeStart"`
		TimeEnd   string `json:"timeEnd"`
	} `json:"config"`
}

// RescheduleJobRequest represents a request to trigger re-scheduling of jobs
type RescheduleJobRequest struct {
	WorkerType string `json:"worker_type"`
}

// handleSubmitCaveoJob handles the endpoint to submit a Caveo API pull job
func (s *Server) handleSubmitCaveoJob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Parse request body
		var jobRequest CaveoJobRequest
		if err := json.NewDecoder(r.Body).Decode(&jobRequest); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Set worker type to api_pull_caveo
		jobRequest.WorkerType = "api_pull_caveo"

		// Validate request using validator
		caveoJobRequestValidation := CaveoJobRequestValidation{
			WorkerType: jobRequest.WorkerType,
			Config: CaveoJobConfigValidation{
				Device: CaveoDeviceValidation{
					PSM:      jobRequest.Config.Device.PSM,
					Estate:   jobRequest.Config.Device.Estate,
					Division: jobRequest.Config.Device.Division,
					Ident:    jobRequest.Config.Device.Ident,
					NIK:      jobRequest.Config.Device.NIK,
					Name:     jobRequest.Config.Device.Name,
					Type:     jobRequest.Config.Device.Type,
				},
				TimeStart: jobRequest.Config.TimeStart,
				TimeEnd:   jobRequest.Config.TimeEnd,
			},
		}

		if err := s.validator.Validate(caveoJobRequestValidation); err != nil {
			s.log.WithError(err).Error("Invalid Caveo job request")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Create task
		task := &worker.Task{
			ID:              generateTaskID(),
			Type:            worker.APICaveoWorker,
			CreatedAt:       time.Now().Unix(),
			RescheduleCount: 0,
		}

		// Marshal payload
		payload, err := json.Marshal(jobRequest)
		if err != nil {
			s.log.WithError(err).Error("Failed to marshal payload")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to marshal payload")
			return
		}

		task.Payload = payload

		// Dispatch task
		if err := s.dispatcher.Dispatch(r.Context(), task); err != nil {
			s.log.WithError(err).Error("Failed to dispatch task")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to dispatch task")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusAccepted)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"task_id": task.ID,
			"message": "Caveo API pull job submitted successfully",
		})
	}
}

// handleGetCaveoJob handles the endpoint to get a Caveo API pull job status
func (s *Server) handleGetCaveoJob() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get job ID from URL
		vars := mux.Vars(r)
		jobID := vars["id"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
		defer cancel()

		// Create pattern for key
		pattern := fmt.Sprintf("logs:api_pull_caveo:*:*:%s", jobID)

		// Get logs matching the pattern
		logs, err := s.redisClient.GetLogsByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get job status")
			return
		}

		if len(logs) == 0 {
			// Job not found
			s.sendErrorResponse(w, http.StatusNotFound, "Job not found")
			return
		}

		// Get the most recent log
		var latestLog LogEntry
		var latestTime time.Time
		for _, log := range logs {
			t, err := time.Parse(time.RFC3339, log.Timestamp)
			if err == nil && (latestTime.IsZero() || t.After(latestTime)) {
				latestLog = LogEntry{
					Timestamp:  log.Timestamp,
					WorkerType: log.WorkerType,
					Status:     log.Status,
					Metadata:   log.Metadata,
				}
				latestTime = t
			}
		}

		// Convert log entry to map for response
		logData, err := json.Marshal(latestLog)
		if err != nil {
			s.log.WithError(err).Error("Failed to marshal log data")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to process job status")
			return
		}

		// Parse log data
		var logEntry map[string]interface{}
		if err := json.Unmarshal([]byte(logData), &logEntry); err != nil {
			s.log.WithError(err).Error("Failed to parse log data")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to parse job status")
			return
		}

		// Extract status from log entry
		status := "unknown"
		if statusVal, ok := logEntry["status"].(string); ok {
			status = statusVal
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"task_id": jobID,
			"status":  status,
			"log":     logEntry,
		})
	}
}

// handleGetCaveoLogs handles the endpoint to get all Caveo API pull logs
func (s *Server) handleGetCaveoLogs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// Set worker type to api_pull_caveo
		params.WorkerType = "api_pull_caveo"

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetCaveoLogsByIMEI handles the endpoint to get Caveo API pull logs for a specific IMEI
func (s *Server) handleGetCaveoLogsByIMEI() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get IMEI from URL
		vars := mux.Vars(r)
		imei := vars["imei"]

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// Set worker type and IMEI
		params.WorkerType = "api_pull_caveo"
		params.IMEI = imei

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetCaveoLogsByDateAndIMEI handles the endpoint to get Caveo API pull logs for a specific date and IMEI
func (s *Server) handleGetCaveoLogsByDateAndIMEI() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get date and IMEI from URL
		vars := mux.Vars(r)
		date := vars["date"]
		imei := vars["imei"]

		// Validate date format
		_, err := time.Parse("2006-01-02", date)
		if err != nil {
			s.log.WithError(err).Error("Invalid date format")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid date format. Expected YYYY-MM-DD")
			return
		}

		// Create query parameters
		params, err := NewLogQueryParamsFromURL(r.URL.Query())
		if err != nil {
			s.log.WithError(err).Error("Failed to parse query parameters")
			s.sendErrorResponse(w, http.StatusBadRequest, err.Error())
			return
		}

		// Set worker type, date, and IMEI
		params.WorkerType = "api_pull_caveo"
		params.Date = date
		params.IMEI = imei

		// Get logs
		response, err := s.logService.GetLogs(params)
		if err != nil {
			s.log.WithError(err).Error("Failed to get logs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get logs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}
}

// handleGetRescheduleJobs handles the endpoint to get all re-scheduled jobs
func (s *Server) handleGetRescheduleJobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		// Get all re-schedule jobs keys
		pattern := "re-schedule-jobs:*"

		// Get keys matching the pattern
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get re-schedule jobs keys from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get re-schedule jobs")
			return
		}

		// Group jobs by worker type
		jobsByWorkerType := make(map[string][]map[string]interface{})
		for _, key := range keys {
			// Extract worker type from key
			parts := strings.Split(key, ":")
			if len(parts) >= 3 {
				workerType := parts[1]

				// Get job payload from Redis
				result, err := s.redisClient.Get(ctx, key)
				if err != nil {
					s.log.WithError(err).WithField("key", key).Warn("Failed to get re-schedule job")
					continue
				}

				// Parse job payload
				var jobPayload map[string]interface{}
				if err := json.Unmarshal([]byte(result), &jobPayload); err != nil {
					s.log.WithError(err).WithField("key", key).Warn("Failed to parse re-schedule job payload")
					continue
				}

				// Add job to list
				if _, ok := jobsByWorkerType[workerType]; !ok {
					jobsByWorkerType[workerType] = []map[string]interface{}{}
				}
				jobsByWorkerType[workerType] = append(jobsByWorkerType[workerType], map[string]interface{}{
					"key":     key,
					"payload": jobPayload,
				})
			}
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"jobs":   jobsByWorkerType,
			"count":  len(keys),
		})
	}
}

// handleGetRescheduleJobsByWorkerType handles the endpoint to get re-scheduled jobs for a specific worker type
func (s *Server) handleGetRescheduleJobsByWorkerType() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		// Get re-schedule jobs keys for the worker type
		pattern := fmt.Sprintf("re-schedule-jobs:%s:*", workerType)
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get re-schedule jobs keys from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get re-schedule jobs")
			return
		}

		// Get job payloads
		jobs := make([]map[string]interface{}, 0, len(keys))
		for _, key := range keys {
			// Get job payload from Redis
			result, err := s.redisClient.Get(ctx, key)
			if err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to get re-schedule job")
				continue
			}

			// Parse job payload
			var jobPayload map[string]interface{}
			if err := json.Unmarshal([]byte(result), &jobPayload); err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to parse re-schedule job payload")
				continue
			}

			// Add job to list
			jobs = append(jobs, map[string]interface{}{
				"key":     key,
				"payload": jobPayload,
			})
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"worker_type": workerType,
			"jobs":        jobs,
			"count":       len(jobs),
		})
	}
}

// handleTriggerRescheduleJobs handles the endpoint to trigger re-scheduling of jobs
func (s *Server) handleTriggerRescheduleJobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Get all re-schedule jobs keys
		pattern := "re-schedule-jobs:*"
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get re-schedule jobs keys from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get re-schedule jobs")
			return
		}

		// Process each job
		processedJobs := 0
		failedJobs := 0
		for _, key := range keys {
			// Extract worker type from key
			parts := strings.Split(key, ":")
			if len(parts) < 3 {
				s.log.WithField("key", key).Warn("Invalid re-schedule job key format")
				failedJobs++
				continue
			}
			workerType := parts[1]

			// Get job payload from Redis
			result, err := s.redisClient.Get(ctx, key)
			if err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to get re-schedule job")
				failedJobs++
				continue
			}

			// Parse the payload to extract RescheduleCount
			var payloadMap map[string]interface{}
			rescheduleCount := 0
			if err := json.Unmarshal([]byte(result), &payloadMap); err == nil {
				if count, ok := payloadMap["reschedule_count"]; ok {
					if countInt, ok := count.(float64); ok {
						rescheduleCount = int(countInt)
					}
				}
			}

			// Create task
			task := &worker.Task{
				ID:              generateTaskID(),
				Type:            worker.WorkerType(workerType),
				CreatedAt:       time.Now().Unix(),
				RescheduleCount: rescheduleCount,
				Payload:         []byte(result),
			}

			// Dispatch task
			if err := s.dispatcher.Dispatch(ctx, task); err != nil {
				s.log.WithError(err).WithField("key", key).Error("Failed to dispatch re-schedule job")
				failedJobs++
				continue
			}

			// Delete the job from Redis
			if err := s.redisClient.DeleteKey(ctx, key); err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to delete re-schedule job key")
			}

			processedJobs++
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":         "ok",
			"processed_jobs": processedJobs,
			"failed_jobs":    failedJobs,
			"total_jobs":     len(keys),
			"message":        fmt.Sprintf("Successfully processed %d re-schedule jobs", processedJobs),
		})
	}
}

// handleTriggerRescheduleJobsByWorkerType handles the endpoint to trigger re-scheduling of jobs for a specific worker type
func (s *Server) handleTriggerRescheduleJobsByWorkerType() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]

		// Block api_pull_caveo worker type from using this endpoint
		if workerType == "api_pull_caveo" {
			s.sendErrorResponse(w, http.StatusForbidden, "Reschedule endpoint is not available for api_pull_caveo worker type")
			return
		}

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Get re-schedule jobs keys for the worker type
		pattern := fmt.Sprintf("re-schedule-jobs:%s:*", workerType)
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get re-schedule jobs keys from Redis")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get re-schedule jobs")
			return
		}

		// Process each job
		processedJobs := 0
		failedJobs := 0
		for _, key := range keys {
			// Get job payload from Redis
			result, err := s.redisClient.Get(ctx, key)
			if err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to get re-schedule job")
				failedJobs++
				continue
			}

			// Parse the payload to extract RescheduleCount
			var payloadMap map[string]interface{}
			rescheduleCount := 0
			if err := json.Unmarshal([]byte(result), &payloadMap); err == nil {
				if count, ok := payloadMap["reschedule_count"]; ok {
					if countInt, ok := count.(float64); ok {
						rescheduleCount = int(countInt)
					}
				}
			}

			// Create task
			task := &worker.Task{
				ID:              generateTaskID(),
				Type:            worker.WorkerType(workerType),
				CreatedAt:       time.Now().Unix(),
				RescheduleCount: rescheduleCount,
				Payload:         []byte(result),
			}

			// Dispatch task
			if err := s.dispatcher.Dispatch(ctx, task); err != nil {
				s.log.WithError(err).WithField("key", key).Error("Failed to dispatch re-schedule job")
				failedJobs++
				continue
			}

			// Delete the job from Redis
			if err := s.redisClient.DeleteKey(ctx, key); err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to delete re-schedule job key")
			}

			processedJobs++
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":         "ok",
			"worker_type":    workerType,
			"processed_jobs": processedJobs,
			"failed_jobs":    failedJobs,
			"total_jobs":     len(keys),
			"message":        fmt.Sprintf("Successfully processed %d re-schedule jobs for worker type %s", processedJobs, workerType),
		})
	}
}

// handleDeleteRescheduleJobs handles the endpoint to delete all re-scheduled jobs
func (s *Server) handleDeleteRescheduleJobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Delete all re-schedule jobs keys
		pattern := "re-schedule-jobs:*"
		deleted, err := s.redisClient.DeleteByKeyPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to delete re-schedule jobs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete re-schedule jobs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"deleted": deleted,
			"message": fmt.Sprintf("Successfully deleted %d re-schedule jobs", deleted),
		})
	}
}

// handleDeleteRescheduleJobsByWorkerType handles the endpoint to delete re-scheduled jobs for a specific worker type
func (s *Server) handleDeleteRescheduleJobsByWorkerType() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Delete re-schedule jobs keys for the worker type
		pattern := fmt.Sprintf("re-schedule-jobs:%s:*", workerType)
		deleted, err := s.redisClient.DeleteByKeyPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to delete re-schedule jobs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete re-schedule jobs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"worker_type": workerType,
			"deleted":     deleted,
			"message":     fmt.Sprintf("Successfully deleted %d re-schedule jobs for worker type %s", deleted, workerType),
		})
	}
}

// handleDownloadFile handles the endpoint to download a file from Minio
func (s *Server) handleDownloadFile() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Minio client is available
		if s.minioClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Minio client not available")
			return
		}

		// Get bucket and path from URL
		vars := mux.Vars(r)
		bucketName := vars["bucket"]
		objectPath := vars["path"]

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Check if bucket exists
		exists, err := s.minioClient.BucketExists(ctx, bucketName)
		if err != nil {
			s.log.WithError(err).WithField("bucket", bucketName).Error("Failed to check if bucket exists")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to check if bucket exists")
			return
		}
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("Bucket '%s' not found", bucketName))
			return
		}

		// Check if object exists
		_, err = s.minioClient.StatObject(ctx, bucketName, objectPath, minio.StatObjectOptions{})
		if err != nil {
			s.log.WithError(err).WithFields(map[string]interface{}{
				"bucket": bucketName,
				"path":   objectPath,
			}).Error("Failed to stat object")
			s.sendErrorResponse(w, http.StatusNotFound, "File not found")
			return
		}

		// Get object
		object, err := s.minioClient.GetObject(ctx, bucketName, objectPath, minio.GetObjectOptions{})
		if err != nil {
			s.log.WithError(err).WithFields(map[string]interface{}{
				"bucket": bucketName,
				"path":   objectPath,
			}).Error("Failed to get object")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get file")
			return
		}
		defer object.Close()

		// Get object info
		stat, err := object.Stat()
		if err != nil {
			s.log.WithError(err).WithFields(map[string]interface{}{
				"bucket": bucketName,
				"path":   objectPath,
			}).Error("Failed to get object info")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get file info")
			return
		}

		// Set response headers
		w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filepath.Base(objectPath)))
		w.Header().Set("Content-Type", "application/octet-stream")
		w.Header().Set("Content-Length", fmt.Sprintf("%d", stat.Size))

		// Copy object data to response
		if _, err := io.Copy(w, object); err != nil {
			s.log.WithError(err).WithFields(map[string]interface{}{
				"bucket": bucketName,
				"path":   objectPath,
			}).Error("Failed to copy object data to response")
			// Cannot send error response here as headers have already been sent
			return
		}

		// Log download
		s.log.WithFields(map[string]interface{}{
			"bucket": bucketName,
			"path":   objectPath,
			"size":   stat.Size,
		}).Info("File downloaded")
	}
}

// handleDeleteFile handles the endpoint to delete a single file from Minio
func (s *Server) handleDeleteFile() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Minio client is available
		if s.minioClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Minio client not available")
			return
		}

		// Get bucket and path from URL
		vars := mux.Vars(r)
		bucketName := vars["bucket"]
		objectPath := vars["path"]

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Check if bucket exists
		exists, err := s.minioClient.BucketExists(ctx, bucketName)
		if err != nil {
			s.log.WithError(err).WithField("bucket", bucketName).Error("Failed to check if bucket exists")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to check if bucket exists")
			return
		}
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("Bucket '%s' not found", bucketName))
			return
		}

		// Check if object exists
		_, err = s.minioClient.StatObject(ctx, bucketName, objectPath, minio.StatObjectOptions{})
		if err != nil {
			s.log.WithError(err).WithFields(map[string]interface{}{
				"bucket": bucketName,
				"path":   objectPath,
			}).Error("Failed to stat object")
			s.sendErrorResponse(w, http.StatusNotFound, "File not found")
			return
		}

		// Delete the object
		err = s.minioClient.RemoveObject(ctx, bucketName, objectPath, minio.RemoveObjectOptions{})
		if err != nil {
			s.log.WithError(err).WithFields(map[string]interface{}{
				"bucket": bucketName,
				"path":   objectPath,
			}).Error("Failed to delete object")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete file")
			return
		}

		// Log deletion
		s.log.WithFields(map[string]interface{}{
			"bucket": bucketName,
			"path":   objectPath,
		}).Info("File deleted")

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": "File deleted successfully",
			"bucket":  bucketName,
			"path":    objectPath,
		})
	}
}

// handleDeleteMultipleFiles handles the endpoint to delete multiple files from a folder in Minio
func (s *Server) handleDeleteMultipleFiles() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Minio client is available
		if s.minioClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Minio client not available")
			return
		}

		// Get bucket and folder path from URL
		vars := mux.Vars(r)
		bucketName := vars["bucket"]
		folderPath := vars["folder-path"]

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 60*time.Second) // Longer timeout for bulk operations
		defer cancel()

		// Check if bucket exists
		exists, err := s.minioClient.BucketExists(ctx, bucketName)
		if err != nil {
			s.log.WithError(err).WithField("bucket", bucketName).Error("Failed to check if bucket exists")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to check if bucket exists")
			return
		}
		if !exists {
			s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("Bucket '%s' not found", bucketName))
			return
		}

		// Ensure folder path ends with a slash for proper prefix matching
		if !strings.HasSuffix(folderPath, "/") {
			folderPath = folderPath + "/"
		}

		// List objects in the folder
		objectCh := s.minioClient.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
			Prefix:    folderPath,
			Recursive: true,
		})

		// Collect objects to delete
		var objectsToDelete []minio.ObjectInfo
		for object := range objectCh {
			if object.Err != nil {
				s.log.WithError(object.Err).WithFields(map[string]interface{}{
					"bucket": bucketName,
					"folder": folderPath,
				}).Error("Failed to list objects")
				s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to list files in folder")
				return
			}
			objectsToDelete = append(objectsToDelete, object)
		}

		// Check if any objects were found
		if len(objectsToDelete) == 0 {
			s.sendErrorResponse(w, http.StatusNotFound, fmt.Sprintf("No files found in folder '%s'", folderPath))
			return
		}

		// Delete each object
		var deletedObjects []string
		var failedObjects []string

		for _, object := range objectsToDelete {
			err := s.minioClient.RemoveObject(ctx, bucketName, object.Key, minio.RemoveObjectOptions{})
			if err != nil {
				s.log.WithError(err).WithFields(map[string]interface{}{
					"bucket": bucketName,
					"path":   object.Key,
				}).Error("Failed to delete object")
				failedObjects = append(failedObjects, object.Key)
			} else {
				deletedObjects = append(deletedObjects, object.Key)
			}
		}

		// Log deletion
		s.log.WithFields(map[string]interface{}{
			"bucket":          bucketName,
			"folder":          folderPath,
			"deleted_count":   len(deletedObjects),
			"failed_count":    len(failedObjects),
			"total_attempted": len(objectsToDelete),
		}).Info("Bulk file deletion completed")

		// Send response
		w.Header().Set("Content-Type", "application/json")
		if len(failedObjects) > 0 {
			w.WriteHeader(http.StatusPartialContent)
		} else {
			w.WriteHeader(http.StatusOK)
		}

		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":          "ok",
			"message":         fmt.Sprintf("Deleted %d files, failed to delete %d files", len(deletedObjects), len(failedObjects)),
			"bucket":          bucketName,
			"folder":          folderPath,
			"deleted_count":   len(deletedObjects),
			"deleted_files":   deletedObjects,
			"failed_count":    len(failedObjects),
			"failed_files":    failedObjects,
			"total_attempted": len(objectsToDelete),
		})
	}
}

// handleDeleteRedisData handles the endpoint to delete Redis data by key pattern
func (s *Server) handleDeleteRedisData() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Get key pattern from query parameters
		keyPattern := r.URL.Query().Get("key-path")
		if keyPattern == "" {
			s.sendErrorResponse(w, http.StatusBadRequest, "Missing key-path parameter")
			return
		}

		// Log the request
		s.log.WithField("key_pattern", keyPattern).Info("Deleting Redis data")

		// Delete keys matching the pattern
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		deleted, err := s.redisClient.DeleteByKeyPattern(ctx, keyPattern)
		if err != nil {
			s.log.WithError(err).WithField("key_pattern", keyPattern).Error("Failed to delete Redis data")
			s.sendErrorResponse(w, http.StatusInternalServerError, fmt.Sprintf("Failed to delete Redis data: %v", err))
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"message": fmt.Sprintf("Successfully deleted %d keys matching pattern '%s'", deleted, keyPattern),
			"deleted": deleted,
		})
	}
}

// handleListWorkerTypes handles the endpoint to list all available worker types
func (s *Server) handleListWorkerTypes() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get all available worker types
		workerTypes := []string{
			"api_pull",
			"api_pull_caveo",
			"db_clone",
			"caveo_payload_maker",
			"reschedule_caveo",
			"caveo_to_influx",
			"geof_division",
			"geof_estate",
			"geof_block",
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"worker_types": workerTypes,
			"count":        len(workerTypes),
		})
	}
}

// handleGetWorkerTypeSchema handles the endpoint to get the schema for a specific worker type
func (s *Server) handleGetWorkerTypeSchema() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]

		// Check if worker type is valid
		if !isValidWorkerType(workerType) {
			s.log.WithField("worker_type", workerType).Error("Invalid worker type")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid worker type")
			return
		}

		// Get schema for worker type using the GetWorkerPayloadSchema function
		schema, err := GetWorkerPayloadSchema(workerType)
		if err != nil {
			s.log.WithError(err).WithField("worker_type", workerType).Error("Failed to get schema for worker type")
			s.sendErrorResponse(w, http.StatusInternalServerError, fmt.Sprintf("Failed to get schema: %s", err.Error()))
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(schema)
	}
}

// handleTriggerRescheduleCaveoWorker handles the endpoint to trigger the reschedule Caveo worker
func (s *Server) handleTriggerRescheduleCaveoWorker() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create a reschedule Caveo worker instance
		rescheduleWorker, err := worker.NewRescheduleCaveoWorker(s.log, nil)
		if err != nil {
			s.log.WithError(err).Error("Failed to create reschedule Caveo worker")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to create reschedule Caveo worker")
			return
		}

		// Set the dispatcher
		rescheduleWorker.SetDispatcher(s.dispatcher)

		// Set the Redis client directly
		if rescheduleWorkerImpl, ok := rescheduleWorker.(*worker.RescheduleCaveoWorkerImpl); ok {
			rescheduleWorkerImpl.SetRedisClient(s.redisClient)
		}

		// Create a dummy task for the worker
		task := &worker.Task{
			ID:        generateTaskID(),
			Type:      worker.RescheduleCaveoWorker,
			CreatedAt: time.Now().Unix(),
			Payload:   []byte("{}"), // Empty payload as the worker scans Redis directly
		}

		// Process the task
		if err := rescheduleWorker.Process(ctx, task); err != nil {
			s.log.WithError(err).Error("Failed to process reschedule Caveo task")
			s.sendErrorResponse(w, http.StatusInternalServerError, fmt.Sprintf("Failed to process reschedule task: %s", err.Error()))
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"task_id": task.ID,
			"message": "Reschedule Caveo worker triggered successfully",
		})
	}
}

// handleGetNotComplyJobs handles the endpoint to get all not-comply jobs
func (s *Server) handleGetNotComplyJobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Parse query parameters
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")

		// Set default values
		limit := 100
		offset := 0

		// Parse limit
		if limitStr != "" {
			if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		// Parse offset
		if offsetStr != "" {
			if parsedOffset, err := strconv.Atoi(offsetStr); err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Get all not-comply jobs keys
		pattern := "not-comply-job:*"
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get not-comply jobs keys")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get not-comply jobs")
			return
		}

		// Sort keys for consistent ordering
		sort.Strings(keys)

		// Filter out non-string keys by checking their type
		validKeys := make([]string, 0, len(keys))
		for _, key := range keys {
			// Quick check if key contains valid string data
			_, err := s.redisClient.Get(ctx, key)
			if err == nil {
				validKeys = append(validKeys, key)
			} else if !strings.Contains(err.Error(), "WRONGTYPE") {
				// Include keys that exist but might have other errors (we'll handle them later)
				validKeys = append(validKeys, key)
			}
			// Skip WRONGTYPE keys silently
		}

		// Calculate pagination with valid keys
		totalCount := len(validKeys)
		keys = validKeys
		startIndex := offset
		endIndex := offset + limit

		if startIndex >= totalCount {
			// Return empty result if offset is beyond available data
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"status":      "ok",
				"total_count": totalCount,
				"count":       0,
				"offset":      offset,
				"limit":       limit,
				"jobs":        []map[string]interface{}{},
			})
			return
		}

		if endIndex > totalCount {
			endIndex = totalCount
		}

		// Get paginated keys
		paginatedKeys := keys[startIndex:endIndex]

		// Retrieve job data for each key
		jobs := make([]map[string]interface{}, 0, len(paginatedKeys))
		for _, key := range paginatedKeys {
			value, err := s.redisClient.Get(ctx, key)
			if err != nil {
				// Skip keys that are not string values or don't exist
				if strings.Contains(err.Error(), "WRONGTYPE") {
					s.log.WithField("key", key).Debug("Skipping non-string Redis key")
					continue
				}
				s.log.WithError(err).WithField("key", key).Warn("Failed to get not-comply job data")
				continue
			}

			// Parse JSON data
			var jobData map[string]interface{}
			if err := json.Unmarshal([]byte(value), &jobData); err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to parse not-comply job data")
				continue
			}

			// Add key information to job data
			jobData["redis_key"] = key
			jobs = append(jobs, jobData)
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"total_count": totalCount,
			"count":       len(jobs),
			"offset":      offset,
			"limit":       limit,
			"jobs":        jobs,
		})
	}
}

// handleGetNotComplyJobsByWorkerType handles the endpoint to get not-comply jobs by worker type
func (s *Server) handleGetNotComplyJobsByWorkerType() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Parse query parameters
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")

		// Set default values
		limit := 100
		offset := 0

		// Parse limit
		if limitStr != "" {
			if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		// Parse offset
		if offsetStr != "" {
			if parsedOffset, err := strconv.Atoi(offsetStr); err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Get not-comply jobs keys for specific worker type
		pattern := fmt.Sprintf("not-comply-job:%s:*", workerType)
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get not-comply jobs keys")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get not-comply jobs")
			return
		}

		// Sort keys for consistent ordering
		sort.Strings(keys)

		// Filter out non-string keys by checking their type
		validKeys := make([]string, 0, len(keys))
		for _, key := range keys {
			// Quick check if key contains valid string data
			_, err := s.redisClient.Get(ctx, key)
			if err == nil {
				validKeys = append(validKeys, key)
			} else if !strings.Contains(err.Error(), "WRONGTYPE") {
				// Include keys that exist but might have other errors (we'll handle them later)
				validKeys = append(validKeys, key)
			}
			// Skip WRONGTYPE keys silently
		}

		// Calculate pagination with valid keys
		totalCount := len(validKeys)
		keys = validKeys
		startIndex := offset
		endIndex := offset + limit

		if startIndex >= totalCount {
			// Return empty result if offset is beyond available data
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"status":      "ok",
				"worker_type": workerType,
				"total_count": totalCount,
				"count":       0,
				"offset":      offset,
				"limit":       limit,
				"jobs":        []map[string]interface{}{},
			})
			return
		}

		if endIndex > totalCount {
			endIndex = totalCount
		}

		// Get paginated keys
		paginatedKeys := keys[startIndex:endIndex]

		// Retrieve job data for each key
		jobs := make([]map[string]interface{}, 0, len(paginatedKeys))
		for _, key := range paginatedKeys {
			value, err := s.redisClient.Get(ctx, key)
			if err != nil {
				// Skip keys that are not string values or don't exist
				if strings.Contains(err.Error(), "WRONGTYPE") {
					s.log.WithField("key", key).Debug("Skipping non-string Redis key")
					continue
				}
				s.log.WithError(err).WithField("key", key).Warn("Failed to get not-comply job data")
				continue
			}

			// Parse JSON data
			var jobData map[string]interface{}
			if err := json.Unmarshal([]byte(value), &jobData); err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to parse not-comply job data")
				continue
			}

			// Add key information to job data
			jobData["redis_key"] = key
			jobs = append(jobs, jobData)
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"worker_type": workerType,
			"total_count": totalCount,
			"count":       len(jobs),
			"offset":      offset,
			"limit":       limit,
			"jobs":        jobs,
		})
	}
}

// handleGetNotComplyJobsByWorkerTypeAndDate handles the endpoint to get not-comply jobs by worker type and date
func (s *Server) handleGetNotComplyJobsByWorkerTypeAndDate() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type and date from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]
		date := vars["date"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Parse query parameters
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")

		// Set default values
		limit := 100
		offset := 0

		// Parse limit
		if limitStr != "" {
			if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		// Parse offset
		if offsetStr != "" {
			if parsedOffset, err := strconv.Atoi(offsetStr); err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Get not-comply jobs keys for specific worker type and date
		pattern := fmt.Sprintf("not-comply-job:%s:%s:*", workerType, date)
		keys, err := s.redisClient.GetKeysByPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to get not-comply jobs keys")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get not-comply jobs")
			return
		}

		// Sort keys for consistent ordering
		sort.Strings(keys)

		// Filter out non-string keys by checking their type
		validKeys := make([]string, 0, len(keys))
		for _, key := range keys {
			// Quick check if key contains valid string data
			_, err := s.redisClient.Get(ctx, key)
			if err == nil {
				validKeys = append(validKeys, key)
			} else if !strings.Contains(err.Error(), "WRONGTYPE") {
				// Include keys that exist but might have other errors (we'll handle them later)
				validKeys = append(validKeys, key)
			}
			// Skip WRONGTYPE keys silently
		}

		// Calculate pagination with valid keys
		totalCount := len(validKeys)
		keys = validKeys
		startIndex := offset
		endIndex := offset + limit

		if startIndex >= totalCount {
			// Return empty result if offset is beyond available data
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(map[string]interface{}{
				"status":      "ok",
				"worker_type": workerType,
				"date":        date,
				"total_count": totalCount,
				"count":       0,
				"offset":      offset,
				"limit":       limit,
				"jobs":        []map[string]interface{}{},
			})
			return
		}

		if endIndex > totalCount {
			endIndex = totalCount
		}

		// Get paginated keys
		paginatedKeys := keys[startIndex:endIndex]

		// Retrieve job data for each key
		jobs := make([]map[string]interface{}, 0, len(paginatedKeys))
		for _, key := range paginatedKeys {
			value, err := s.redisClient.Get(ctx, key)
			if err != nil {
				// Skip keys that are not string values or don't exist
				if strings.Contains(err.Error(), "WRONGTYPE") {
					s.log.WithField("key", key).Debug("Skipping non-string Redis key")
					continue
				}
				s.log.WithError(err).WithField("key", key).Warn("Failed to get not-comply job data")
				continue
			}

			// Parse JSON data
			var jobData map[string]interface{}
			if err := json.Unmarshal([]byte(value), &jobData); err != nil {
				s.log.WithError(err).WithField("key", key).Warn("Failed to parse not-comply job data")
				continue
			}

			// Add key information to job data
			jobData["redis_key"] = key
			jobs = append(jobs, jobData)
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"worker_type": workerType,
			"date":        date,
			"total_count": totalCount,
			"count":       len(jobs),
			"offset":      offset,
			"limit":       limit,
			"jobs":        jobs,
		})
	}
}

// handleGetNotComplyJobByWorkerTypeDateAndIMEI handles the endpoint to get a specific not-comply job
func (s *Server) handleGetNotComplyJobByWorkerTypeDateAndIMEI() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get parameters from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]
		date := vars["date"]
		imei := vars["imei"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		// Construct the exact key
		key := fmt.Sprintf("not-comply-job:%s:%s:%s", workerType, date, imei)

		// Get the job data
		value, err := s.redisClient.Get(ctx, key)
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				s.sendErrorResponse(w, http.StatusNotFound, "Not-comply job not found")
				return
			}
			if strings.Contains(err.Error(), "WRONGTYPE") {
				s.sendErrorResponse(w, http.StatusNotFound, "Not-comply job not found (invalid data type)")
				return
			}
			s.log.WithError(err).WithField("key", key).Error("Failed to get not-comply job data")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get not-comply job")
			return
		}

		// Parse JSON data
		var jobData map[string]interface{}
		if err := json.Unmarshal([]byte(value), &jobData); err != nil {
			s.log.WithError(err).WithField("key", key).Error("Failed to parse not-comply job data")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to parse not-comply job data")
			return
		}

		// Add key information to job data
		jobData["redis_key"] = key

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"worker_type": workerType,
			"date":        date,
			"imei":        imei,
			"job":         jobData,
		})
	}
}

// handleDeleteNotComplyJobs handles the endpoint to delete all not-comply jobs
func (s *Server) handleDeleteNotComplyJobs() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Delete all not-comply jobs keys
		pattern := "not-comply-job:*"
		deleted, err := s.redisClient.DeleteByKeyPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to delete not-comply jobs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete not-comply jobs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":  "ok",
			"deleted": deleted,
			"message": fmt.Sprintf("Successfully deleted %d not-comply jobs", deleted),
		})
	}
}

// handleDeleteNotComplyJobsByWorkerType handles the endpoint to delete not-comply jobs by worker type
func (s *Server) handleDeleteNotComplyJobsByWorkerType() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get worker type from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Delete not-comply jobs keys for specific worker type
		pattern := fmt.Sprintf("not-comply-job:%s:*", workerType)
		deleted, err := s.redisClient.DeleteByKeyPattern(ctx, pattern)
		if err != nil {
			s.log.WithError(err).Error("Failed to delete not-comply jobs")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete not-comply jobs")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"worker_type": workerType,
			"deleted":     deleted,
			"message":     fmt.Sprintf("Successfully deleted %d not-comply jobs for worker type %s", deleted, workerType),
		})
	}
}

// handleDeleteNotComplyJobByWorkerTypeDateAndIMEI handles the endpoint to delete a specific not-comply job
func (s *Server) handleDeleteNotComplyJobByWorkerTypeDateAndIMEI() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get parameters from URL
		vars := mux.Vars(r)
		workerType := vars["worker_type"]
		date := vars["date"]
		imei := vars["imei"]

		// Check if Redis client is available
		if s.redisClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Redis client not available")
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		// Construct the exact key
		key := fmt.Sprintf("not-comply-job:%s:%s:%s", workerType, date, imei)

		// Check if the key exists first
		_, err := s.redisClient.Get(ctx, key)
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				s.sendErrorResponse(w, http.StatusNotFound, "Not-comply job not found")
				return
			}
			if strings.Contains(err.Error(), "WRONGTYPE") {
				s.sendErrorResponse(w, http.StatusNotFound, "Not-comply job not found (invalid data type)")
				return
			}
			s.log.WithError(err).WithField("key", key).Error("Failed to check not-comply job existence")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to check not-comply job")
			return
		}

		// Delete the key
		err = s.redisClient.DeleteKey(ctx, key)
		if err != nil {
			s.log.WithError(err).WithField("key", key).Error("Failed to delete not-comply job")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to delete not-comply job")
			return
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":      "ok",
			"worker_type": workerType,
			"date":        date,
			"imei":        imei,
			"message":     fmt.Sprintf("Successfully deleted not-comply job for %s on %s with IMEI %s", workerType, date, imei),
		})
	}
}
