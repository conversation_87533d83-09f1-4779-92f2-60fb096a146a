package api

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/user/workers/config"
	"github.com/user/workers/pkg/logger"
)

// RabbitMQManagementClient represents a client for RabbitMQ Management API
type RabbitMQManagementClient struct {
	baseURL    string
	username   string
	password   string
	httpClient *http.Client
	log        *logger.Logger
}

// NewRabbitMQManagementClient creates a new RabbitMQ Management API client
func NewRabbitMQManagementClient(cfg *config.Config, log *logger.Logger) *RabbitMQManagementClient {
	baseURL := fmt.Sprintf("http://%s:%s/api", cfg.RabbitMQ.Host, cfg.RabbitMQ.ManagementPort)
	
	return &RabbitMQManagementClient{
		baseURL:  baseURL,
		username: cfg.RabbitMQ.User,
		password: cfg.RabbitMQ.Password,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		log: log,
	}
}

// makeRequest makes an HTTP request to the RabbitMQ Management API
func (c *RabbitMQManagementClient) makeRequest(ctx context.Context, endpoint string) ([]byte, error) {
	url := fmt.Sprintf("%s%s", c.baseURL, endpoint)
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	req.SetBasicAuth(c.username, c.password)
	req.Header.Set("Content-Type", "application/json")
	
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d", resp.StatusCode)
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	
	return body, nil
}

// GetOverview gets the RabbitMQ overview information
func (c *RabbitMQManagementClient) GetOverview(ctx context.Context) (map[string]interface{}, error) {
	data, err := c.makeRequest(ctx, "/overview")
	if err != nil {
		return nil, err
	}
	
	var overview map[string]interface{}
	if err := json.Unmarshal(data, &overview); err != nil {
		return nil, fmt.Errorf("failed to unmarshal overview: %w", err)
	}
	
	return overview, nil
}

// GetQueues gets all queues
func (c *RabbitMQManagementClient) GetQueues(ctx context.Context) ([]map[string]interface{}, error) {
	data, err := c.makeRequest(ctx, "/queues")
	if err != nil {
		return nil, err
	}
	
	var queues []map[string]interface{}
	if err := json.Unmarshal(data, &queues); err != nil {
		return nil, fmt.Errorf("failed to unmarshal queues: %w", err)
	}
	
	return queues, nil
}

// GetQueue gets a specific queue by name
func (c *RabbitMQManagementClient) GetQueue(ctx context.Context, vhost, name string) (map[string]interface{}, error) {
	endpoint := fmt.Sprintf("/queues/%s/%s", vhost, name)
	data, err := c.makeRequest(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	
	var queue map[string]interface{}
	if err := json.Unmarshal(data, &queue); err != nil {
		return nil, fmt.Errorf("failed to unmarshal queue: %w", err)
	}
	
	return queue, nil
}

// GetExchanges gets all exchanges
func (c *RabbitMQManagementClient) GetExchanges(ctx context.Context) ([]map[string]interface{}, error) {
	data, err := c.makeRequest(ctx, "/exchanges")
	if err != nil {
		return nil, err
	}
	
	var exchanges []map[string]interface{}
	if err := json.Unmarshal(data, &exchanges); err != nil {
		return nil, fmt.Errorf("failed to unmarshal exchanges: %w", err)
	}
	
	return exchanges, nil
}

// GetExchange gets a specific exchange by name
func (c *RabbitMQManagementClient) GetExchange(ctx context.Context, vhost, name string) (map[string]interface{}, error) {
	endpoint := fmt.Sprintf("/exchanges/%s/%s", vhost, name)
	data, err := c.makeRequest(ctx, endpoint)
	if err != nil {
		return nil, err
	}
	
	var exchange map[string]interface{}
	if err := json.Unmarshal(data, &exchange); err != nil {
		return nil, fmt.Errorf("failed to unmarshal exchange: %w", err)
	}
	
	return exchange, nil
}

// GetBindings gets all bindings
func (c *RabbitMQManagementClient) GetBindings(ctx context.Context) ([]map[string]interface{}, error) {
	data, err := c.makeRequest(ctx, "/bindings")
	if err != nil {
		return nil, err
	}
	
	var bindings []map[string]interface{}
	if err := json.Unmarshal(data, &bindings); err != nil {
		return nil, fmt.Errorf("failed to unmarshal bindings: %w", err)
	}
	
	return bindings, nil
}

// GetConnections gets all connections
func (c *RabbitMQManagementClient) GetConnections(ctx context.Context) ([]map[string]interface{}, error) {
	data, err := c.makeRequest(ctx, "/connections")
	if err != nil {
		return nil, err
	}
	
	var connections []map[string]interface{}
	if err := json.Unmarshal(data, &connections); err != nil {
		return nil, fmt.Errorf("failed to unmarshal connections: %w", err)
	}
	
	return connections, nil
}

// GetChannels gets all channels
func (c *RabbitMQManagementClient) GetChannels(ctx context.Context) ([]map[string]interface{}, error) {
	data, err := c.makeRequest(ctx, "/channels")
	if err != nil {
		return nil, err
	}
	
	var channels []map[string]interface{}
	if err := json.Unmarshal(data, &channels); err != nil {
		return nil, fmt.Errorf("failed to unmarshal channels: %w", err)
	}
	
	return channels, nil
}

// GetConsumers gets all consumers
func (c *RabbitMQManagementClient) GetConsumers(ctx context.Context) ([]map[string]interface{}, error) {
	data, err := c.makeRequest(ctx, "/consumers")
	if err != nil {
		return nil, err
	}
	
	var consumers []map[string]interface{}
	if err := json.Unmarshal(data, &consumers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal consumers: %w", err)
	}
	
	return consumers, nil
}

// TestConnection tests the connection to RabbitMQ Management API
func (c *RabbitMQManagementClient) TestConnection(ctx context.Context) error {
	_, err := c.makeRequest(ctx, "/overview")
	return err
}
