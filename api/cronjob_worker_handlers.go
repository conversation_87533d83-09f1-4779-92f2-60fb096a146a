package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/user/workers/internal/cronjob"
	"github.com/user/workers/internal/worker"
)

// RefreshCronjobWorkerRequest represents a request to refresh a cronjob worker
type RefreshCronjobWorkerRequest struct {
	WorkerType string `json:"worker_type" validate:"required,oneof=api_pull db_clone api_pull_caveo caveo_payload_maker caveo_to_influx geof_division geof_estate"`
}

// handleRefreshCronjobWorker handles the endpoint to refresh a cronjob worker
func (s *Server) handleRefreshCronjobWorker() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if dispatcher is available
		if s.dispatcher == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Dispatcher not available")
			return
		}

		// Parse request body
		var request RefreshCronjobWorkerRequest
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Validate request using validator
		if err := s.validator.Validate(request); err != nil {
			s.log.WithError(err).WithField("worker_type", request.WorkerType).Error("Invalid refresh worker request")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Get worker pool from dispatcher
		pool := s.dispatcher.GetWorkerPool()
		if pool == nil {
			s.log.Error("Worker pool not available")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Worker pool not available")
			return
		}

		// Get pool manager
		poolManager, ok := pool.(*worker.PoolManager)
		if !ok {
			s.log.Error("Worker pool is not a PoolManager")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Worker pool is not a PoolManager")
			return
		}

		// Update worker count based on configuration
		if err := poolManager.UpdateWorkerCount(); err != nil {
			s.log.WithError(err).Error("Failed to update worker count")
			s.sendErrorResponse(w, http.StatusInternalServerError, fmt.Sprintf("Failed to update worker count: %s", err.Error()))
			return
		}

		// Reload cronjobs if cronjob manager is available
		if s.cronjobManager != nil {
			// Get all cronjobs
			cronjobs, err := s.cronjobManager.ListCronjobs()
			if err != nil {
				s.log.WithError(err).Error("Failed to list cronjobs")
				s.sendErrorResponse(w, http.StatusInternalServerError, fmt.Sprintf("Failed to list cronjobs: %s", err.Error()))
				return
			}

			// Reschedule enabled cronjobs
			refreshedCount := 0
			for _, c := range cronjobs {
				if c.Enabled && c.WorkerType == request.WorkerType {
					// Get the latest version of the cronjob
					latestCronjob, err := s.cronjobManager.GetCronjob(c.Name)
					if err != nil {
						s.log.WithError(err).WithField("cronjob", c.Name).Error("Failed to get cronjob for refresh")
						continue
					}

					// Update the cronjob to ensure it's rescheduled
					if err := s.cronjobManager.UpdateCronjob(latestCronjob); err != nil {
						s.log.WithError(err).WithField("cronjob", c.Name).Error("Failed to update cronjob")
						// Continue with other cronjobs
					} else {
						refreshedCount++
						s.log.WithField("cronjob", c.Name).Info("Cronjob refreshed successfully")
					}
				}
			}

			s.log.WithFields(map[string]interface{}{
				"worker_type":     request.WorkerType,
				"refreshed_count": refreshedCount,
				"total_cronjobs":  len(cronjobs),
			}).Info("Cronjobs refreshed")
		}

		// Calculate refreshed count
		refreshedCount := 0
		totalCount := 0
		if s.cronjobManager != nil {
			cronjobs, _ := s.cronjobManager.ListCronjobs()
			for _, c := range cronjobs {
				if c.WorkerType == request.WorkerType {
					totalCount++
					if c.Enabled {
						refreshedCount++
					}
				}
			}
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":          "ok",
			"message":         fmt.Sprintf("Worker type '%s' refreshed successfully", request.WorkerType),
			"worker_type":     request.WorkerType,
			"refreshed_count": refreshedCount,
			"total_count":     totalCount,
			"timestamp":       time.Now().Format(time.RFC3339),
		})
	}
}

// handleUpdateCronjobAndRefreshWorker handles the endpoint to update a cronjob and refresh the worker
func (s *Server) handleUpdateCronjobAndRefreshWorker() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Check if cronjob manager is available
		if s.cronjobManager == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "Cronjob manager not available")
			return
		}

		// Get cronjob name from URL
		vars := mux.Vars(r)
		name := vars["name"]

		// Parse request body
		var request cronjob.Cronjob
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			s.log.WithError(err).Error("Failed to decode request body")
			s.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
			return
		}

		// Ensure name in URL matches name in request
		request.Name = name

		// Validate cron expression to ensure it has exactly 6 fields
		if err := ValidateCronExpression(request.Schedule); err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Invalid cron expression")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Invalid cron expression: %s", err.Error()))
			return
		}

		// Validate request using validator
		cronjobValidation := CronjobValidation{
			Name:        request.Name,
			Description: request.Description,
			Enabled:     request.Enabled,
			Schedule:    request.Schedule,
			WorkerType:  request.WorkerType,
			Payload:     request.Payload,
			Variables:   request.Variables,
			RetryPolicy: RetryPolicyValidation{
				Attempts: request.RetryPolicy.Attempts,
				Delay:    request.RetryPolicy.Delay,
			},
			Timeout: request.Timeout,
		}

		if err := s.validator.Validate(cronjobValidation); err != nil {
			s.log.WithError(err).WithField("name", request.Name).Error("Invalid cronjob request")
			s.sendErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Validation error: %s", err.Error()))
			return
		}

		// Update cronjob
		if err := s.cronjobManager.UpdateCronjob(&request); err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to update cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to update cronjob")
			return
		}

		// Get updated cronjob
		updatedCronjob, err := s.cronjobManager.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to get updated cronjob")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get updated cronjob")
			return
		}

		// Now explicitly refresh the worker pool
		// Get worker pool from dispatcher
		pool := s.dispatcher.GetWorkerPool()
		if pool == nil {
			s.log.Error("Worker pool not available")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Worker pool not available")
			return
		}

		// Get pool manager
		poolManager, ok := pool.(*worker.PoolManager)
		if !ok {
			s.log.Error("Worker pool is not a PoolManager")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Worker pool is not a PoolManager")
			return
		}

		// Update worker count based on configuration
		if err := poolManager.UpdateWorkerCount(); err != nil {
			s.log.WithError(err).Error("Failed to update worker count")
			s.sendErrorResponse(w, http.StatusInternalServerError, fmt.Sprintf("Failed to update worker count: %s", err.Error()))
			return
		}

		// Explicitly reload the cronjob in the manager by getting it and updating it again
		reloadCronjob, err := s.cronjobManager.GetCronjob(name)
		if err != nil {
			s.log.WithError(err).WithField("name", name).Error("Failed to get cronjob for reload")
			// Continue anyway, as the cronjob was updated in Redis
		} else {
			// Update the cronjob again to ensure it's rescheduled
			if err := s.cronjobManager.UpdateCronjob(reloadCronjob); err != nil {
				s.log.WithError(err).WithField("name", name).Error("Failed to reload cronjob")
				// Continue anyway, as the cronjob was updated in Redis
			}
		}

		// Convert to response format
		response := convertCronjobToResponse(updatedCronjob)

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status":    "ok",
			"message":   "Cronjob updated and worker refreshed successfully",
			"cronjob":   response,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}
}
