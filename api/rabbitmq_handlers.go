package api

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// handleRabbitMQOverview handles the RabbitMQ overview endpoint
func (s *Server) handleRabbitMQOverview() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		overview, err := s.rabbitMQClient.GetOverview(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ overview")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ overview")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"data":   overview,
		})
	}
}

// handleRabbitMQQueues handles the RabbitMQ queues endpoint
func (s *Server) handleRabbitMQQueues() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		queues, err := s.rabbitMQClient.GetQueues(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ queues")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ queues")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"count":  len(queues),
			"data":   queues,
		})
	}
}

// handleRabbitMQQueue handles the RabbitMQ specific queue endpoint
func (s *Server) handleRabbitMQQueue() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		vars := mux.Vars(r)
		queueName := vars["name"]
		vhost := r.URL.Query().Get("vhost")
		if vhost == "" {
			vhost = "%2F" // Default vhost (URL encoded /)
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		queue, err := s.rabbitMQClient.GetQueue(ctx, vhost, queueName)
		if err != nil {
			s.log.WithError(err).WithField("queue", queueName).Error("Failed to get RabbitMQ queue")
			s.sendErrorResponse(w, http.StatusNotFound, "Queue not found or failed to get queue details")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"data":   queue,
		})
	}
}

// handleRabbitMQExchanges handles the RabbitMQ exchanges endpoint
func (s *Server) handleRabbitMQExchanges() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		exchanges, err := s.rabbitMQClient.GetExchanges(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ exchanges")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ exchanges")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"count":  len(exchanges),
			"data":   exchanges,
		})
	}
}

// handleRabbitMQExchange handles the RabbitMQ specific exchange endpoint
func (s *Server) handleRabbitMQExchange() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		vars := mux.Vars(r)
		exchangeName := vars["name"]
		vhost := r.URL.Query().Get("vhost")
		if vhost == "" {
			vhost = "%2F" // Default vhost (URL encoded /)
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		exchange, err := s.rabbitMQClient.GetExchange(ctx, vhost, exchangeName)
		if err != nil {
			s.log.WithError(err).WithField("exchange", exchangeName).Error("Failed to get RabbitMQ exchange")
			s.sendErrorResponse(w, http.StatusNotFound, "Exchange not found or failed to get exchange details")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"data":   exchange,
		})
	}
}

// handleRabbitMQBindings handles the RabbitMQ bindings endpoint
func (s *Server) handleRabbitMQBindings() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		bindings, err := s.rabbitMQClient.GetBindings(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ bindings")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ bindings")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"count":  len(bindings),
			"data":   bindings,
		})
	}
}

// handleRabbitMQConnections handles the RabbitMQ connections endpoint
func (s *Server) handleRabbitMQConnections() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		connections, err := s.rabbitMQClient.GetConnections(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ connections")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ connections")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"count":  len(connections),
			"data":   connections,
		})
	}
}

// handleRabbitMQChannels handles the RabbitMQ channels endpoint
func (s *Server) handleRabbitMQChannels() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		channels, err := s.rabbitMQClient.GetChannels(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ channels")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ channels")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"count":  len(channels),
			"data":   channels,
		})
	}
}

// handleRabbitMQConsumers handles the RabbitMQ consumers endpoint
func (s *Server) handleRabbitMQConsumers() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		consumers, err := s.rabbitMQClient.GetConsumers(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ consumers")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ consumers")
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"count":  len(consumers),
			"data":   consumers,
		})
	}
}

// handleRabbitMQMessageStats handles the RabbitMQ message statistics endpoint
func (s *Server) handleRabbitMQMessageStats() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if s.rabbitMQClient == nil {
			s.sendErrorResponse(w, http.StatusServiceUnavailable, "RabbitMQ Management client not available")
			return
		}

		ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
		defer cancel()

		// Get overview for message statistics
		overview, err := s.rabbitMQClient.GetOverview(ctx)
		if err != nil {
			s.log.WithError(err).Error("Failed to get RabbitMQ overview for message stats")
			s.sendErrorResponse(w, http.StatusInternalServerError, "Failed to get RabbitMQ message statistics")
			return
		}

		// Extract message statistics
		messageStats := map[string]interface{}{
			"timestamp": time.Now().Format(time.RFC3339),
		}

		if stats, ok := overview["message_stats"].(map[string]interface{}); ok {
			messageStats["message_stats"] = stats
		}

		if queueTotals, ok := overview["queue_totals"].(map[string]interface{}); ok {
			messageStats["queue_totals"] = queueTotals
		}

		if objectTotals, ok := overview["object_totals"].(map[string]interface{}); ok {
			messageStats["object_totals"] = objectTotals
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "ok",
			"data":   messageStats,
		})
	}
}
