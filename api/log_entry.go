package api

// LogEntry represents a log entry in the API
type LogEntry struct {
	Timestamp  string                 `json:"timestamp"`
	WorkerType string                 `json:"worker_type"`
	Status     string                 `json:"status"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// LogResponse represents a response containing log entries
type LogResponse struct {
	Total   int        `json:"total"`
	Page    int        `json:"page"`
	PerPage int        `json:"per_page"`
	Logs    []LogEntry `json:"logs"`
}
