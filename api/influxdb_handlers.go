package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// InfluxDBCountResponse represents the response for count queries
type InfluxDBCountResponse struct {
	IMEI      string `json:"imei"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Count     int    `json:"count"`
}

// InfluxDBTrackingResponse represents the response for tracking queries
type InfluxDBTrackingResponse struct {
	IMEI string                  `json:"imei"`
	Date string                  `json:"date"`
	Data []InfluxDBTrackingPoint `json:"data"`
}

// InfluxDBTrackingPoint represents a single tracking point
type InfluxDBTrackingPoint struct {
	Timestamp string  `json:"timestamp"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// handleGetInfluxDBCountByIMEIAndDateRange handles GET /api/influxdb/count/{imei}
func (s *Server) handleGetInfluxDBCountByIMEIAndDateRange() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

		// Handle preflight request
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusOK)
			return
		}

		// Get IMEI from URL path
		vars := mux.Vars(r)
		imei := vars["imei"]
		if imei == "" {
			http.Error(w, "IMEI is required", http.StatusBadRequest)
			return
		}

		// Get query parameters
		startDateStr := r.URL.Query().Get("startDate")
		endDateStr := r.URL.Query().Get("endDate")

		if startDateStr == "" || endDateStr == "" {
			http.Error(w, "startDate and endDate parameters are required", http.StatusBadRequest)
			return
		}

		// Parse dates
		startDate, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			http.Error(w, fmt.Sprintf("Invalid startDate format. Use YYYY-MM-DD: %v", err), http.StatusBadRequest)
			return
		}

		endDate, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			http.Error(w, fmt.Sprintf("Invalid endDate format. Use YYYY-MM-DD: %v", err), http.StatusBadRequest)
			return
		}

		// Validate date range
		if endDate.Before(startDate) {
			http.Error(w, "endDate cannot be before startDate", http.StatusBadRequest)
			return
		}

		// Set end date to end of day
		endDate = endDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)

		// Check if InfluxDB client is available
		if s.influxClient == nil {
			http.Error(w, "InfluxDB client not available", http.StatusServiceUnavailable)
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Query InfluxDB for count
		count, err := s.influxClient.CountRecordsByIMEIAndDateRange(ctx, imei, startDate, endDate)
		if err != nil {
			s.log.WithError(err).WithField("imei", imei).Error("Failed to count records from InfluxDB")
			http.Error(w, fmt.Sprintf("Failed to query InfluxDB: %v", err), http.StatusInternalServerError)
			return
		}

		// Create response
		response := InfluxDBCountResponse{
			IMEI:      imei,
			StartDate: startDateStr,
			EndDate:   endDateStr,
			Count:     count,
		}

		// Set response headers
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)

		// Encode and send response
		if err := json.NewEncoder(w).Encode(response); err != nil {
			s.log.WithError(err).Error("Failed to encode response")
			http.Error(w, "Failed to encode response", http.StatusInternalServerError)
			return
		}

		s.log.WithFields(map[string]interface{}{
			"imei":       imei,
			"start_date": startDateStr,
			"end_date":   endDateStr,
			"count":      count,
		}).Info("Successfully retrieved InfluxDB count")
	}
}

// handleGetInfluxDBTrackingByIMEIAndDate handles GET /api/influxdb/tracking/{imei}/{date}
func (s *Server) handleGetInfluxDBTrackingByIMEIAndDate() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

		// Handle preflight request
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusOK)
			return
		}

		// Get IMEI and date from URL path
		vars := mux.Vars(r)
		imei := vars["imei"]
		dateStr := vars["date"]

		if imei == "" {
			http.Error(w, "IMEI is required", http.StatusBadRequest)
			return
		}

		if dateStr == "" {
			http.Error(w, "Date is required", http.StatusBadRequest)
			return
		}

		// Parse date
		date, err := time.Parse("2006-01-02", dateStr)
		if err != nil {
			http.Error(w, fmt.Sprintf("Invalid date format. Use YYYY-MM-DD: %v", err), http.StatusBadRequest)
			return
		}

		// Check if InfluxDB client is available
		if s.influxClient == nil {
			http.Error(w, "InfluxDB client not available", http.StatusServiceUnavailable)
			return
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
		defer cancel()

		// Query InfluxDB for tracking data
		trackingPoints, err := s.influxClient.GetTrackingDataByIMEIAndDate(ctx, imei, date)
		if err != nil {
			s.log.WithError(err).WithFields(map[string]interface{}{
				"imei": imei,
				"date": dateStr,
			}).Error("Failed to retrieve tracking data from InfluxDB")
			http.Error(w, fmt.Sprintf("Failed to query InfluxDB: %v", err), http.StatusInternalServerError)
			return
		}

		// Convert tracking points to response format
		var responseData []InfluxDBTrackingPoint
		for _, point := range trackingPoints {
			responseData = append(responseData, InfluxDBTrackingPoint{
				Timestamp: point.Timestamp.Format(time.RFC3339),
				Latitude:  point.Latitude,
				Longitude: point.Longitude,
			})
		}

		// Create response
		response := InfluxDBTrackingResponse{
			IMEI: imei,
			Date: dateStr,
			Data: responseData,
		}

		// Set response headers
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)

		// Encode and send response
		if err := json.NewEncoder(w).Encode(response); err != nil {
			s.log.WithError(err).Error("Failed to encode response")
			http.Error(w, "Failed to encode response", http.StatusInternalServerError)
			return
		}

		s.log.WithFields(map[string]interface{}{
			"imei":        imei,
			"date":        dateStr,
			"data_points": len(responseData),
		}).Info("Successfully retrieved InfluxDB tracking data")
	}
}
