package api

// JobRequest represents a job request from the API
type JobRequest struct {
	WorkerType  string      `json:"worker_type"`
	Config      interface{} `json:"config"`
	Payload     interface{} `json:"payload,omitempty"`
	Table       string      `json:"table,omitempty"`
	Source      string      `json:"source,omitempty"`
	Destination string      `json:"destination,omitempty"`
	Metadata    interface{} `json:"metadata,omitempty"`
}

// CaveoAPIDeviceConfig represents the device configuration for a Caveo API job
type CaveoAPIDeviceConfig struct {
	PSM      string      `json:"psm"`
	Estate   string      `json:"estate"`
	Division string      `json:"division"`
	Ident    interface{} `json:"ident"` // Accept any type for ident
	NIK      string      `json:"nik"`
	Name     string      `json:"name"`
	Type     string      `json:"type"`
}

// CaveoAPIJobConfig represents the configuration for a Caveo API job
type CaveoAPIJobConfig struct {
	Device    CaveoAPIDeviceConfig `json:"device"`
	TimeStart string               `json:"timeStart"`
	TimeEnd   string               `json:"timeEnd"`
}

// APIJobConfig represents the configuration for an API job
type APIJobConfig struct {
	Endpoint string            `json:"endpoint"`
	Method   string            `json:"method"`
	Headers  map[string]string `json:"headers"`
	Body     interface{}       `json:"body,omitempty"`
}

// DBJobConfig represents the configuration for a DB job
type DBJobConfig struct {
	Table      string   `json:"table"`
	Columns    []string `json:"columns"`
	Conditions string   `json:"conditions"`
	BatchSize  int      `json:"batch_size"`
}
