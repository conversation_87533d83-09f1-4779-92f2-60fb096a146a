package api

// APIPullPayload represents the payload for the API pull worker
type APIPullPayload struct {
	WorkerType      string                 `json:"worker_type" jsonschema:"required,description=Must be 'api_pull' (required)"`
	Config          APIPullPayloadConfig   `json:"config" jsonschema:"required,description=Configuration for the API pull worker (required)"`
	Metadata        APIPullPayloadMetadata `json:"metadata,omitempty" jsonschema:"description=Additional metadata for the job"`
	RescheduleCount int                    `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// APIPullPayloadConfig represents the configuration for the API pull worker
type APIPullPayloadConfig struct {
	Endpoint string                 `json:"endpoint" jsonschema:"required,description=API endpoint to call (required)"`
	Method   string                 `json:"method" jsonschema:"required,description=HTTP method to use (GET, POST, etc.) (required)"`
	Headers  map[string]string      `json:"headers" jsonschema:"required,description=HTTP headers to include in the request (required)"`
	Body     map[string]interface{} `json:"body,omitempty" jsonschema:"description=Request body for POST/PUT requests"`
	URL      string                 `json:"url" jsonschema:"required,description=Full URL to call (required)"`
	Params   map[string]interface{} `json:"params,omitempty" jsonschema:"description=Query parameters for GET requests"`
	Auth     *APIPullAuth           `json:"auth,omitempty" jsonschema:"description=Authentication credentials if needed"`
}

// APIPullAuth represents the authentication configuration for the API pull worker
type APIPullAuth struct {
	Username string `json:"username" jsonschema:"description=Username for basic authentication"`
	Password string `json:"password" jsonschema:"description=Password for basic authentication"`
}

// APIPullPayloadMetadata represents the metadata for the API pull worker
type APIPullPayloadMetadata struct {
	PSM      string `json:"psm,omitempty" jsonschema:"description=PSM identifier"`
	Estate   string `json:"estate,omitempty" jsonschema:"description=Estate identifier"`
	Division string `json:"division,omitempty" jsonschema:"description=Division identifier"`
	NIK      string `json:"nik,omitempty" jsonschema:"description=NIK identifier"`
	Name     string `json:"name,omitempty" jsonschema:"description=Name identifier"`
}

// CaveoAPIPayload represents the payload for the Caveo API pull worker
type CaveoAPIPayload struct {
	WorkerType      string                `json:"worker_type" jsonschema:"required,description=Must be 'api_pull_caveo' (required)"`
	Config          CaveoAPIPayloadConfig `json:"config" jsonschema:"required,description=Configuration for the Caveo API pull worker (required)"`
	RescheduleCount int                   `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// DeviceConfig represents the device configuration for the Caveo API pull worker
type DeviceConfig struct {
	PSM      string `json:"psm" jsonschema:"required,description=PSM identifier (required)"`
	Estate   string `json:"estate" jsonschema:"required,description=Estate identifier (required)"`
	Division string `json:"division" jsonschema:"required,description=Division identifier (required)"`
	Ident    string `json:"ident" jsonschema:"required,description=Device identifier (required)"`
	NIK      string `json:"nik" jsonschema:"required,description=NIK identifier (required)"`
	Name     string `json:"name" jsonschema:"required,description=Device name (required)"`
	Type     string `json:"type" jsonschema:"required,description=Device type (required)"`
}

// CaveoAPIPayloadConfig represents the configuration for the Caveo API pull worker
type CaveoAPIPayloadConfig struct {
	Device    DeviceConfig `json:"device" jsonschema:"required,description=Device configuration (required)"`
	TimeStart string       `json:"timeStart" jsonschema:"required,description=Start time for data retrieval (required)"`
	TimeEnd   string       `json:"timeEnd" jsonschema:"required,description=End time for data retrieval (required)"`
}

// DBCloningPayload represents the payload for the DB cloning worker
type DBCloningPayload struct {
	WorkerType      string                 `json:"worker_type" jsonschema:"required,description=Must be 'db_clone' (required)"`
	Table           string                 `json:"table" jsonschema:"required,description=Table name to clone (required)"`
	Columns         []string               `json:"columns,omitempty" jsonschema:"description=Specific columns to clone (if empty, all columns will be cloned)"`
	Conditions      string                 `json:"conditions,omitempty" jsonschema:"description=SQL WHERE conditions for filtering data"`
	BatchSize       int                    `json:"batch_size,omitempty" jsonschema:"description=Number of records to process in each batch"`
	Source          string                 `json:"source,omitempty" jsonschema:"description=Source database identifier"`
	Destination     string                 `json:"destination,omitempty" jsonschema:"description=Destination database identifier"`
	Metadata        map[string]interface{} `json:"metadata,omitempty" jsonschema:"description=Additional metadata for the job"`
	RescheduleCount int                    `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// CaveoPayloadMakerPayload represents the payload for the Caveo payload maker worker
type CaveoPayloadMakerPayload struct {
	WorkerType      string                         `json:"worker_type" jsonschema:"required,description=Must be 'caveo_payload_maker' (required)"`
	Config          CaveoPayloadMakerPayloadConfig `json:"config" jsonschema:"required,description=Configuration for the Caveo payload maker worker (required)"`
	RescheduleCount int                            `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// CaveoPayloadMakerPayloadConfig represents the configuration for the Caveo payload maker worker
type CaveoPayloadMakerPayloadConfig struct {
	Message string `json:"message" jsonschema:"required,description=Message to print (required)"`
}

// RescheduleCaveoPayload represents the payload for the reschedule Caveo worker
type RescheduleCaveoPayload struct {
	WorkerType      string                       `json:"worker_type" jsonschema:"required,description=Must be 'reschedule_caveo' (required)"`
	Config          RescheduleCaveoPayloadConfig `json:"config" jsonschema:"required,description=Configuration for the reschedule Caveo worker (required)"`
	RescheduleCount int                          `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// RescheduleCaveoPayloadConfig represents the configuration for the reschedule Caveo worker
type RescheduleCaveoPayloadConfig struct {
	// This worker doesn't need specific configuration as it scans all re-schedule jobs
}

// CaveoToInfluxPayload represents the payload for the Caveo to InfluxDB worker
type CaveoToInfluxPayload struct {
	WorkerType      string                     `json:"worker_type" jsonschema:"required,description=Must be 'caveo_to_influx' (required)"`
	Config          CaveoToInfluxPayloadConfig `json:"config" jsonschema:"required,description=Configuration for the Caveo to InfluxDB worker (required)"`
	RescheduleCount int                        `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// CaveoToInfluxPayloadConfig represents the configuration for the Caveo to InfluxDB worker
type CaveoToInfluxPayloadConfig struct {
	Date string `json:"date" jsonschema:"required,description=Date in YYYY-MM-DD format (required)"`
	IMEI string `json:"imei" jsonschema:"required,description=IMEI identifier (required)"`
}

// GeofDivisionPayload represents the payload for the Geofencing Division worker
type GeofDivisionPayload struct {
	WorkerType      string                    `json:"worker_type" jsonschema:"required,description=Must be 'geof_division' (required)"`
	Config          GeofDivisionPayloadConfig `json:"config" jsonschema:"required,description=Configuration for the Geofencing Division worker (required)"`
	RescheduleCount int                       `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// GeofDivisionPayloadConfig represents the configuration for the Geofencing Division worker
type GeofDivisionPayloadConfig struct {
	Date        string                      `json:"date" jsonschema:"required,description=Date in YYYY-MM-DD format (required)"`
	Device      GeofDivisionDeviceInfo      `json:"device" jsonschema:"required,description=Device information (required)"`
	Division    string                      `json:"division" jsonschema:"required,description=Division name (required)"`
	Tracking    []GeofDivisionTrackingPoint `json:"tracking" jsonschema:"required,description=GPS tracking points (required)"`
	GeojsBlocks []GeofDivisionGeoJSBlock    `json:"geojsBlocks" jsonschema:"required,description=GeoJSON polygon blocks for geofencing (required)"`
}

// GeofDivisionDeviceInfo represents device information for geofencing
type GeofDivisionDeviceInfo struct {
	Division string `json:"division" jsonschema:"required,description=Device division (required)"`
	Estate   string `json:"estate" jsonschema:"required,description=Device estate (required)"`
	IMEI     string `json:"imei" jsonschema:"required,description=Device IMEI (required)"`
	Name     string `json:"name" jsonschema:"required,description=Device name (required)"`
	NIK      string `json:"nik" jsonschema:"required,description=Device NIK (required)"`
	PSM      string `json:"psm" jsonschema:"required,description=Device PSM (required)"`
	Type     string `json:"type" jsonschema:"required,description=Device type (required)"`
}

// GeofDivisionTrackingPoint represents a GPS tracking point
type GeofDivisionTrackingPoint struct {
	Timestamp string  `json:"timestamp" jsonschema:"required,description=Timestamp in ISO format (required)"`
	Latitude  float64 `json:"latitude" jsonschema:"required,description=GPS latitude coordinate (required)"`
	Longitude float64 `json:"longitude" jsonschema:"required,description=GPS longitude coordinate (required)"`
}

// GeofDivisionGeoJSBlock represents a GeoJSON block for geofencing
type GeofDivisionGeoJSBlock struct {
	Type       string                      `json:"type" jsonschema:"required,description=GeoJSON type, typically 'Feature' (required)"`
	Properties GeofDivisionBlockProperties `json:"properties" jsonschema:"required,description=Block properties (required)"`
	Geometry   map[string]interface{}      `json:"geometry" jsonschema:"required,description=GeoJSON geometry object (required)"`
}

// GeofDivisionBlockProperties represents the properties of a GeoJSON block
type GeofDivisionBlockProperties struct {
	Blok        string `json:"blok" jsonschema:"required,description=Block identifier (required)"`
	Divisi      string `json:"divisi" jsonschema:"required,description=Division name (required)"`
	Estate      string `json:"estate" jsonschema:"required,description=Estate name (required)"`
	PeriodeUkur int    `json:"periode_ukur" jsonschema:"required,description=Measurement period (required)"`
	PSM         string `json:"psm" jsonschema:"required,description=PSM identifier (required)"`
	PT          string `json:"pt" jsonschema:"required,description=Company name (required)"`
}

// GeofEstatePayload represents the payload for the Geofencing Estate worker
type GeofEstatePayload struct {
	WorkerType      string                  `json:"worker_type" jsonschema:"required,description=Must be 'geof_estate' (required)"`
	Config          GeofEstatePayloadConfig `json:"config" jsonschema:"required,description=Configuration for the Geofencing Estate worker (required)"`
	RescheduleCount int                     `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// GeofEstatePayloadConfig represents the configuration for the Geofencing Estate worker
type GeofEstatePayloadConfig struct {
	Date      string                    `json:"date" jsonschema:"required,description=Date in YYYY-MM-DD format (required)"`
	Device    GeofEstateDeviceInfo      `json:"device" jsonschema:"required,description=Device information (required)"`
	Tracking  []GeofEstateTrackingPoint `json:"tracking" jsonschema:"required,description=GPS tracking points (required)"`
	GeojsDivs []GeofEstateGeoJSDiv      `json:"geojsDivs" jsonschema:"required,description=GeoJSON estate divisions for geofencing (required)"`
}

// GeofEstateDeviceInfo represents device information for estate geofencing
type GeofEstateDeviceInfo struct {
	Division string `json:"division" jsonschema:"required,description=Device division (required)"`
	Estate   string `json:"estate" jsonschema:"required,description=Device estate (required)"`
	Ident    string `json:"ident" jsonschema:"required,description=Device identifier (required)"`
	Name     string `json:"name" jsonschema:"required,description=Device name (required)"`
	NIK      string `json:"nik" jsonschema:"required,description=Device NIK (required)"`
	PSM      string `json:"psm" jsonschema:"required,description=Device PSM (required)"`
	Type     string `json:"type" jsonschema:"required,description=Device type (required)"`
}

// GeofEstateTrackingPoint represents a GPS tracking point for estate geofencing
type GeofEstateTrackingPoint struct {
	Timestamp string  `json:"timestamp" jsonschema:"required,description=Timestamp in ISO format (required)"`
	Latitude  float64 `json:"latitude" jsonschema:"required,description=GPS latitude coordinate (required)"`
	Longitude float64 `json:"longitude" jsonschema:"required,description=GPS longitude coordinate (required)"`
}

// GeofEstateGeoJSDiv represents a GeoJSON estate division for geofencing
type GeofEstateGeoJSDiv struct {
	Type       string                  `json:"type" jsonschema:"required,description=GeoJSON type, typically 'Feature' (required)"`
	Properties GeofEstateDivProperties `json:"properties" jsonschema:"required,description=Estate division properties (required)"`
	Geometry   map[string]interface{}  `json:"geometry" jsonschema:"required,description=GeoJSON geometry object (required)"`
}

// GeofEstateDivProperties represents the properties of a GeoJSON estate division
type GeofEstateDivProperties struct {
	Divisi      string `json:"divisi" jsonschema:"required,description=Division name (required)"`
	Estate      string `json:"estate" jsonschema:"required,description=Estate name (required)"`
	PeriodeUkur int    `json:"periode_ukur" jsonschema:"required,description=Measurement period (required)"`
	PSM         string `json:"psm" jsonschema:"required,description=PSM identifier (required)"`
	PT          string `json:"pt" jsonschema:"required,description=Company name (required)"`
}

// GeofBlockPayload represents the payload for the Geofencing Block worker
type GeofBlockPayload struct {
	WorkerType      string                 `json:"worker_type" jsonschema:"required,description=Must be 'geof_block' (required)"`
	Config          GeofBlockPayloadConfig `json:"config" jsonschema:"required,description=Configuration for the Geofencing Block worker (required)"`
	RescheduleCount int                    `json:"reschedule_count,omitempty" jsonschema:"description=Number of times this job has been rescheduled"`
}

// GeofBlockPayloadConfig represents the configuration for the Geofencing Block worker
type GeofBlockPayloadConfig struct {
	Date       string                   `json:"date" jsonschema:"required,description=Date in YYYY-MM-DD format (required)"`
	Device     GeofBlockDeviceInfo      `json:"device" jsonschema:"required,description=Device information (required)"`
	Tracking   []GeofBlockTrackingPoint `json:"tracking" jsonschema:"required,description=GPS tracking points (required)"`
	GeojsTrees []GeofBlockGeoJSTree     `json:"geojsTrees" jsonschema:"required,description=GeoJSON tree locations for geofencing (required)"`
}

// GeofBlockDeviceInfo represents device information for block geofencing
type GeofBlockDeviceInfo struct {
	Division string `json:"division" jsonschema:"required,description=Device division (required)"`
	Estate   string `json:"estate" jsonschema:"required,description=Device estate (required)"`
	Ident    string `json:"ident" jsonschema:"required,description=Device identifier (required)"`
	Name     string `json:"name" jsonschema:"required,description=Device name (required)"`
	NIK      string `json:"nik" jsonschema:"required,description=Device NIK (required)"`
	PSM      string `json:"psm" jsonschema:"required,description=Device PSM (required)"`
	Type     string `json:"type" jsonschema:"required,description=Device type (required)"`
}

// GeofBlockTrackingPoint represents a GPS tracking point for block geofencing
type GeofBlockTrackingPoint struct {
	Timestamp string  `json:"timestamp" jsonschema:"required,description=Timestamp in ISO format (required)"`
	Latitude  float64 `json:"latitude" jsonschema:"required,description=GPS latitude coordinate (required)"`
	Longitude float64 `json:"longitude" jsonschema:"required,description=GPS longitude coordinate (required)"`
}

// GeofBlockGeoJSTree represents a GeoJSON tree location for geofencing
type GeofBlockGeoJSTree struct {
	Type       string                  `json:"type" jsonschema:"required,description=GeoJSON type, typically 'Feature' (required)"`
	Properties GeofBlockTreeProperties `json:"properties" jsonschema:"required,description=Tree properties (required)"`
	Geometry   map[string]interface{}  `json:"geometry" jsonschema:"required,description=GeoJSON geometry object (required)"`
}

// GeofBlockTreeProperties represents the properties of a GeoJSON tree
type GeofBlockTreeProperties struct {
	PSM         string      `json:"psm" jsonschema:"required,description=PSM identifier (required)"`
	Region      string      `json:"region" jsonschema:"required,description=Region name (required)"`
	Estate      string      `json:"estate" jsonschema:"required,description=Estate name (required)"`
	Divisi      string      `json:"divisi" jsonschema:"required,description=Division name (required)"`
	Blok        string      `json:"blok" jsonschema:"required,description=Block identifier (required)"`
	PointX      float64     `json:"point_x" jsonschema:"required,description=Tree X coordinate (longitude) (required)"`
	PointY      float64     `json:"point_y" jsonschema:"required,description=Tree Y coordinate (latitude) (required)"`
	PeriodeUkur interface{} `json:"periode_ukur" jsonschema:"description=Measurement period (can be null)"`
}
