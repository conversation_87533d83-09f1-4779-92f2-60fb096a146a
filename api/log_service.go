package api

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/redis"
)

// LogService handles querying log data from Redis
type LogService struct {
	redisClient *redis.Client
	mu          sync.RWMutex
	log         *logger.Logger
	ctx         context.Context
}

// NewLogService creates a new LogService instance
func NewLogService(redisConfig redis.Config, log *logger.Logger) *LogService {
	// Create context
	ctx := context.Background()

	// Create Redis client
	redisClient, err := redis.New(redisConfig)
	if err != nil {
		log.WithError(err).Warn("Failed to connect to Redis, log service will be limited")
		return &LogService{
			redisClient: nil,
			log:         log,
			ctx:         ctx,
		}
	}

	return &LogService{
		redisClient: redisClient,
		log:         log,
		ctx:         ctx,
	}
}

// LoadLogs is a no-op function for backward compatibility
// Logs are now stored in Redis and loaded on-demand
func (s *LogService) LoadLogs() error {
	s.log.Info("LoadLogs is a no-op with Redis-based logging")
	return nil
}

// GetLogs returns logs based on query parameters
func (s *LogService) GetLogs(params LogQueryParams) (LogResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogResponse{
			Total:   0,
			Page:    params.Page,
			PerPage: params.PerPage,
			Logs:    []LogEntry{},
		}, fmt.Errorf("Redis client not available")
	}

	// Get logs from Redis based on parameters
	var logs []redis.LogEntry
	var err error

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get logs based on the most specific filter
	if params.TaskID != "" {
		logs, err = s.redisClient.GetLogsByTaskID(ctx, params.TaskID)
	} else if params.IMEI != "" {
		logs, err = s.redisClient.GetLogsByIMEI(ctx, params.IMEI)
	} else if params.Date != "" {
		// If date is specified, use it as the primary filter
		logs, err = s.redisClient.GetLogsByDate(ctx, params.Date)
	} else if params.WorkerType != "" {
		logs, err = s.redisClient.GetLogsByWorkerType(ctx, params.WorkerType)
	} else if params.Status != "" {
		logs, err = s.redisClient.GetLogsByStatus(ctx, params.Status)
	} else {
		// Get all logs
		logs, err = s.redisClient.GetLogsByPattern(ctx, "logs:*")
	}

	if err != nil {
		s.log.WithError(err).Error("Failed to get logs from Redis")
		return LogResponse{
			Total:   0,
			Page:    params.Page,
			PerPage: params.PerPage,
			Logs:    []LogEntry{},
		}, err
	}

	// Convert redis.LogEntry to api.LogEntry
	apiLogs := make([]LogEntry, len(logs))
	for i, log := range logs {
		apiLogs[i] = LogEntry{
			Timestamp:  log.Timestamp,
			WorkerType: log.WorkerType,
			Status:     log.Status,
			Metadata:   log.Metadata,
		}
	}

	// Apply additional filters
	filteredLogs := s.filterLogs(apiLogs, params)

	// Apply pagination
	start := (params.Page - 1) * params.PerPage
	end := start + params.PerPage

	if start >= len(filteredLogs) {
		// Return empty result if start index is out of range
		return LogResponse{
			Total:   len(filteredLogs),
			Page:    params.Page,
			PerPage: params.PerPage,
			Logs:    []LogEntry{},
		}, nil
	}

	if end > len(filteredLogs) {
		end = len(filteredLogs)
	}

	return LogResponse{
		Total:   len(filteredLogs),
		Page:    params.Page,
		PerPage: params.PerPage,
		Logs:    filteredLogs[start:end],
	}, nil
}

// filterLogs applies additional filters to logs
func (s *LogService) filterLogs(logs []LogEntry, params LogQueryParams) []LogEntry {
	var filtered []LogEntry

	for _, entry := range logs {
		// Apply filters that weren't applied in the Redis query

		// If we queried by worker type, we don't need to filter by it again
		if params.WorkerType != "" && params.WorkerType != entry.WorkerType {
			continue
		}

		// If we queried by IMEI, we don't need to filter by it again
		if params.IMEI != "" && (entry.Metadata == nil || entry.Metadata["imei"] != params.IMEI) {
			continue
		}

		// If we queried by status, we don't need to filter by it again
		if params.Status != "" && params.Status != entry.Status {
			continue
		}

		// If we queried by task ID, we don't need to filter by it again
		if params.TaskID != "" && (entry.Metadata == nil || entry.Metadata["task_id"] != params.TaskID) {
			continue
		}

		// Filter by error message
		if params.Error != "" && (entry.Metadata == nil || entry.Metadata["error"] != params.Error) {
			continue
		}

		// Filter by timestamp
		if params.Timestamp != "" && entry.Timestamp != params.Timestamp {
			continue
		}

		// Filter by date if we didn't already query by date
		if params.Date != "" {
			entryTime, err := time.Parse(time.RFC3339, entry.Timestamp)
			if err != nil {
				// Skip entries with invalid timestamps
				continue
			}

			// Format date as YYYY-MM-DD
			entryDate := entryTime.Format("2006-01-02")

			// Compare with requested date
			if entryDate != params.Date {
				continue
			}
		}

		// Filter by time range
		if !params.FromTime.IsZero() || !params.ToTime.IsZero() {
			entryTime, err := time.Parse(time.RFC3339, entry.Timestamp)
			if err != nil {
				// Skip entries with invalid timestamps
				continue
			}

			if !params.FromTime.IsZero() && entryTime.Before(params.FromTime) {
				continue
			}

			if !params.ToTime.IsZero() && entryTime.After(params.ToTime) {
				continue
			}
		}

		filtered = append(filtered, entry)
	}

	return filtered
}

// ReloadLogs is a no-op function for backward compatibility
func (s *LogService) ReloadLogs() error {
	s.log.Info("ReloadLogs is a no-op with Redis-based logging")
	return nil
}

// GetLogByKey retrieves a specific log by its Redis key
func (s *LogService) GetLogByKey(key string) (LogEntry, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogEntry{}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)
	defer cancel()

	// Get log from Redis
	jsonData, err := s.redisClient.Get(ctx, key)
	if err != nil {
		return LogEntry{}, fmt.Errorf("failed to get log from Redis: %w", err)
	}

	// Unmarshal JSON data
	var entry LogEntry
	if err := json.Unmarshal([]byte(jsonData), &entry); err != nil {
		return LogEntry{}, fmt.Errorf("failed to unmarshal log entry: %w", err)
	}

	return entry, nil
}

// GetLogsByKeyPattern retrieves logs with keys matching a pattern
func (s *LogService) GetLogsByKeyPattern(pattern string) (LogResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get logs from Redis
	logs, err := s.redisClient.GetLogsByPattern(ctx, pattern)
	if err != nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Convert redis.LogEntry to api.LogEntry
	apiLogs := make([]LogEntry, len(logs))
	for i, log := range logs {
		apiLogs[i] = LogEntry{
			Timestamp:  log.Timestamp,
			WorkerType: log.WorkerType,
			Status:     log.Status,
			Metadata:   log.Metadata,
		}
	}

	return LogResponse{
		Total:   len(apiLogs),
		Page:    1,
		PerPage: 100,
		Logs:    apiLogs,
	}, nil
}

// GetLogByDateStatusTaskID retrieves a specific log by date, status, and task ID
func (s *LogService) GetLogByDateStatusTaskID(date, status, taskID string) (LogEntry, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogEntry{}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)
	defer cancel()

	// Create pattern for key
	pattern := fmt.Sprintf("logs:*:%s:*:%s", date, taskID)

	// Get logs matching the pattern
	logs, err := s.redisClient.GetLogsByPattern(ctx, pattern)
	if err != nil {
		return LogEntry{}, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Filter by status
	for _, log := range logs {
		if log.Status == status {
			return LogEntry{
				Timestamp:  log.Timestamp,
				WorkerType: log.WorkerType,
				Status:     log.Status,
				Metadata:   log.Metadata,
			}, nil
		}
	}

	return LogEntry{}, fmt.Errorf("log not found")
}

// SaveLog saves a new log entry to Redis
func (s *LogService) SaveLog(entry LogEntry) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)
	defer cancel()

	// Convert to Redis log entry
	redisEntry := redis.LogEntry{
		Timestamp:  entry.Timestamp,
		WorkerType: entry.WorkerType,
		Status:     entry.Status,
		Metadata:   entry.Metadata,
	}

	// Save log to Redis
	return s.redisClient.SaveLog(ctx, redisEntry)
}

// UpdateLog updates an existing log entry in Redis
func (s *LogService) UpdateLog(key string, entry LogEntry) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)
	defer cancel()

	// Check if log exists
	_, err := s.redisClient.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("log not found: %w", err)
	}

	// Marshal log entry to JSON
	jsonData, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal log entry: %w", err)
	}

	// Update log in Redis
	return s.redisClient.Set(ctx, key, jsonData, 0)
}

// DeleteLog deletes a log entry from Redis
func (s *LogService) DeleteLog(key string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 5*time.Second)
	defer cancel()

	// Delete log from Redis
	return s.redisClient.DeleteKey(ctx, key)
}

// DeleteLogsByPattern deletes log entries matching a pattern from Redis
func (s *LogService) DeleteLogsByPattern(pattern string) (int64, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return 0, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 30*time.Second)
	defer cancel()

	// Delete logs from Redis
	return s.redisClient.DeleteByKeyPattern(ctx, pattern)
}

// SaveLogsBatch saves multiple log entries to Redis in a batch
func (s *LogService) SaveLogsBatch(entries []LogEntry) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 30*time.Second)
	defer cancel()

	// Save each log entry
	for _, entry := range entries {
		// Convert to Redis log entry
		redisEntry := redis.LogEntry{
			Timestamp:  entry.Timestamp,
			WorkerType: entry.WorkerType,
			Status:     entry.Status,
			Metadata:   entry.Metadata,
		}

		// Save log to Redis
		if err := s.redisClient.SaveLog(ctx, redisEntry); err != nil {
			return fmt.Errorf("failed to save log entry: %w", err)
		}
	}

	return nil
}

// GetLogsByPrefix retrieves logs with keys matching a prefix
func (s *LogService) GetLogsByPrefix(prefix string) (LogResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get logs from Redis
	logs, err := s.redisClient.GetLogsByPattern(ctx, prefix)
	if err != nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Convert redis.LogEntry to api.LogEntry
	apiLogs := make([]LogEntry, len(logs))
	for i, log := range logs {
		apiLogs[i] = LogEntry{
			Timestamp:  log.Timestamp,
			WorkerType: log.WorkerType,
			Status:     log.Status,
			Metadata:   log.Metadata,
		}
	}

	return LogResponse{
		Total:   len(apiLogs),
		Page:    1,
		PerPage: 100,
		Logs:    apiLogs,
	}, nil
}

// GetRecentLogs retrieves the most recent logs
func (s *LogService) GetRecentLogs(limit int) (LogResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: limit,
			Logs:    []LogEntry{},
		}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get all logs
	logs, err := s.redisClient.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		s.log.WithError(err).Error("Failed to get logs from Redis")
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: limit,
			Logs:    []LogEntry{},
		}, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Convert redis.LogEntry to api.LogEntry
	apiLogs := make([]LogEntry, len(logs))
	for i, log := range logs {
		apiLogs[i] = LogEntry{
			Timestamp:  log.Timestamp,
			WorkerType: log.WorkerType,
			Status:     log.Status,
			Metadata:   log.Metadata,
		}
	}

	// Sort logs by timestamp (newest first)
	sort.Slice(apiLogs, func(i, j int) bool {
		timeI, errI := time.Parse(time.RFC3339, apiLogs[i].Timestamp)
		timeJ, errJ := time.Parse(time.RFC3339, apiLogs[j].Timestamp)

		// If we can't parse either timestamp, consider them equal
		if errI != nil || errJ != nil {
			return false
		}

		// Sort in descending order (newest first)
		return timeJ.Before(timeI)
	})

	// Apply limit
	if len(apiLogs) > limit {
		apiLogs = apiLogs[:limit]
	}

	return LogResponse{
		Total:   len(apiLogs),
		Page:    1,
		PerPage: limit,
		Logs:    apiLogs,
	}, nil
}

// GetLogsByStatusCode retrieves logs with a specific status code
func (s *LogService) GetLogsByStatusCode(statusCode string) (LogResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get all logs
	logs, err := s.redisClient.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Filter logs by status code
	var filteredLogs []LogEntry
	for _, log := range logs {
		if log.Metadata != nil {
			if sc, ok := log.Metadata["status_code"].(string); ok && sc == statusCode {
				filteredLogs = append(filteredLogs, LogEntry{
					Timestamp:  log.Timestamp,
					WorkerType: log.WorkerType,
					Status:     log.Status,
					Metadata:   log.Metadata,
				})
			} else if sc, ok := log.Metadata["status_code"].(float64); ok && fmt.Sprintf("%.0f", sc) == statusCode {
				filteredLogs = append(filteredLogs, LogEntry{
					Timestamp:  log.Timestamp,
					WorkerType: log.WorkerType,
					Status:     log.Status,
					Metadata:   log.Metadata,
				})
			}
		}
	}

	return LogResponse{
		Total:   len(filteredLogs),
		Page:    1,
		PerPage: 100,
		Logs:    filteredLogs,
	}, nil
}

// GetLogsByURL retrieves logs with a specific URL
func (s *LogService) GetLogsByURL(url string) (LogResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get all logs
	logs, err := s.redisClient.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Filter logs by URL
	var filteredLogs []LogEntry
	for _, log := range logs {
		if log.Metadata != nil {
			if logURL, ok := log.Metadata["url"].(string); ok && logURL == url {
				filteredLogs = append(filteredLogs, LogEntry{
					Timestamp:  log.Timestamp,
					WorkerType: log.WorkerType,
					Status:     log.Status,
					Metadata:   log.Metadata,
				})
			}
		}
	}

	return LogResponse{
		Total:   len(filteredLogs),
		Page:    1,
		PerPage: 100,
		Logs:    filteredLogs,
	}, nil
}

// GetLogStats retrieves statistics about logs
func (s *LogService) GetLogStats() (map[string]interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get all logs
	logs, err := s.redisClient.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return nil, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Calculate statistics
	stats := make(map[string]interface{})

	// Count by status
	statusCount := make(map[string]int)
	for _, log := range logs {
		statusCount[log.Status]++
	}
	stats["status_count"] = statusCount

	// Count by worker type
	workerTypeCount := make(map[string]int)
	for _, log := range logs {
		workerTypeCount[log.WorkerType]++
	}
	stats["worker_type_count"] = workerTypeCount

	// Count by status code
	statusCodeCount := make(map[string]int)
	for _, log := range logs {
		if log.Metadata != nil {
			if sc, ok := log.Metadata["status_code"].(string); ok {
				statusCodeCount[sc]++
			} else if sc, ok := log.Metadata["status_code"].(float64); ok {
				statusCodeCount[fmt.Sprintf("%.0f", sc)]++
			}
		}
	}
	stats["status_code_count"] = statusCodeCount

	// Total logs
	stats["total_logs"] = len(logs)

	return stats, nil
}

// GroupLogsByField groups logs by a specific field
func (s *LogService) GroupLogsByField(field string) (map[string][]LogEntry, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get all logs
	logs, err := s.redisClient.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return nil, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Group logs by field
	grouped := make(map[string][]LogEntry)

	for _, log := range logs {
		var fieldValue string

		// Get field value based on field name
		switch field {
		case "worker_type":
			fieldValue = log.WorkerType
		case "status":
			fieldValue = log.Status
		default:
			// Try to get field from metadata
			if log.Metadata != nil {
				if val, ok := log.Metadata[field].(string); ok {
					fieldValue = val
				} else if val, ok := log.Metadata[field].(float64); ok {
					fieldValue = fmt.Sprintf("%.0f", val)
				} else {
					// Skip if field not found
					continue
				}
			} else {
				// Skip if metadata is nil
				continue
			}
		}

		// Add log to group
		grouped[fieldValue] = append(grouped[fieldValue], LogEntry{
			Timestamp:  log.Timestamp,
			WorkerType: log.WorkerType,
			Status:     log.Status,
			Metadata:   log.Metadata,
		})
	}

	return grouped, nil
}

// CountLogsByStatus counts logs by status for a specific date
func (s *LogService) CountLogsByStatus(date string) (map[string]int, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Create pattern for key
	pattern := fmt.Sprintf("logs:*:%s:*", date)

	// Get logs matching the pattern
	logs, err := s.redisClient.GetLogsByPattern(ctx, pattern)
	if err != nil {
		return nil, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Count logs by status
	statusCount := make(map[string]int)
	for _, log := range logs {
		statusCount[log.Status]++
	}

	return statusCount, nil
}

// GetStatusCodeDistribution gets the distribution of status codes for a specific date
func (s *LogService) GetStatusCodeDistribution(date string) (map[string]int, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Create pattern for key
	pattern := fmt.Sprintf("logs:*:%s:*", date)

	// Get logs matching the pattern
	logs, err := s.redisClient.GetLogsByPattern(ctx, pattern)
	if err != nil {
		return nil, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Count logs by status code
	statusCodeCount := make(map[string]int)
	for _, log := range logs {
		if log.Metadata != nil {
			if sc, ok := log.Metadata["status_code"].(string); ok {
				// Group by first digit (2xx, 4xx, 5xx)
				if len(sc) > 0 {
					group := sc[0:1] + "xx"
					statusCodeCount[group]++
				}
			} else if sc, ok := log.Metadata["status_code"].(float64); ok {
				// Convert to string and group by first digit
				scStr := fmt.Sprintf("%.0f", sc)
				if len(scStr) > 0 {
					group := scStr[0:1] + "xx"
					statusCodeCount[group]++
				}
			}
		}
	}

	return statusCodeCount, nil
}

// GetLogsByDate retrieves logs for a specific date
func (s *LogService) GetLogsByDate(date string) (LogResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Create pattern for key
	pattern := fmt.Sprintf("logs:*:%s:*", date)

	// Get logs matching the pattern
	logs, err := s.redisClient.GetLogsByPattern(ctx, pattern)
	if err != nil {
		return LogResponse{
			Total:   0,
			Page:    1,
			PerPage: 100,
			Logs:    []LogEntry{},
		}, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Convert redis.LogEntry to api.LogEntry
	apiLogs := make([]LogEntry, len(logs))
	for i, log := range logs {
		apiLogs[i] = LogEntry{
			Timestamp:  log.Timestamp,
			WorkerType: log.WorkerType,
			Status:     log.Status,
			Metadata:   log.Metadata,
		}
	}

	return LogResponse{
		Total:   len(apiLogs),
		Page:    1,
		PerPage: 100,
		Logs:    apiLogs,
	}, nil
}

// GetSystemStats retrieves statistics about the system
func (s *LogService) GetSystemStats() (map[string]interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if Redis client is available
	if s.redisClient == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	// Get all logs
	logs, err := s.redisClient.GetLogsByPattern(ctx, "logs:*")
	if err != nil {
		return nil, fmt.Errorf("failed to get logs from Redis: %w", err)
	}

	// Calculate statistics
	stats := make(map[string]interface{})

	// Total logs
	stats["total_logs"] = len(logs)

	// Count by worker type
	workerTypeCount := make(map[string]int)
	for _, log := range logs {
		workerTypeCount[log.WorkerType]++
	}
	stats["worker_types"] = workerTypeCount

	// Count by status
	statusCount := make(map[string]int)
	for _, log := range logs {
		statusCount[log.Status]++
	}
	stats["statuses"] = statusCount

	// Redis info
	redisInfo, err := s.redisClient.Info(ctx)
	if err == nil {
		stats["redis_info"] = redisInfo
	}

	return stats, nil
}
