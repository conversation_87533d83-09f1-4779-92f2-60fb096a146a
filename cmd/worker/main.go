package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/user/workers/api"
	"github.com/user/workers/config"
	"github.com/user/workers/internal/cronjob"
	"github.com/user/workers/internal/dispatcher"
	"github.com/user/workers/internal/queue"
	"github.com/user/workers/internal/worker"
	"github.com/user/workers/pkg/envcheck"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

func main() {
	// Check if all required environment variables exist
	if err := envcheck.EnsureEnvVarsExist(); err != nil {
		fmt.Printf("ERROR: %s\n", err.Error())
		os.Exit(1)
	}

	// Create context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.Error())
	}

	// Print warning for missing environment variables
	envcheck.CheckAndValidateEnvVars()

	// Create logger
	log := logger.New(cfg.LogLevel)

	// Create LogWriter with Redis configuration
	lwConfig := logwriter.LogWriterConfig{
		LogsDir:           "logs",
		RedisHost:         cfg.Redis.Host,
		RedisPort:         cfg.Redis.Port,
		RedisPassword:     cfg.Redis.Password,
		RedisDB:           cfg.Redis.DB,
		LogFormatFilePath: "config/redis_log_format.json",
	}
	lw, err := logwriter.New(lwConfig)
	if err != nil {
		log.WithError(err).Fatal("Failed to create LogWriter")
	}

	// Verify Redis connection
	if !lw.IsRedisAvailable() {
		log.Warn("LogWriter initialized with file-based logging (Redis not available)")
	}

	// Load worker configuration from workers.json
	workersConfig, err := config.LoadWorkersConfig("")
	if err != nil {
		log.WithError(err).Fatal("Failed to load workers configuration")
	}

	// Create RabbitMQ client
	rabbitMQ, err := queue.NewRabbitMQ(cfg, workersConfig, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to create RabbitMQ client")
	}
	defer rabbitMQ.Close()

	// Calculate total worker count from configuration
	totalWorkers := 0
	for _, workerConfig := range workersConfig.GetEnabledWorkers() {
		totalWorkers += workerConfig.Count
	}

	// Create worker pool with size from workers.json
	pool := worker.NewPool(ctx, totalWorkers, log)

	// Convert config to map for workers
	configMap := map[string]interface{}{
		"API": map[string]interface{}{
			"BaseURL": cfg.API.BaseURL,
			"Timeout": cfg.API.Timeout,
		},
		"CaveoAPI": map[string]interface{}{
			"BaseURL":       cfg.CaveoAPI.BaseURL,
			"TokenUsername": cfg.CaveoAPI.TokenUsername,
			"TokenPassword": cfg.CaveoAPI.TokenPassword,
			"Timeout":       cfg.CaveoAPI.Timeout,
		},
		"Minio": map[string]interface{}{
			"Endpoint":  cfg.Minio.Endpoint,
			"AccessKey": cfg.Minio.AccessKey,
			"SecretKey": cfg.Minio.SecretKey,
			"Bucket":    cfg.Minio.Bucket,
			"UseSSL":    cfg.Minio.UseSSL,
		},
		"SourceDB": map[string]interface{}{
			"Host":     cfg.SourceDB.Host,
			"Port":     cfg.SourceDB.Port,
			"User":     cfg.SourceDB.User,
			"Password": cfg.SourceDB.Password,
			"Name":     cfg.SourceDB.Name,
		},
		"TargetDB": map[string]interface{}{
			"Host":     cfg.TargetDB.Host,
			"Port":     cfg.TargetDB.Port,
			"User":     cfg.TargetDB.User,
			"Password": cfg.TargetDB.Password,
			"Name":     cfg.TargetDB.Name,
		},
	}

	// Register workers based on configuration
	enabledWorkers := workersConfig.GetEnabledWorkers()

	// Create and register API workers
	if apiConfig, exists := enabledWorkers["api_pull"]; exists && apiConfig.Enabled {
		// Add bucket from workers.json to configMap
		if configMap["Minio"] != nil {
			minioConfigMap := configMap["Minio"].(map[string]interface{})
			minioConfigMap["Bucket"] = apiConfig.Bucket
		}

		for i := 0; i < apiConfig.Count; i++ {
			apiWorker, err := worker.NewWorker("api_pull", log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create API pull worker")
			}
			apiWorker.SetLogWriter(lw)
			pool.RegisterWorker(apiWorker)
		}
	}

	// Create and register DB workers
	if dbConfig, exists := enabledWorkers["db_clone"]; exists && dbConfig.Enabled {
		for i := 0; i < dbConfig.Count; i++ {
			dbWorker, err := worker.NewWorker(worker.DBWorker, log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create DB cloning worker")
			}
			dbWorker.SetLogWriter(lw)
			pool.RegisterWorker(dbWorker)
		}
	}

	// Create and register Caveo API workers
	if caveoConfig, exists := enabledWorkers["api_pull_caveo"]; exists && caveoConfig.Enabled {
		// Add bucket from workers.json to configMap
		if configMap["CaveoAPI"] != nil {
			caveoAPIConfig := configMap["CaveoAPI"].(map[string]interface{})
			caveoAPIConfig["Bucket"] = caveoConfig.Bucket
		}

		// Add worker configuration to configMap
		configMap["WorkerConfig"] = caveoConfig

		for i := 0; i < caveoConfig.Count; i++ {
			caveoWorker, err := worker.NewWorker(worker.APICaveoWorker, log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create Caveo API pulling worker")
			}
			caveoWorker.SetLogWriter(lw)
			pool.RegisterWorker(caveoWorker)
		}
	}

	// Create and register Caveo Payload Maker workers
	var payloadMakerWorkers []worker.Worker
	if payloadMakerConfig, exists := enabledWorkers["caveo_payload_maker"]; exists && payloadMakerConfig.Enabled {
		for i := 0; i < payloadMakerConfig.Count; i++ {
			payloadMakerWorker, err := worker.NewWorker("caveo_payload_maker", log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create Caveo Payload Maker worker")
			}
			payloadMakerWorker.SetLogWriter(lw)
			pool.RegisterWorker(payloadMakerWorker)
			payloadMakerWorkers = append(payloadMakerWorkers, payloadMakerWorker)
		}
	}

	// Create and register Reschedule Caveo workers
	var rescheduleCaveoWorkers []worker.Worker
	if rescheduleCaveoConfig, exists := enabledWorkers["reschedule_caveo"]; exists && rescheduleCaveoConfig.Enabled {
		for i := 0; i < rescheduleCaveoConfig.Count; i++ {
			rescheduleCaveoWorker, err := worker.NewWorker(worker.RescheduleCaveoWorker, log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create Reschedule Caveo worker")
			}
			rescheduleCaveoWorker.SetLogWriter(lw)
			pool.RegisterWorker(rescheduleCaveoWorker)
			rescheduleCaveoWorkers = append(rescheduleCaveoWorkers, rescheduleCaveoWorker)
		}
	}

	// Create and register Caveo to InfluxDB workers
	if caveoToInfluxConfig, exists := enabledWorkers["caveo_to_influx"]; exists && caveoToInfluxConfig.Enabled {
		// Add bucket from workers.json to configMap
		if configMap["Minio"] != nil {
			minioConfigMap := configMap["Minio"].(map[string]interface{})
			minioConfigMap["Bucket"] = caveoToInfluxConfig.Bucket
		}

		for i := 0; i < caveoToInfluxConfig.Count; i++ {
			caveoToInfluxWorker, err := worker.NewWorker(worker.CaveoToInfluxWorkerType, log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create Caveo to InfluxDB worker")
			}
			caveoToInfluxWorker.SetLogWriter(lw)
			pool.RegisterWorker(caveoToInfluxWorker)
		}
	}

	// Create and register Geofencing Division workers
	if geofDivisionConfig, exists := enabledWorkers["geof_division"]; exists && geofDivisionConfig.Enabled {
		for i := 0; i < geofDivisionConfig.Count; i++ {
			geofDivisionWorker, err := worker.NewWorker(worker.GeofDivisionWorkerType, log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create Geofencing Division worker")
			}
			geofDivisionWorker.SetLogWriter(lw)
			pool.RegisterWorker(geofDivisionWorker)
		}
	}

	// Create and register Geofencing Estate workers
	if geofEstateConfig, exists := enabledWorkers["geof_estate"]; exists && geofEstateConfig.Enabled {
		for i := 0; i < geofEstateConfig.Count; i++ {
			geofEstateWorker, err := worker.NewWorker(worker.GeofEstateWorkerType, log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create Geofencing Estate worker")
			}
			geofEstateWorker.SetLogWriter(lw)
			pool.RegisterWorker(geofEstateWorker)
		}
	}

	// Create and register Geofencing Block workers
	if geofBlockConfig, exists := enabledWorkers["geof_block"]; exists && geofBlockConfig.Enabled {
		for i := 0; i < geofBlockConfig.Count; i++ {
			geofBlockWorker, err := worker.NewWorker(worker.GeofBlockWorkerType, log, configMap)
			if err != nil {
				log.WithError(err).Fatal("Failed to create Geofencing Block worker")
			}
			geofBlockWorker.SetLogWriter(lw)
			pool.RegisterWorker(geofBlockWorker)
		}
	}

	// Start worker pool
	pool.Start()

	// Create Redis client for cronjob manager
	redisClient := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Host + ":" + cfg.Redis.Port,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// Create dispatcher
	disp := dispatcher.NewDispatcher(rabbitMQ, log, cfg, workersConfig)

	// Set dispatcher on caveo_payload_maker workers
	for _, payloadMakerWorker := range payloadMakerWorkers {
		payloadMakerWorker.SetDispatcher(disp)
	}

	// Set dispatcher on reschedule_caveo workers
	for _, rescheduleCaveoWorker := range rescheduleCaveoWorkers {
		rescheduleCaveoWorker.SetDispatcher(disp)
	}

	// Create cronjob storage
	cronjobStorage := cronjob.NewRedisStorage(redisClient, log)

	// Create cronjob manager
	cronjobManager := cronjob.NewManager(cronjobStorage, disp, log)

	// Start cronjob manager
	if err := cronjobManager.Start(); err != nil {
		log.WithError(err).Error("Failed to start cronjob manager")
	}

	// Create consumer
	consumer := queue.NewConsumer(ctx, rabbitMQ, pool, log, cfg, workersConfig)
	if err := consumer.Start(); err != nil {
		log.WithError(err).Fatal("Failed to start consumer")
	}

	// Create API server
	apiServer := api.NewServer(disp, log, cfg)

	// Get port from environment variable or use default
	port := 8080 // Default port
	if portEnv := os.Getenv("PORT"); portEnv != "" {
		if p, err := strconv.Atoi(portEnv); err == nil {
			port = p
		}
	}

	// Start API server in a goroutine with port retry logic
	go func() {
		// Try the initial port
		err := apiServer.Start(port)

		// If the port is in use, try alternative ports
		if err != nil && err != http.ErrServerClosed {
			// Check if the error is "address already in use"
			if strings.Contains(err.Error(), "address already in use") {
				// Try alternative ports (port+1, port+2, etc.)
				for i := 1; i <= 10; i++ {
					alternativePort := port + i
					log.WithField("port", alternativePort).Info("Retrying with alternative port")
					err = apiServer.Start(alternativePort)
					if err == nil || err == http.ErrServerClosed {
						log.WithField("port", alternativePort).Info("API server started successfully")
						return
					}
					if !strings.Contains(err.Error(), "address already in use") {
						break
					}
				}
			}
			log.WithError(err).Fatal("Failed to start API server after multiple attempts")
		} else {
			log.WithField("port", port).Info("API server started successfully")
		}
	}()

	// Wait for termination signal
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGINT, syscall.SIGTERM)
	<-sig

	// Graceful shutdown
	// First, shut down the API server
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()
	if err := apiServer.Shutdown(shutdownCtx); err != nil {
		log.WithError(err).Error("Failed to shut down API server gracefully")
	}

	// Stop the cronjob manager
	cronjobManager.Stop()

	// Then, stop accepting new tasks
	consumer.Shutdown()

	// Finally, wait for all workers to finish
	pool.Shutdown()

	log.Info("Application shutdown complete")
}
