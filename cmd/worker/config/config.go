package config

import (
	"fmt"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	LogLevel       string `mapstructure:"LOG_LEVEL"`
	WorkerPoolSize int    `mapstructure:"WORKER_POOL_SIZE"`

	// RabbitMQ configuration
	RabbitMQ struct {
		Host              string `mapstructure:"RABBITMQ_HOST"`
		Port              string `mapstructure:"RABBITMQ_PORT"`
		ManagementPort    string `mapstructure:"RABBITMQ_PORT_MANAGEMENT"`
		User              string `mapstructure:"RABBITMQ_USER"`
		Password          string `mapstructure:"RABBITMQ_PASSWORD"`
		APIPullQueue      string `mapstructure:"RABBITMQ_API_PULL_QUEUE"`
		APICaveoPullQueue string `mapstructure:"RABBITMQ_API_PULL_CAVEO_QUEUE"`
		DBCloneQueue      string `mapstructure:"RABBITMQ_DB_CLONE_QUEUE"`
	}

	// Redis configuration
	Redis struct {
		Host     string `mapstructure:"REDIS_HOST"`
		Port     string `mapstructure:"REDIS_PORT"`
		Password string `mapstructure:"REDIS_PASSWORD"`
		DB       int    `mapstructure:"REDIS_DB"`
	}

	// API configuration for API pulling worker
	API struct {
		BaseURL string `mapstructure:"API_BASE_URL"`
		Timeout int    `mapstructure:"API_TIMEOUT"`
	}

	// Caveo API configuration
	CaveoAPI struct {
		BaseURL       string `mapstructure:"CAVEO_API_BASE_URL"`
		TokenUsername string `mapstructure:"CAVEO_API_TOKEN_USERNAME"`
		TokenPassword string `mapstructure:"CAVEO_API_TOKEN_PASSWORD"`
		Timeout       int    `mapstructure:"CAVEO_API_TIMEOUT"`
	}

	// Minio configuration
	Minio struct {
		Endpoint  string `mapstructure:"MINIO_ENDPOINT"`
		AccessKey string `mapstructure:"MINIO_ACCESS_KEY"`
		SecretKey string `mapstructure:"MINIO_SECRET_KEY"`
		Bucket    string `mapstructure:"MINIO_BUCKET"`
		UseSSL    bool   `mapstructure:"MINIO_USE_SSL"`
	}

	// Database configuration for DB cloning worker
	SourceDB struct {
		Host     string `mapstructure:"SOURCE_DB_HOST"`
		Port     string `mapstructure:"SOURCE_DB_PORT"`
		User     string `mapstructure:"SOURCE_DB_USER"`
		Password string `mapstructure:"SOURCE_DB_PASSWORD"`
		Name     string `mapstructure:"SOURCE_DB_NAME"`
	}

	TargetDB struct {
		Host     string `mapstructure:"TARGET_DB_HOST"`
		Port     string `mapstructure:"TARGET_DB_PORT"`
		User     string `mapstructure:"TARGET_DB_USER"`
		Password string `mapstructure:"TARGET_DB_PASSWORD"`
		Name     string `mapstructure:"TARGET_DB_NAME"`
	}
}

// Load loads configuration from environment variables and .env file
func Load() (*Config, error) {
	// Configure viper to read from .env file
	viper.SetConfigFile(".env")

	// Read config file if it exists
	if err := viper.ReadInConfig(); err != nil {
		// Print warning but continue with environment variables and defaults
		fmt.Printf("Warning: Failed to read config file: %v\n", err)
	}

	// Configure viper to read from environment variables
	viper.AutomaticEnv()

	// Create config struct
	config := &Config{
		LogLevel:       viper.GetString("LOG_LEVEL"),
		WorkerPoolSize: viper.GetInt("WORKER_POOL_SIZE"),
	}

	// Set RabbitMQ config
	config.RabbitMQ.Host = viper.GetString("RABBITMQ_HOST")
	config.RabbitMQ.Port = viper.GetString("RABBITMQ_PORT")
	config.RabbitMQ.ManagementPort = viper.GetString("RABBITMQ_PORT_MANAGEMENT")
	config.RabbitMQ.User = viper.GetString("RABBITMQ_USER")
	config.RabbitMQ.Password = viper.GetString("RABBITMQ_PASSWORD")
	config.RabbitMQ.APIPullQueue = viper.GetString("RABBITMQ_API_PULL_QUEUE")
	config.RabbitMQ.APICaveoPullQueue = viper.GetString("RABBITMQ_API_CAVEO_PULL_QUEUE")
	config.RabbitMQ.DBCloneQueue = viper.GetString("RABBITMQ_DB_CLONE_QUEUE")

	// Set Redis config
	config.Redis.Host = viper.GetString("REDIS_HOST")
	config.Redis.Port = viper.GetString("REDIS_PORT")
	config.Redis.Password = viper.GetString("REDIS_PASSWORD")
	config.Redis.DB = viper.GetInt("REDIS_DB")

	// Set API config
	config.API.BaseURL = viper.GetString("API_BASE_URL")
	config.API.Timeout = viper.GetInt("API_TIMEOUT")

	// Set Caveo API config
	config.CaveoAPI.BaseURL = viper.GetString("CAVEO_API_BASE_URL")
	config.CaveoAPI.TokenUsername = viper.GetString("CAVEO_API_TOKEN_USERNAME")
	config.CaveoAPI.TokenPassword = viper.GetString("CAVEO_API_TOKEN_PASSWORD")
	config.CaveoAPI.Timeout = viper.GetInt("CAVEO_API_TIMEOUT")

	// Set Minio config
	config.Minio.Endpoint = viper.GetString("MINIO_ENDPOINT")
	config.Minio.AccessKey = viper.GetString("MINIO_ACCESS_KEY")
	config.Minio.SecretKey = viper.GetString("MINIO_SECRET_KEY")
	config.Minio.UseSSL = viper.GetBool("MINIO_USE_SSL")

	// Set Source DB config
	config.SourceDB.Host = viper.GetString("SOURCE_DB_HOST")
	config.SourceDB.Port = viper.GetString("SOURCE_DB_PORT")
	config.SourceDB.User = viper.GetString("SOURCE_DB_USER")
	config.SourceDB.Password = viper.GetString("SOURCE_DB_PASSWORD")
	config.SourceDB.Name = viper.GetString("SOURCE_DB_NAME")

	// Set Target DB config
	config.TargetDB.Host = viper.GetString("TARGET_DB_HOST")
	config.TargetDB.Port = viper.GetString("TARGET_DB_PORT")
	config.TargetDB.User = viper.GetString("TARGET_DB_USER")
	config.TargetDB.Password = viper.GetString("TARGET_DB_PASSWORD")
	config.TargetDB.Name = viper.GetString("TARGET_DB_NAME")

	return config, nil
}
