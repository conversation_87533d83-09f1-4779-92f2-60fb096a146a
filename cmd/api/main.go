package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/user/workers/api"
	"github.com/user/workers/config"
	"github.com/user/workers/internal/dispatcher"
	"github.com/user/workers/internal/queue"
	"github.com/user/workers/pkg/logger"
)

func main() {
	// Create context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.Error())
	}

	// Create logger
	log := logger.New(cfg.LogLevel)
	log.Info("Starting API server")

	// Load worker configuration from workers.json
	workersConfig, err := config.LoadWorkersConfig("")
	if err != nil {
		log.WithError(err).Fatal("Failed to load workers configuration")
	}

	// Create RabbitMQ client
	rabbitMQ, err := queue.NewRabbitMQ(cfg, workersConfig, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to create RabbitMQ client")
	}
	defer rabbitMQ.Close()

	// Create dispatcher
	disp := dispatcher.NewDispatcher(rabbitMQ, log, cfg, workersConfig)

	// Create API server
	server := api.NewServer(disp, log, cfg)

	// Start API server in a goroutine
	go func() {
		if err := server.Start(8080); err != nil {
			log.WithError(err).Fatal("Failed to start API server")
		}
	}()

	log.Info("API server started on port 8080")

	// Wait for interrupt signal to gracefully shut down the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Shutting down...")

	// Create a deadline to wait for
	ctx, cancel = context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		log.WithError(err).Fatal("Server shutdown failed")
	}

	log.Info("Server gracefully stopped")
}
