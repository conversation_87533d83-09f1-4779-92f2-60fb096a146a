package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/user/workers/api"
	"github.com/user/workers/config"
	"github.com/user/workers/internal/dispatcher"
	"github.com/user/workers/internal/queue"
	"github.com/user/workers/internal/worker"
	"github.com/user/workers/pkg/logger"
	"github.com/user/workers/pkg/logwriter"
)

func main() {
	// Create context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.Error())
	}

	// Create logger
	log := logger.New(cfg.LogLevel)
	log.Info("Starting worker application with pooling configuration")

	// Load worker pooling configuration
	workersConfig, err := config.LoadWorkersConfig("")
	if err != nil {
		log.WithError(err).Fatal("Failed to load workers configuration")
	}

	// Create LogWriter with Redis configuration
	lwConfig := logwriter.LogWriterConfig{
		LogsDir:       "logs",
		RedisHost:     cfg.Redis.Host,
		RedisPort:     cfg.Redis.Port,
		RedisPassword: cfg.Redis.Password,
		RedisDB:       cfg.Redis.DB,
	}
	lw, err := logwriter.New(lwConfig)
	if err != nil {
		log.WithError(err).Fatal("Failed to create LogWriter")
	}

	// Verify Redis connection
	if lw.IsRedisAvailable() {
		log.Info("LogWriter initialized with Redis support")
	} else {
		log.Warn("LogWriter initialized with file-based logging (Redis not available)")
	}

	// Create RabbitMQ client
	rabbitMQ, err := queue.NewRabbitMQ(cfg, workersConfig, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to create RabbitMQ client")
	}
	defer rabbitMQ.Close()

	// Convert config to map for workers
	configMap := map[string]interface{}{
		"API": map[string]interface{}{
			"BaseURL": cfg.API.BaseURL,
			"Timeout": cfg.API.Timeout,
		},
		"CaveoAPI": map[string]interface{}{
			"BaseURL":       cfg.CaveoAPI.BaseURL,
			"TokenUsername": cfg.CaveoAPI.TokenUsername,
			"TokenPassword": cfg.CaveoAPI.TokenPassword,
			"Timeout":       cfg.CaveoAPI.Timeout,
		},
		"Minio": map[string]interface{}{
			"Endpoint":  cfg.Minio.Endpoint,
			"AccessKey": cfg.Minio.AccessKey,
			"SecretKey": cfg.Minio.SecretKey,
			"Bucket":    cfg.Minio.Bucket,
			"UseSSL":    cfg.Minio.UseSSL,
		},
		"SourceDB": map[string]interface{}{
			"Host":     cfg.SourceDB.Host,
			"Port":     cfg.SourceDB.Port,
			"User":     cfg.SourceDB.User,
			"Password": cfg.SourceDB.Password,
			"Name":     cfg.SourceDB.Name,
		},
		"TargetDB": map[string]interface{}{
			"Host":     cfg.TargetDB.Host,
			"Port":     cfg.TargetDB.Port,
			"User":     cfg.TargetDB.User,
			"Password": cfg.TargetDB.Password,
			"Name":     cfg.TargetDB.Name,
		},
	}

	// Create worker pool manager
	poolManager := worker.NewPoolManager(ctx, log, workersConfig, configMap, lw)

	// Start worker pool manager
	if err := poolManager.Start(); err != nil {
		log.WithError(err).Fatal("Failed to start worker pool manager")
	}

	// Create consumer
	consumer := queue.NewConsumer(ctx, rabbitMQ, poolManager.GetPool(), log, cfg, workersConfig)
	if err := consumer.Start(); err != nil {
		log.WithError(err).Fatal("Failed to start consumer")
	}

	// Create dispatcher
	disp := dispatcher.NewDispatcher(rabbitMQ, log, cfg, workersConfig)

	// Create API server
	apiServer := api.NewServer(disp, log, cfg)

	// Get port from environment variable or use default
	port := 8080 // Default port
	if portEnv := os.Getenv("PORT"); portEnv != "" {
		if p, err := strconv.Atoi(portEnv); err == nil {
			port = p
		}
	}

	// Start API server in a goroutine with port retry logic
	go func() {
		// Try the initial port
		log.WithField("port", port).Info("Starting API server")
		err := apiServer.Start(port)
		if err != nil && err != http.ErrServerClosed {
			log.WithError(err).Error("API server failed to start")
		}
	}()

	// Wait for termination signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Shutdown signal received")

	// Create a context with timeout for shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Shutdown API server
	if err := apiServer.Shutdown(shutdownCtx); err != nil {
		log.WithError(err).Error("API server shutdown error")
	}

	// Shutdown consumer
	consumer.Shutdown()

	// Shutdown worker pool manager
	poolManager.Shutdown()

	log.Info("Shutdown complete")
}
