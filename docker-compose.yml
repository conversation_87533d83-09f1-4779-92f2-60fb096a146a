version: "3"

services:
  rabbitmq:
    image: rabbitmq:3-management
    container_name: smartstep-broker
    ports:
      - "${RABBITMQ_PORT:-5672}:5672" # AMQP port
      - "${RABBITMQ_PORT_MANAGEMENT:-15672}:15672" # Management UI port
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-password}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO for object storage
  minio:
    image: minio/minio:latest
    container_name: smartstep-storage
    restart: unless-stopped
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - ./data/minio:/data:Z
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY:-minioadmin}
    command: server /data --console-address ":9001"
    networks:
      - smartstep-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis for structured log storage
  redis:
    image: redis/redis-stack:latest
    container_name: smartstep-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
      - "${REDIS_INSIGHT_PORT:-8001}:8001"
    volumes:
      - ./data/redis:/data:Z
    environment:
      - REDIS_ARGS=--requirepass ${REDIS_PASSWORD:-your_secure_password} --maxmemory ${REDIS_MAX_MEMORY:-512mb} --maxmemory-policy noeviction
    networks:
      - smartstep-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-your_secure_password}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: smartstep-workers
    depends_on:
      rabbitmq:
        condition: service_healthy
    environment:
      - RABBITMQ_HOST=${RABBITMQ_HOST:-localhost}
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-admin}
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-password}
      - RABBITMQ_QUEUE=${RABBITMQ_QUEUE:-tasks}
      - RABBITMQ_API_PULL_CAVEO_QUEUE=${RABBITMQ_API_PULL_CAVEO_QUEUE:-api-pull-caveo}
      - LOG_LEVEL=${LOG_LEVEL:-debug}
      - WORKER_POOL_SIZE=${WORKER_POOL_SIZE:-5}
    volumes:
      - ./.env:/app/.env

  influxdb:
    image: influxdb:2
    container_name: smartstep-influxdb
    ports:
      - ${INFLUX_PORT}:8086
    volumes:
      - ./data/influxdb2:/var/lib/influxdb2
    environment:
      - INFLUXDB_ADMIN_USER=${INFLUX_ADMIN_USER}
      - INFLUXDB_ADMIN_PASSWORD=${INFLUX_ADMIN_PASSWORD}
    networks:
      - smartstep-network

networks:
  smartstep-network:
    driver: bridge

volumes:
  rabbitmq_data:
  redis_data:
