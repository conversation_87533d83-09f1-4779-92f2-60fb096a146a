# Cronjob Feature Documentation

The cronjob feature allows you to schedule tasks to be executed automatically at specified intervals. This document provides information on how to use the cronjob feature.

## Overview

The cronjob feature uses the cron syntax to schedule tasks. Tasks are stored in Redis and executed by the cronjob manager. When a task is executed, it is dispatched to the appropriate worker through the dispatcher.

## Cronjob Configuration

A cronjob configuration consists of the following fields:

- `name`: A unique name for the cronjob
- `description`: A description of the cronjob
- `enabled`: Whether the cronjob is enabled
- `schedule`: The cron expression for the schedule
- `worker_type`: The type of worker to execute the task
- `payload`: The payload to send to the worker
- `variables`: Variables to be processed in the payload
- `retry_policy`: The retry policy for failed tasks
- `timeout`: The timeout for the task in seconds

## Cron Syntax

The cron syntax is a string of six fields separated by spaces (includes seconds):

```
┌───────────── second (0 - 59)
│ ┌───────────── minute (0 - 59)
│ │ ┌───────────── hour (0 - 23)
│ │ │ ┌───────────── day of the month (1 - 31)
│ │ │ │ ┌───────────── month (1 - 12)
│ │ │ │ │ ┌───────────── day of the week (0 - 6) (Sunday to Saturday)
│ │ │ │ │ │
│ │ │ │ │ │
* * * * * *
```

Examples:

- `0 0 0 * * *`: Daily at midnight
- `0 0 * * * *`: Hourly at minute 0
- `0 */15 * * * *`: Every 15 minutes
- `0 0 0 * * 0`: Weekly on Sunday at midnight
- `0 0 0 1 * *`: Monthly on the 1st at midnight
- `0 0 0 1 1 *`: Yearly on January 1st at midnight
- `*/15 * * * * *`: Every 15 seconds
- `0 */5 * * * *`: Every 5 minutes
- `30 0 9-17 * * 1-5`: At 30 seconds past the minute, every hour from 9 AM to 5 PM, Monday through Friday

## API Endpoints

The following API endpoints are available for managing cronjobs:

### List Cronjobs

```
GET /api/cronjobs
```

Returns a list of all cronjobs.

### Get Cronjob

```
GET /api/cronjobs/{name}
```

Returns a specific cronjob by name.

### Create Cronjob

```
POST /api/cronjobs
```

Creates a new cronjob.

Example request body:

```json
{
  "name": "daily_api_pull",
  "description": "Pull data from API daily at midnight",
  "enabled": true,
  "schedule": "0 0 * * *",
  "worker_type": "api_pull",
  "payload": {
    "endpoint": "https://api.example.com/data",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer token123"
    },
    "params": {
      "date": "${YESTERDAY_DATE}"
    }
  },
  "variables": {
    "YESTERDAY_DATE": "function() { return moment().subtract(1, 'day').format('YYYY-MM-DD'); }"
  },
  "retry_policy": {
    "attempts": 3,
    "delay": 300
  },
  "timeout": 600
}
```

### Update Cronjob

```
PUT /api/cronjobs/{name}
```

Updates an existing cronjob.

### Delete Cronjob

```
DELETE /api/cronjobs/{name}
```

Deletes a cronjob.

### Enable Cronjob

```
POST /api/cronjobs/{name}/enable
```

Enables a cronjob.

### Disable Cronjob

```
POST /api/cronjobs/{name}/disable
```

Disables a cronjob.

### Execute Cronjob Now

```
POST /api/cronjobs/{name}/execute
```

Executes a cronjob immediately.

## Example Payloads

### API Pull Worker

```json
{
  "name": "daily_api_pull",
  "description": "Pull data from API daily at midnight",
  "enabled": true,
  "schedule": "0 0 * * *",
  "worker_type": "api_pull",
  "payload": {
    "endpoint": "https://api.example.com/data",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer ${AUTH_TOKEN}"
    },
    "params": {
      "date": "${YESTERDAY_DATE}"
    }
  },
  "variables": {
    "AUTH_TOKEN": "function() { return 'token123'; }",
    "YESTERDAY_DATE": "function() { return moment().subtract(1, 'day').format('YYYY-MM-DD'); }"
  },
  "retry_policy": {
    "attempts": 3,
    "delay": 300
  },
  "timeout": 600
}
```

### API Pull Caveo Worker

```json
{
  "name": "hourly_caveo_pull",
  "description": "Pull Caveo data hourly",
  "enabled": true,
  "schedule": "0 * * * *",
  "worker_type": "api_pull_caveo",
  "payload": {
    "config": {
      "device": {
        "psm": "PSM 2",
        "estate": "SMSE",
        "division": "DIVISI 1",
        "ident": "868738070028697",
        "nik": "SMSE-07002",
        "name": "HOLIDIN",
        "type": "Pemanen"
      },
      "timeStart": "${HOUR_START}",
      "timeEnd": "${HOUR_END}"
    }
  },
  "variables": {
    "HOUR_START": "function() { return moment().subtract(1, 'hour').format('YYYY-MM-DD HH:00:00'); }",
    "HOUR_END": "function() { return moment().format('YYYY-MM-DD HH:00:00'); }"
  },
  "retry_policy": {
    "attempts": 3,
    "delay": 300
  },
  "timeout": 600
}
```

### DB Clone Worker

```json
{
  "name": "weekly_db_clone",
  "description": "Clone database tables weekly",
  "enabled": true,
  "schedule": "0 0 * * 0",
  "worker_type": "db_clone",
  "payload": {
    "table": "users",
    "columns": ["id", "name", "email", "created_at", "updated_at"],
    "conditions": "updated_at > '${LAST_WEEK_DATE}'",
    "batch_size": 1000
  },
  "variables": {
    "LAST_WEEK_DATE": "function() { return moment().subtract(7, 'days').format('YYYY-MM-DD'); }"
  },
  "retry_policy": {
    "attempts": 3,
    "delay": 300
  },
  "timeout": 1800
}
```
